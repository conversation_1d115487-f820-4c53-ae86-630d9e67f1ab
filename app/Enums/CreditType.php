<?php

namespace App\Enums;

abstract class CreditType
{
    const PLAN_PURCHASE = 1;
    const WATCH_VIDEO = 2;
    const MAGIC_METHOD = 3;
    const MARKETPLACE_ORDER = 4;
    const REFERRAL_CREDIT = 5;
    const PRODUCT_TRANSLATION = 6;
    const CHEAPEST_PRICE_CALCULATION = 7;
    const WEBSHOP_ANALYSIS = 8;
    const PRODUCT_ANALYSIS = 9;
    const TRIAL_CREDIT = 10;
    const BENACHMARK_SEARCH = 11;
    const CREDIT_RESET = 12;

    const CREDIT_ADD = 1;
    const CREDIT_REMOVE = -1;

    const LABELS = [
      self::PLAN_PURCHASE => 'import_plan_purchase',
      self::WATCH_VIDEO => 'watch_video',
      self::BENACHMARK_SEARCH => 'user_use_benachmark_search',
    ];
    const MESSAGE = [
      self::PLAN_PURCHASE => 'import_plan_purchase',
      self::WATCH_VIDEO => 'watch_video',
      self::BEN<PERSON>HMARK_SEARCH => 'User Use Benachmark Search',
    ];
}

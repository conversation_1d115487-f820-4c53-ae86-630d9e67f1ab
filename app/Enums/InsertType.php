<?php

namespace App\Enums;

abstract class InsertType
{
    const API = 1;
    const VOD = 2;
    const STRIPE = 3;
    const CHARGE = 4;
    const IMPORT = 5;
    const MANUAL = 6;
    const MARKETPLACE = 7;
    const MARKETPLACE_SELL = 8;
    const MP_INTERNEL_MANUAL = 17;

    const APP = 9;
    const IMPORT_PLAN = 10;
    const DT_TEMPLATE = 11;
    const DT_LICENSE = 12;
    const TRANSLATE = 13;
    const APPOINTMENT = 14;
    const QUIZ = 15;
    const MP_CATEGORY = 16;

    const TAX_21 = [
        self::MARKETPLACE_SELL,
        self::APP,
        // self::IMPORT_PLAN,
        self::DT_TEMPLATE,
        self::DT_LICENSE,
        // self::TRANSLATE,
        // self::APPOINTMENT,
        // self::TRANSLATE,
        // self::QUIZ,
        // self::MP_CATEGORY,
    ];

    const TAX_21_TYPES = [
        'app',
        // 'import',
        'dt', 
        'dtlicense',
        // 'mp_category',
    ];
}

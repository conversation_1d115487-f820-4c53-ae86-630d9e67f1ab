<?php

namespace App\Helper;

use GuzzleHttp\Client;
use Illuminate\Support\Str;
use DB;
use Illuminate\Support\Collection;
use App\Notifications\DRMNotification;
use App\User;


class GambioApi
{
    public $products_id, $auth, $base_url, $shop_details, $lang, $shop_id, $user_id;
    public static $userId;

    function __construct($products_id, $shop_details, $lang, $user_id)
    { 
        $user = $shop_details->username;
        $pass = $shop_details->password;
        $api_path = "api.php/v2/";
        $this->base_url = $shop_details->url;
        $this->auth = base64_encode("$user:$pass");
        $this->shop_details = $shop_details;
        $this->shop_id = $shop_details->id;
        $this->products_id = $products_id;
        $this->lang = $lang;
        $this->user_id = $user_id;
        self::$userId = $user_id;
    }



    public function ImportToGambioByProductIdBeta()
    {
        // dd("blocked");
        if (count((array) $this->products_id) > 0) {

            $all_products_new = array();
            $all_products_old = array();

            $table = "drm_translation_" .$this->lang;

            foreach ($this->products_id as $i => $id) {

                $product = DB::table($table)
                                ->join('drm_products', 'drm_products.id', '=', $table . '.product_id')
                                ->where('drm_products.user_id', $this->user_id)
                                ->whereNull('drm_products.deleted_at')
                                ->select('drm_products.*', 'drm_products.description as descriptionProduct', 'drm_products.name as product_category_nameroduct', 'drm_products.id as product_id', $table . '.description', $table . '.title as name')
                                ->where('drm_products.id', $id)->first();

                $category = DB::table('drm_products')->join('drm_product_categories', 'drm_product_categories.product_id', '=', 'drm_products.id')
                                ->join('drm_category', 'drm_category.id', '=', 'drm_product_categories.category_id')
                                ->select('drm_category.*', 'drm_products.ean')
                                ->where('drm_products.id', $id)->get();

                
                if((int)$product->ean == 0){
                    continue;
                }
                
                $product_price = DB::table('drm_product_shop_prices')->where(['drm_product_id' => $id, 'shop_id' => $this->shop_id])->first();

                $variables_tag = ["#number", "#ean", "#ek_price", "#vk_price", "#weight", "#size", "#color", "#note", "#production_year", "#brand", "#materials", "#tags","#status", "#gender"];

                foreach ($variables_tag as $key => $variables_tag_value) {
                   if($variables_tag_value == "#number"){$variables_tag_info[$key] = $product->item_number;}
                   if($variables_tag_value == "#ean"){$variables_tag_info[$key] = $product->ean;}
                   if($variables_tag_value == "#ek_price"){$variables_tag_info[$key] = $product->ek_price;}
                   if($variables_tag_value == "#vk_price"){$variables_tag_info[$key] = $product->vk_price;}
                   if($variables_tag_value == "#weight"){$variables_tag_info[$key] = $product->item_weight;}
                   if($variables_tag_value == "#size"){$variables_tag_info[$key] = $product->item_size;}
                   if($variables_tag_value == "#color"){$variables_tag_info[$key] = $product->item_color;}
                   if($variables_tag_value == "#note"){$variables_tag_info[$key] = $product->note;}
                   if($variables_tag_value == "#production_year"){$variables_tag_info[$key] = $product->production_year;}
                   if($variables_tag_value == "#brand"){$variables_tag_info[$key] = $product->brand;}
                   if($variables_tag_value == "#materials"){$variables_tag_info[$key] = $product->materials;}
                   if($variables_tag_value == "#tags"){$variables_tag_info[$key] = $product->tags;}
                   if($variables_tag_value == "#status"){$variables_tag_info[$key] = $product->status;}
                   if($variables_tag_value == "#gender"){$variables_tag_info[$key] = $product->gender;}
                }

                // $this->productCategoriesProcess($category);
               
                $category_ids[$product->ean] = $this->getGambioCategory($category);
            

                $img = $this->makeImageArray($product->image, $product);
                $data['pid'] = $product->product_id;
                $data['quantity'] = (int)$product->stock;
                $data['ean'] = $product->ean;
                $data['weight'] = 0; 
                $data['shippingCosts'] = 0;
                $data['taxClassId'] = 1;
                $data['productModel'] = $product->item_number;
                $data['price'] = $product_price->vk_price??$product->vk_price;

                // $data['color'] = $product->item_color;
                // $data['size'] = $product->item_size;
                // $data['materials'] = $product->materials;
                // $data['production_year'] = $product->production_year;

                $default_languge_shortcode = $this->lang;

                $namearray[$default_languge_shortcode] = trim(str_replace($variables_tag, $variables_tag_info, $product->name));
                $metaarray[$default_languge_shortcode] = trim(str_replace($variables_tag, $variables_tag_info, $product->name));
                $metadesarray[$default_languge_shortcode] = trim(str_replace($variables_tag, $variables_tag_info, $product->name));
                $metaKeywordsarray[$default_languge_shortcode] = trim(str_replace($variables_tag, $variables_tag_info, $product->name));
                $descriptionarray[$default_languge_shortcode] = (isset($product->description)) ? trim(str_replace($variables_tag, $variables_tag_info, $product->description)) : "";
                $checkoutinfoarray[$default_languge_shortcode] = "";
                $viewcountarray[$default_languge_shortcode] = 0;
                $shortdescriptionarray[$default_languge_shortcode] = "";

                $data['name'] = $namearray;
                // $data['metaTitle'] = ['de' => '', 'en' => ''];//$metaarray;
                // $data['metaDescription'] = ['de' => '', 'en' => ''];//$metadesarray;
                $data['description'] = $descriptionarray;
                // $data['metaKeywords'] = ['de' => '', 'en' => ''];//$metaKeywordsarray;
                $data['checkoutInformation'] = $checkoutinfoarray;
                $data['shortDescription'] = $shortdescriptionarray;
                // $data['url'] = ['de' => '', 'en' => ''];//$namearray;
                $data['viewedCount'] = $viewcountarray;
                // $data['keywords'] = ['de' => '', 'en' => ''];//$namearray;
                // $data['urlKeywords'] = ['de' => '', 'en' => ''];//$namearray;
                $data['images'] = $img;
                $data['isActive'] = true;
                $data['settings'] = array("detailsTemplate" => "standard.html", "minOrder" => "1");

                // Product Manufacturers
                if($product->brand){

                    $manufacturers= $this->gambioSearchManufacturers($product->brand);

                    if (empty($manufacturers) && is_array($manufacturers)) {

                        sleep(1);
                        $manufacturers = $this->gambioSearchManufacturers($product->brand);

                        if (empty($manufacturers)) {

                           $this->postProductManufacturers(trim($product->brand));

                        }

                    }

                    $manufacturers = $this->gambioSearchManufacturers($product->brand);
                    if (!empty($manufacturers) && is_array($manufacturers) && isset($manufacturers[0]['name']) && trim($manufacturers[0]['name']) == trim($product->brand)) {

                        $manufacturerId = $manufacturers[0]['id'];
                    }

                }

                $data['manufacturerId'] = (int)$manufacturerId;

                // Product Manufacturers

                $test = null;

                $collect = collect($test);
                // $exists = self::gambioSearchProduct($this->shop_details, $data['ean']);

                // $gambio_product_exists = DB::table('gambio_products')->where('ean',$product->ean)->where('shop_id',$this->shop_id)->first();

                $exists_db = DB::table('gambio_products')->where('ean',$product->ean)->where('shop_id',$this->shop_id)->first();
                // dd($exists_db);
                if($exists_db){
                    sleep(1);
                    $exists = self::gambioSearchProduct($this->shop_details, $product->ean);
                }
               
                if ($exists) {
                    $exists_product_id = $exists;
                    $data['id'] = (int)$exists_product_id;

                    if (!empty($category_ids)) {
                        $getProductLink = $this->getProductLink((int)$exists_product_id);
                        $array_getProductLink = (array)json_decode($getProductLink, true);
                        $getlinkCategory = ($getProductLink)? array_filter($array_getProductLink) : [];

                        foreach ((array)$getlinkCategory as $getlinkCategoryvalue) {
                            $this->deleteProductLink($exists_product_id, $getlinkCategoryvalue);
                        }

                        foreach ((array)$category_ids[$data['ean']] as $category_id_value) {
                            $this->postProductLink($exists_product_id, $category_id_value);
                        }
                    }

                    $all_products_old[] = $data;

                } else {
                    $all_products_new[] = $data;
                }


            }

            if (count((array) $all_products_new) > 0) {
                $this->createNewProduct($all_products_new, $category_ids);
            }
            if (count((array) $all_products_old) > 0){
                $this->updateProduct($all_products_old);
            }

        }
    }


    public static function doCulrRequest($url, $curl_header, $alldata = null, $request_type = 'POST')
    {
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_HEADER => 0,
            CURLOPT_CUSTOMREQUEST => $request_type,
            CURLOPT_HTTPHEADER => $curl_header,
            CURLOPT_POSTFIELDS => $alldata,
            CURLOPT_FOLLOWLOCATION => true
        ));

        $output = curl_exec($ch);
        $info = curl_getinfo($ch);
        // if(!empty(self::$userId) && json_decode($output)->status == 'error'){
        //     User::find(self::$userId)->notify(new DRMNotification('Gambio Export '. json_decode($output)->message . $output, '', '#'));
        // }

        return $output;
    }



    /* Gambio Product Create process */

    public function createNewProduct($productsarray, $category_ids)
    {
        $api_path = "api.php/v2/";
        $user = $this->shop_details->username;
        $pass = $this->shop_details->password;
        $auth = base64_encode("$user:$pass");
        $url = $this->shop_details->url . $api_path . "products";
        $curl_header = array("accept: application/json", "authorization: Basic " . $auth, "content-type: application/json");
        $alldata = json_encode($productsarray);
        $insertProducts = $this->doCulrRequest($url, $curl_header, $alldata, 'POST');
        $this->doAfterProductCreate($insertProducts, $category_ids);
    }

    public function doAfterProductCreate($insertProducts, $category_ids)
    {
        $all_response_array = json_decode($insertProducts);
        if (count((array) $all_response_array->created) > 0) {
            $this->handleCreateSuccess($all_response_array->created, $category_ids);
        }
        // if (count((array) $all_response_array->error) > 0 && $all_response_array->status == 'error') {
        //     User::find($this->user_id)->notify(new DRMNotification('Gambio Export '. $all_response_array->message , '', '#'));
        // }
    }

    public function handleCreateSuccess($success_products, $category_ids)
    {
        $success_product_ean = collect($success_products)->pluck('ean')->toArray();
        $info = [];

        foreach ($success_products as $response_array) {

            if (!empty($category_ids)) {
                foreach ((array)$category_ids[$response_array->ean] as $category_id_value) {
                    $this->postProductLink($response_array->id, $category_id_value);
                }
            }

            // $info[] = array(
            //     'user_id' => $this->user_id,
            //     'shop_id' => $this->shop_id,
            //     'product_id' => $response_array->id,
            //     'product_model' => $response_array->productModel,
            //     'ean' => $response_array->ean,
            //     'created_at' => now()
            // );


            DB::table('gambio_products')->updateOrInsert([
                'user_id' => $this->user_id,
                'shop_id' => $this->shop_id,
                'ean' => $response_array->ean,
            ],[
                'product_id' => $response_array->id,
                'product_model' => $response_array->productModel,
            ]);

        }

        // DB::table('gambio_products')->whereNotIn('ean', $success_product_ean)->where('shop_id', $this->shop_id)->insert($info);
        $info = null;

    }


    /* Gambio Product Update process */

    public function updateProduct($productsarray)
    {
        $product_id = $productsarray['id'];

        $api_path = "api.php/v2/";
        $user = $this->shop_details->username;
        $pass = $this->shop_details->password;
        $auth = base64_encode("$user:$pass");
        $url = $this->shop_details->url . $api_path . "products";
        $curl_header = array("authorization: Basic " . $auth, "content-type: application/json");
        $alldata = json_encode($productsarray);

        $updateProducts = $this->doCulrRequest($url, $curl_header, $alldata, 'PUT');

        $this->doAfterProductUpdate($updateProducts);
    }

    public function doAfterProductUpdate($updateProducts)
    {
        $all_response_array = json_decode($updateProducts);
        if (count((array) $all_response_array->affected) > 0) {
            $this->handleUpadteSuccess($all_response_array->affected);
        }
        // if (count((array) $all_response_array->error) > 0 && $all_response_array->status == 'error') {
        //     User::find($this->user_id)->notify(new DRMNotification('Gambio Export '. $all_response_array->message , '', '#'));
        // }
    }

    public function handleUpadteSuccess($success_products)
    {
        // $success_product_ean = collect($success_products)->pluck('ean')->toArray();

        $info = [];
        foreach ($success_products as $success_product) {
            $info = array(
                'user_id' => $this->user_id,
                'shop_id' => $this->shop_id,
                'product_id' => $success_product->id,
                'product_model' => $success_product->productModel,
                'ean' => $success_product->ean,
            );

            DB::table('gambio_products')->updateOrInsert([
                'user_id' => $this->user_id,
                'shop_id' => $this->shop_id,
                'ean' => $success_product->ean,
            ],[
                'product_id' => $success_product->id,
                'product_model' => $success_product->productModel,
            ]);
        }

        // DB::table('gambio_products')->whereNotIn('ean', $success_product_ean)->where('shop_id', $this->shop_id)->insert($info);


        // DB::table('gambio_products')->whereIn('ean', $success_product_ean)->where('shop_id', $this->shop_id)->update(['updated_at' => now()]);

        $info = null;

    }


    /* Start Gambio Product Delete process */

    /* Gambio Duplicate Product Delete */

    public static function findDuplicateProducts($shop) 
    {
        $api_path = "api.php/v2/";
        $auth = base64_encode("$shop->username:$shop->password");
        $url = $shop->url . $api_path . "products";
        $headers = array("Authorization: Basic " . $auth, "content-type: application/json");

        $response = self::doCulrRequest($url, $headers, $alldata, 'GET');
        $allProducts = json_decode($response);

        
        $api_product_ean = array();
        $api_product_id = array();
        $set_ean = array();

        foreach ((array) $allProducts as $productValue) {
            $api_product_ean[] = $productValue->ean;
            $api_product_id[] = $productValue->id;
        }

        foreach ($api_product_ean as $key => $value) {
            $set_ean[$value]++;
            if ($set_ean[$value] > 1) {
                if ($api_product_ean[$key] != '') {
                    $productId[] = $api_product_id[$key];
                    $productEan[] = $api_product_ean[$key];
                }
            }
        }

        if (isset($productId)) {
            self::deleteDuplicateProducts($shop, $productId);
        }
    }


    public function findDuplicateCategories($shop)
    {
        // $category_info = self::doCulrRequest($url, $headers, $alldata, 'GET');
        $response = collect(self::gambioSearchProducts($shop,""));
        dd("Ok");

        $ids = $response->pluck('id')->toArray();
        
        $api_path = "api.php/v2/";
        $auth = base64_encode("$shop->username:$shop->password");
        // $url = $shop->url . $api_path . "shop_information";
        $headers = array("Authorization: Basic " . $auth, "content-type: application/json");

        // $response = self::doCulrRequest($url, $headers, $alldata, 'GET');
        // $allProducts = json_decode($response);

        // $delete_cat_ids = array(18,20,29,362);

        // dd($allProducts);
        // $ids = $cats->pluck('id')->toArray();

        
        // foreach (array_chunk((array)$allProducts,200) as $productValues) {
        //     $delete_cat_ids = array();
        //     foreach ($productValues as $key => $productValue) {
        //         // $db_category_info = $this->searchDBCategoryByParent1($productValue->name,$productValue->id);

        //         // if(!$db_category_info){
        //             $delete_cat_ids[] = $productValue->id;
        //         // }
        //     }

            
            $url = $shop->url . $api_path . "products/".implode(',',$ids);
            // dd($url);
            $delete_response = self::doCulrRequest($url, $headers, $alldata, 'DELETE');
            dump($delete_response);
        //     // dd($delete_response);
        // }
        
        // if(count($delete)){
        //     $url = $shop->url . $api_path . "products/".implode(',',$delete);
        //     // dd($url);
        //     $delete_response = self::doCulrRequest($url, $headers, $alldata, 'DELETE');
        //     dump($delete_response);
        // }
 


        // foreach($names as $name){
            
        // }
        // foreach ($api_product_ean as $key => $value) {
        //     $set_ean[$value]++;
        //     if ($set_ean[$value] > 1) {
        //         if ($api_product_ean[$key] != '') {
        //             $productId[] = $api_product_id[$key];
        //             $productEan[] = $api_product_ean[$key];
        //         }
        //     }
        // }

        // if (isset($productId)) {
        //     self::deleteDuplicateProducts($shop, $productId);
        // }

    }
    /* Delete Products Process From Gambio Shop */

    public static function deleteGambioShopProduct($product_ids, $shop_details)
    {
        if (count((array) $product_ids) > 0) {
            $delete_items_ean = [];
            $productEan = DB::table('drm_products')->whereIn('id', (array)$product_ids)->get()->pluck('ean')->toarray();
            foreach ($productEan as $product_ean) {
                $exists = self::gambioSearchProduct($shop_details, $product_ean);
                if ($exists) {

                    $auth = base64_encode("$shop_details->username:$shop_details->password");
                    $url = $shop_details->url . "api.php/v2/products/" . $exists;
                    $curl_header = array("accept: application/json", "authorization: Basic " . $auth, "content-type: application/json");
                    $delete_response = self::doCulrRequest($url, $curl_header, $alldata, 'DELETE');
                    
                    trace('gambio delete response :'.$delete_response);

                    if (json_decode($delete_response)->code == 200) {
                        $delete_items_ean[] = $product_ean;
                    }elseif (json_decode($delete_response)->code == NULL) {
                        $delete_items_ean[] = $product_ean;
                    }
                }
            }
            DB::table('gambio_products')->whereIn('ean',$delete_items_ean)->where('shop_id',$shop_details->id)->delete();
            $delete_items_ean = NULL;
        }
    }


    public static function deleteDuplicateProducts($shop, $product_ids)
    {
        foreach ($product_ids as $product_id) {
            $api_path = "api.php/v2/";
            $user = $shop->username;
            $pass = $shop->password;
            $auth = base64_encode("$user:$pass");
            $url = $shop->url . $api_path . "products/" . $product_id;
            $curl_header = array("accept: application/json", "authorization: Basic " . $auth, "content-type: application/json");
            $res = self::doCulrRequest($url, $curl_header, $alldata, 'DELETE');
        }

    }

    public static function deleteDuplicateProduct($shop, $product_ids)
    {
        foreach ($product_ids as $product_id) {
            $api_path = "api.php/v2/";
            $user = $shop->username;
            $pass = $shop->password;
            $auth = base64_encode("$user:$pass");
            $url = $shop->url . $api_path . "products/" . $product_id;
            $curl_header = array("accept: application/json", "authorization: Basic " . $auth, "content-type: application/json");
            $res = self::doCulrRequest($url, $curl_header, $alldata, 'DELETE');
            dump($res);
        }

    }

    /* End Gambio Product Delete process */


    /* Gambio Image Upload Process */

    public function makeImageArray($src, $product)
    {
        $image = array();
        $all_images = drm_fix_image($src);
        if (count((array) $all_images) > 0) {
            foreach ($all_images as $image => $single_image) {
                $single_image = $single_image->src;
                $image_name = explode("/", urldecode($single_image));
                $lastname = end($image_name);
                $image_details['filename'] = (preg_match('/(\.jpg|\.png|\.bmp)$/i', $lastname)) ? $product->ean . "_" . $lastname : $product->ean . "_" . $lastname . ".jpg";
                $image_details['isPrimary'] = ($image == 0) ? true : false;
                $image_details['isVisible'] = 1;
                $img[] = $image_details;
            }
        }
        return $img;
    }

    public static function GambioImageUploadArray($shop, $ids)
    {
        $products = DB::table('drm_products')->whereIn('id', (array)$ids)->get()->toarray();

        foreach ($products as $product) {
            GambioApi::GambioImageUploadSingle($shop, $product);
        }
    }

    public static function GambioImageUploadSingle($shop, $product)
    {
        $api_path = "api.php/v2/";
        $user = $shop->username;
        $pass = $shop->password;
        $auth = base64_encode("$user:$pass");
        $url = $shop->url . $api_path . "product_images/";

        $all_images = drm_fix_image($product->image);
        if (count((array) $all_images) > 0) {
            $img_name = array();
            $img = array();
            $total_error = 0;
            $total_success = 0;
            $msg = array();
            $count = 0;
            $already_uploaded = 0;
            $invalidImageCount = 0;

            foreach ($all_images as $image => $single_image) {
                $count++;
                $single_images = urldecode($single_image->src);

                if (filter_var($single_images, FILTER_VALIDATE_URL) === FALSE) {
                    $single_images = url($single_images);
                }

                $image_name = explode("/", urldecode($single_images));
                $lastname = end($image_name);
                $filename = (preg_match('/(\.jpg|\.png|\.bmp)$/i', $lastname)) ? $product->ean . "_" . $lastname : $product->ean . "_" . $lastname . ".jpg";

                $exits_image = $shop->url . 'images/product_images/original_images/' . $filename;

                $image_exists = checImageUrl($exits_image);
                if ($image_exists) {
                    // echo $exits_image . "image exits<br/>";
                    $already_uploaded++;
                    continue;
                }
                
                $download_image = @file_get_contents($single_images, false);

                if ($download_image === false) {
                    // echo "Invalid image" . "</br>";
                    $invalidImageCount++;
                    continue;
                }
                $file_saved = storage_path() . '/' . $filename;
                $img_name[$image] = $file_saved;
                $upload = file_put_contents($file_saved, $download_image);
                $cfile = curl_file_create($file_saved, 'image/jpeg', $filename);
                $params = array('upload_product_image' => $cfile, 'filename' => $filename);
                $headers = array("Authorization: Basic " . $auth);
                $output = GambioApi::doCulrRequest($url, $headers, $params);
                @unlink($file_saved);
                $all_response_array = json_decode($output);
                if ($all_response_array->code == 201) {
                    $msg = $output;
                    $total_success++;
                } else {
                    $msg = $output;
                    $total_error++;
                }
            }

            if ($invalidImageCount>0) {
                User::find($shop->user_id)->notify(new DRMNotification('Gambio export '.$invalidImageCount.' image invalid, product ean - '.$product->ean, 'GAMBIO_API_ERROR', '#'));
            }

            $curl_response['msg'] = $msg;
            $curl_response['success'] = $total_success;
            $curl_response['error'] = $total_error;
            $curl_response['total'] = $count;
            $curl_response['already_uploaded'] = $already_uploaded;

            $image_response = json_encode($curl_response);

        }

    }


    /* Search Gambio Shop Product */


    public static function gambioSearchProduct($shop_details, $ean)
    {
        $api_path = "api.php/v2/";
        $user = $shop_details->username;
        $pass = $shop_details->password;
        $auth = base64_encode("$user:$pass");
        $url = $shop_details->url . $api_path . "products/search";
        $curl_header = array("accept: application/json", "authorization: Basic " . $auth, "content-type: application/json");
        $criteria['match']['products.products_ean'] = $ean;
        $search['search'] = $criteria;
        $alldata = json_encode($search);
        $updateProducts = self::doCulrRequest($url, $curl_header, $alldata, 'POST');
        $update_products = json_decode($updateProducts, true);

        if (isset($update_products[0]['ean']) && $update_products[0]['ean'] == $ean) {
            return $update_products[0]['id'];
        } else {
            return false;
        }
    }

    public static function gambioSearchProducts($shop_details, $ean)
    { 
        $api_path = "api.php/v2/";
        $user = $shop_details->username;
        $pass = $shop_details->password;
        $auth = base64_encode("$user:$pass");
        $url = $shop_details->url . $api_path . "products/search";
        $curl_header = array("accept: application/json", "authorization: Basic " . $auth, "content-type: application/json");
        // $criteria['match']['products_description.products_name'] = $ean;
        $criteria['match']['products.products_ean'] = $ean;
        $search['search'] = $criteria;
        $alldata = json_encode($search);
        $updateProducts = self::doCulrRequest($url, $curl_header, $alldata, 'POST');

        $update_products = json_decode($updateProducts, true);

        return $update_products;
    } 


    /* Gambio Shop Product Category Process */


    public function getGambioCategory($category)
    {
        $trans_category = 'category_name_' . $this->lang;
        
        $ids = array();
        foreach ($category as $value) 
        {
            $category_name_translation = trim($value->$trans_category);
            $category_items = array_map('trim', preg_split("/( - |>)/", trim($category_name_translation)));
            $category_id = 0;
            foreach ($category_items as $key => $category_name)
            {
                $category_info   = $this->searchDBCategory($category_name,$category_id);
                $category_id = $category_info->category_id ?? $this->createGambioCategory($category_name,$category_id);
            }

            $ids[] = $category_id;
        }
        return $ids;
    }



    public function createGambioCategory($category,$parent)
    {
        $api_path = "api.php/v2/";
        $user = $this->shop_details->username;
        $pass = $this->shop_details->password;
        $auth = base64_encode("$user:$pass");
        $url = $this->shop_details->url . $api_path . "categories";
        $headers = array("accept: application/json", "authorization: Basic " . $auth, "content-type: application/json");

        $data['parentId'] = $parent;
        $data['isActive'] = true;
        $namearray[$this->lang] = $category;


        $data['name'] = $namearray;
        $data['headingTitle'] = $namearray;
        $data['description'] = $namearray;
        $data['urlKeywords'] = $namearray;
        $data['metaDescription'] = $namearray;
        $data['metaKeywords'] = $namearray;
        $data['metaTitle'] = $namearray;
        $data['sortOrder'] = 0;
        $data['icon'] = "";
        $data['image'] = "";
        $postcategory_new[] = $data;

        $alldata = json_encode($postcategory_new);
        $response = $this->doCulrRequest($url, $headers, $alldata, 'POST');
        $all_response_array = json_decode($response, true);

        $id = false;

        if(isset($all_response_array['created']))
        {
            $id = $all_response_array['created'][0]['id'];
            $this->createDBCategory($category,$id,$parent);
        }
        
        return $id;
    }



    public function createDBCategory($category,$id,$parent)
    {
        DB::table('gambio_categories')->insert([
            'category_name' => $category,
            'category_id' => $id,
            'parent_id'   => $parent,
            'shop_id'     => $this->shop_details->id,
            'country_id'  => 1,
            'created_at'  => now()
        ]);
    }

    public function postProductcategories($lang = 'DE', $category, $parentId)
    {
        $category_info = $this->searchApiCategory($category);
        $db_category_info = $this->searchDBCategoryByParent($category,$parentId);
        // $db_category_info = false;
        // dd($category_info,$db_category_info);
        if($category_info) {
            if (isset($category_info['status']) && $category_info['status'] == "error") {
                 return false;
            }
         }

        if(empty($category_info) && empty($db_category_info)){

            $api_path = "api.php/v2/";
            $user = $this->shop_details->username;
            $pass = $this->shop_details->password;
            $auth = base64_encode("$user:$pass");
            $url = $this->shop_details->url . $api_path . "categories";
            $headers = array("accept: application/json", "authorization: Basic " . $auth, "content-type: application/json");
    
            $data['parentId'] = $parentId;
            $data['isActive'] = true;
            $namearray[$lang] = $category;
            $data['name'] = $namearray;
            $data['headingTitle'] = $namearray;
            $data['description'] = $namearray;
            $data['urlKeywords'] = $namearray;
            $data['metaDescription'] = $namearray;
            $data['metaKeywords'] = $namearray;
            $data['metaTitle'] = $namearray;
            $data['sortOrder'] = 0;
            $data['icon'] = "";
            $data['image'] = "";
            $postcategory_new[] = $data;
    
            $alldata = json_encode($postcategory_new);
            $response = $this->doCulrRequest($url, $headers, $alldata, 'POST');
    
            $all_response_array = json_decode($response, true);
            return $all_response_array;
        }
        else {
            return false;
        }

    }


    /* Gambio Product Link With Categories Process */

    /* Search Gambio Shop Categories */

    public static function gambioSearchCategories($shop_details, $category_name)
    {
        $api_path = "api.php/v2/";
        $user = $shop_details->username;
        $pass = $shop_details->password;
        $auth = base64_encode("$user:$pass");
        $url = $shop_details->url . $api_path . "categories/search";
        $curl_header = array("accept: application/json", "authorization: Basic " . $auth, "content-type: application/json");

        $criteria['match']['categories_description.categories_name'] = $category_name;
        $criteria1['match']['categories_description.categories_name'] = trim($category_name);
        $criteria2['match']['categories_description.categories_name'] = Str::start($category_name, ' ');
        $criteria3['match']['categories_description.categories_name'] = Str::finish($category_name, ' ');
        $criteria4['match']['categories_description.categories_description'] = $category_name;
        $criteria5['match']['categories_description.categories_description'] = trim($category_name);
        $criteria6['match']['categories_description.categories_description'] = Str::start($category_name, ' ');
        $criteria7['match']['categories_description.categories_description'] = Str::finish($category_name, ' ');

        $should_criteria['should'] = [$criteria, $criteria1, $criteria2, $criteria3, $criteria4, $criteria5, $criteria6, $criteria7];
        $search['search'] = $should_criteria;
        $alldata = json_encode($search);

        $response = self::doCulrRequest($url, $curl_header, $alldata, 'POST');
        $response = json_decode($response, true);

        return $response;
    }

    public function searchDBCategory($category_name,$parent)
    {
        $gambio_categories = DB::table('gambio_categories')
            ->where(['category_name' => trim(makeUtf8($category_name)) ,'shop_id' => $this->shop_details->id ,'parent_id' => $parent])
            ->first();
        // trace("Gambio category : ".$category_name." Parent : ".$parent. json_encode($gambio_categories));
        return $gambio_categories;
    }



    public function searchApiCategory($category_name)
    {
        $api_path = "api.php/v2/";
        $user = $this->shop_details->username;
        $pass = $this->shop_details->password;
        $auth = base64_encode("$user:$pass");
        $url = $this->shop_details->url . $api_path . "categories/search";
        $curl_header = array("accept: application/json", "authorization: Basic " . $auth, "content-type: application/json");

        $criteria['match']['categories_description.categories_name'] = $category_name;
        // $criteria1['match']['categories.parent_id'] = $parent;

        $should_criteria['should'] = [$criteria, $criteria1];
        $search['search'] = $should_criteria;
        $alldata = json_encode($search);
        $response = self::doCulrRequest($url, $curl_header, $alldata, 'POST');

        $res = json_decode($response, true);
        if(($response == "" || $response == null)){
            $response = json_encode([
                'status' => 'error'
            ]);
        }
        $response = json_decode($response, true);
        
        return $response;
    }

    public function searchDBCategoryByParent($category_name,$parent)
    {
        $gambio_category = DB::table('gambio_categories')
            ->where(['parent_id' => $parent, 'category_name' => $category_name ,'shop_id' => $this->shop_details->id])
            ->first();
        return $gambio_category;
    }

    public function searchDBCategoryByParent1($category_name,$category_id)
    {
        $gambio_category = DB::table('gambio_categories')
            ->where(['category_id' => $category_id ,'category_name' => $category_name,'shop_id' => $this->shop_details->id])
            ->exists();
        return $gambio_category;
    }

    public function getProductLink($product_id)
    {
        $api_path = "api.php/v2/";
        $user = $this->shop_details->username;
        $pass = $this->shop_details->password;
        $auth = base64_encode("$user:$pass");

        $url = $this->shop_details->url . $api_path . "products/$product_id/links";
        $headers = array("authorization: Basic " . $auth, "content-type: application/json");

        $response = $this->doCulrRequest($url, $headers, $alldata, 'GET');

        return $response;
    }

    public function postProductLink($product_id, $category_id = NULL)
    {
        $api_path = "api.php/v2/";
        $user = $this->shop_details->username;
        $pass = $this->shop_details->password;
        $auth = base64_encode("$user:$pass");

        $url = $this->shop_details->url . $api_path . "products/$product_id/links";
        $headers = array("accept: application/json", "authorization: Basic " . $auth, "content-type: application/json");
        $data['categoryId'] = $category_id;

        $alldata = json_encode($data);
        $response = $this->doCulrRequest($url, $headers, $alldata, 'POST');
        return $response;
    }


    public function deleteProductLink($product_id, $category_id)
    {
        $api_path = "api.php/v2/";
        $user = $this->shop_details->username;
        $pass = $this->shop_details->password;
        $auth = base64_encode("$user:$pass");

        $url = $this->shop_details->url . $api_path . "products/$product_id/links";
        $headers = array("authorization: Basic " . $auth, "content-type: application/json");

        $data['categoryId'] = $category_id;
        $alldata = json_encode($data);

        $response = $this->doCulrRequest($url, $headers, $alldata, 'DELETE');
        return $response;
    }


    /* Gambio Product Manufacturers */

    public function gambioSearchManufacturers($manufacturers_name)
    {
        $api_path = "api.php/v2/";
        $user = $this->shop_details->username;
        $pass = $this->shop_details->password;
        $auth = base64_encode("$user:$pass");
        $url = $this->shop_details->url . $api_path . "manufacturers/search";

        $curl_header = array("accept: application/json", "authorization: Basic " . $auth, "content-type: application/json");

        $criteria['match']['manufacturers.manufacturers_name'] = $manufacturers_name;
        $criteria1['match']['manufacturers.manufacturers_name'] = trim($manufacturers_name);

        $should_criteria['should'] = [$criteria, $criteria1];
        $search['search'] = $should_criteria;
        $alldata = json_encode($search);

        $response = self::doCulrRequest($url, $curl_header, $alldata, 'POST');
        $manufacturers = json_decode($response, true);
        return $manufacturers;
    }

    public function postProductManufacturers($manufacturers)
    {
        $api_path = "api.php/v2/";
        $user = $this->shop_details->username;
        $pass = $this->shop_details->password;
        $auth = base64_encode("$user:$pass");
        $url = $this->shop_details->url . $api_path . "manufacturers";
        $headers = array("accept: application/json", "authorization: Basic " . $auth, "content-type: application/json");

        $data['name'] = $manufacturers;

        $alldata = json_encode($data);
        $response = $this->doCulrRequest($url, $headers, $alldata, 'POST');

        $manufacturers = json_decode($response);
        return $manufacturers;
    }




    public function productCategoriesProcess($category)
    {
        $trans_category = 'category_name_' . $this->lang;
        foreach ($category as $value) {
            $category_name_translation = trim($value->$trans_category);
            
            if(!empty($category_name_translation)){
                if (preg_match("/( - |>)/", $category_name_translation)){
                    $category_items = array_map('trim', preg_split("/( - |>)/", trim($category_name_translation)));
                    
                    foreach ((array)$category_items as $key => $category_item_value) {
                        $category_info = self::gambioSearchCategories($this->shop_details, $category_item_value);
                        
                        if (empty($category_info)) {
                            sleep(1);
                            $category_info = self::gambioSearchCategories($this->shop_details, $category_item_value);
                            $category_first_info = self::gambioSearchCategories($this->shop_details, $category_items[0]);
                            $category_key_info = self::gambioSearchCategories($this->shop_details, $category_items[$key - 1]);
    
                            if (empty($category_info) && empty($category_first_info)) {
                                $this->postProductcategories($this->lang, trim($category_item_value), 0);
                            } elseif (empty($category_info) && !empty($category_key_info) && count((array)$category_key_info) > 1) {
                                
                                $category_info_collection = collect($category_key_info);
    
                                if (isset($category_first_info[0]['id']) && $category_info_collection->contains('parentId',$category_first_info[0]['id'])  && !$category_info_collection->contains('name',$category_item_value) || !$category_info_collection->contains('name',trim($category_item_value))) {
    
                                    $category_id = $category_info_collection->where('parentId', $category_first_info[0]['id'])->first();
    
                                    if (!empty($category_id)) {
                                        $this->postProductcategories($this->lang, trim($category_item_value), $category_id['id']);
                                    } elseif (empty($category_id)) {
                                        $category_second_info = collect(self::gambioSearchCategories($this->shop_details, $category_items[$key - 2]));
                                        $category_second_info_id = $category_second_info->where('parentId', $category_first_info[0]['id'])->first();
                                        $category_id = $category_info_collection->where('parentId', $category_second_info_id['id'])->first();
                                        if (!empty($category_id)) {
                                            $this->postProductcategories($this->lang, trim($category_item_value), $category_id['id']);
                                        }
                                    }
    
                                }
    
                            } elseif (empty($category_info) && !empty($category_key_info)) {
    
                                $this->postProductcategories($this->lang, trim($category_item_value), $category_key_info[0]['id']);
                            }
    
                        } else {
    
                            $category_info_collection = collect($category_info);
    
                            if ($key == 1) {
                                $category_first_info = self::gambioSearchCategories($this->shop_details, $category_items[0]);
    
                                if(isset($category_first_info[0]['id']) && !$category_info_collection->contains('parentId',$category_first_info[0]['id'])){
    
                                    $this->postProductcategories($this->lang, trim($category_item_value), $category_first_info[0]['id']);
                                }
    
                            }elseif ($key == 2) {
                                $category_first_info = self::gambioSearchCategories($this->shop_details, $category_items[0]);
                                $category_second_info = collect(self::gambioSearchCategories($this->shop_details, $category_items[1]));
                                $category_second_info_id = $category_second_info->where('parentId',$category_first_info[0]['id'])->first();
    
                                if(!empty($category_second_info_id) && !$category_info_collection->contains('parentId',$category_second_info_id['id'])){
    
                                    $this->postProductcategories($this->lang, trim($category_item_value), $category_second_info_id['id']);
                                }
    
                            } elseif ($key == 3) {
                                $category_first_info = self::gambioSearchCategories($this->shop_details, $category_items[0]);
                                $category_second_info = collect(self::gambioSearchCategories($this->shop_details, $category_items[1]));
                                $category_third_info = collect(self::gambioSearchCategories($this->shop_details, $category_items[2]));
                                $category_second_info_id = (object)$category_second_info->where('parentId',$category_first_info[0]['id'])->first();
                                $category_third_info_id = $category_third_info->where('parentId',$category_second_info_id->id)->first();
    
                                if(!empty($category_third_info_id) && !$category_info_collection->contains('parentId',$category_third_info_id['id'])){
    
                                    $this->postProductcategories($this->lang, trim($category_item_value), $category_third_info_id['id']);
                                }
    
                            } elseif ($key == 4) {
                                $category_first_info = self::gambioSearchCategories($this->shop_details, $category_items[0]);
                                $category_second_info = collect(self::gambioSearchCategories($this->shop_details, $category_items[1]));
                                $category_third_info = collect(self::gambioSearchCategories($this->shop_details, $category_items[2]));
                                $category_four_info = collect(self::gambioSearchCategories($this->shop_details, $category_items[3]));
                                $category_second_info_id = (object)$category_second_info->where('parentId',$category_first_info[0]['id'])->first();
                                $category_third_info_id = (object)$category_third_info->where('parentId',$category_second_info_id->id)->first();
                                $category_four_info_id = $category_four_info->where('parentId',$category_third_info_id->id)->first();
    
                                if(!empty($category_four_info_id) && !$category_info_collection->contains('parentId',$category_four_info_id['id'])){
    
                                    $this->postProductcategories($this->lang, trim($category_item_value), $category_four_info_id['id']);
                                }
    
                            }
    
                        }
                    }
                } else {
    
                    $category_info = self::gambioSearchCategories($this->shop_details, $category_name_translation);
    
                    if (empty($category_info)) {
    
                        sleep(1);
                        $category_info = self::gambioSearchCategories($this->shop_details, $category_name_translation);
                       
                        if (empty($category_info)) {
                            $this->postProductcategories($this->lang, $category_name_translation, 0);
                        }
                    }
                }
    
            }
    
        }
    
    }


    public function productLinkWithCategories($category)
    {
        $trans_category = 'category_name_' . $this->lang;
        foreach ($category as $value) {
            $category_name_trans = $value->$trans_category;
            if (!empty($category_name_trans)) {

                if (preg_match("/( - |>)/", $category_name_trans)) {
                    $category_collection = new Collection(array_map('trim', preg_split("/( - |>)/", trim($category_name_trans))));

                    $category_info = self::gambioSearchCategories($this->shop_details, $category_collection->last());
                    if (empty($category_info) && is_array($category_info)) {
                        sleep(1);
                        $category_info = self::gambioSearchCategories($this->shop_details, $category_collection->last());
                    }


                    if (count((array)$category_info)>1) {
                        $category_new_info = collect(self::gambioSearchCategories($this->shop_details, $category_collection->get($category_collection->count() - 2)));
                        $category_key_info = self::gambioSearchCategories($this->shop_details, $category_collection->get(0));

                        if (empty($category_new_info) && is_array($category_new_info)) {
                            sleep(1);
                            $category_new_info = collect(self::gambioSearchCategories($this->shop_details, $category_collection->get($category_collection->count() - 2)));
                        }

                        $category_info_list = collect($category_info);

                        $category_new_info_id = (object)$category_new_info->where('parentId',$category_key_info[0]['id'])->first();

                        $find_category_info = $category_info_list->where('parentId',$category_new_info_id->id)->first();

                        if (empty($find_category_info)) {

                            $find_category_info = $category_info_list->where('parentId',$category_key_info[0]['id'])->first();
                            if(empty($find_category_info)){
                                $find_category_info = $category_info_list->where('parentId',$category_new_info[0]['id'])->first();
                                $category_id[] = $find_category_info['id'];
                            } elseif (!empty($find_category_info)) {
                                $category_id[] = $find_category_info['id'];
                            }


                        } elseif (!empty($find_category_info)) {
                            $category_id[] = $find_category_info['id'];
                        }

                    } elseif (count((array)$category_info) == 1) {
                        $category_id[] = $category_info[0]['id'];
                    }
                } else {
                    $category_info = self::gambioSearchCategories($this->shop_details, $category_name_trans);

                    if (empty($category_info) && is_array($category_info)) {
                        sleep(1);
                        $category_info = self::gambioSearchCategories($this->shop_details, $category_name_trans);
                    }

                    if (count((array)$category_info) == 1) {
                        $category_id[] =$category_info[0]['id'];
                    } elseif (count((array)$category_info) > 1) {

                        $category_info_list = collect($category_info);
                        $find_category_info = $category_info_list->where('parentId',0)->first();

                        if (!empty($find_category_info)) {
                            $category_id[] = $find_category_info['id'];
                        }

                    }
                }
            }

        }
        return $category_id;
    }




}

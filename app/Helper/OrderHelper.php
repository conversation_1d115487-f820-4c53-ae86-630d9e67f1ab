<?php

namespace App\Helper;

use PDF;
use App\NewOrder;
use Exception;
use App\User;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use App\OrderTrackings;
use App\Notifications\DRMNotification;

class OrderHelper
{
    //Send status email
    public static function sendOrderStatusMail($order_id, $options = [])
    {
        // //DB::beginTransaction();
        try {
            //Check validity
            if (empty($order_id)) return false;

            usleep(5);
            $order = NewOrder::with('customer')->where('id', $order_id)->first();
            if (empty($order)) return false;

            $order_status = $order->status;
            if (empty($order_status)) return false;

            //Check user has active status mail
            $user_id = $order->cms_user_id;

            // if( (isLocal() || in_array($order->cms_user_id, [212, 2592])) ){
                // $combine_status = ['Erstattet', 'credit_note_created', 'refund_initiated', 'refund_completed'];
                $combine_status = config('order_status.combine_status');

                $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

                if(in_array($order_status, $combine_status)){

                    if($user_id > 2990){
                        $channel_combine_status_mail = DB::table('combine_status_email_settings')
                        ->where(['cms_user_id' => $user_id, 'channel' => $channel])
                        ->exists();
    
                        if($channel_combine_status_mail){
                            $combine_status_template = getCombineStatusMailTemplate($user_id, $channel);
                        }else{
                            $combine_status_template = getCombineStatusMailTemplate($user_id);
                        }
    
                        if (empty($combine_status_template) || ($combine_status_template->auto_mail != 1)) return false;
                    }else{
                        $channel_order_status_mail = DB::table('order_mail_templates')
                        ->where(['user_id' => $user_id, 'channel' => $channel, 'order_status' => $order_status])
                        ->exists();

                        if($channel_order_status_mail){
                            $status_template = getStatusMailTemplate($user_id, $order_status, $channel);
                        }else{
                            $status_template = getStatusMailTemplate($user_id, $order_status);
                        }

                        if (empty($status_template) || ($status_template->auto_mail != 1)) return false;
                    }

                }else{

                    $channel_order_status_mail = DB::table('order_mail_templates')
                    ->where(['user_id' => $user_id, 'channel' => $channel, 'order_status' => $order_status])
                    ->exists();

                    if($channel_order_status_mail){
                        $status_template = getStatusMailTemplate($user_id, $order_status, $channel);
                    }else{
                        $status_template = getStatusMailTemplate($user_id, $order_status);
                    }

                    if (empty($status_template) || ($status_template->auto_mail != 1)) return false;

                }

            // }else{
            // $status_template = getStatusMailTemplate($user_id, $order_status);
            // if (empty($status_template) || ($status_template->auto_mail != 1)) return false;
            // }
            //validity check end

            $is_charge_email = false;
            $pay_url = null;
            $paywall = null;

            $data = [];
            $invoice_data = [];
            $invoice_data['page_title'] = 'Order mail';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = $product_list = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

            if($order->marketplace_order_ref && $order->cms_user_id == 2455) {
              $invoice_data['shipping_details'] = DB::table("new_orders")
              ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
              ->select("new_orders.id as mp_order_id", "new_customers.*")
              ->where('new_orders.id', $order->marketplace_order_ref)->first();
            }

            $pdf_view = '';
            $pdf_name = '';
            //Select blade file
            if ($order->credit_number > 0) {
                $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.credit_note.daily' : 'admin.credit_note.general';
                $pdf_view = ($order->insert_type == 4) ? 'admin.credit_note.charge_inv' : $pdf_view;

                $pdf_name = 'credit_' . $order->credit_number;
            } else {
                $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.invoice.daily' : 'admin.invoice.general';
                $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;
                $pdf_name = 'invoice_' . $order->invoice_number;
            }

            $customer_name = $order->customer ? $order->customer->full_name : '';
            $note_name = $pdf_name . '_' . $customer_name;
            $note_name = preg_replace('/\s+/', '_', $note_name);
            $note_name = preg_replace('/[_]+/', '_', $note_name);
            $note_name = strtolower($note_name . '.pdf');

            //Send payment link
            // if ($order->insert_type == 4) {
            //     $paywall = MonthlyPaywall::where('order_id', $order->id)->whereNull('paid_at')->first();
            //     if ($paywall) {
            //         $is_charge_email = true;
            //         $pay_url = route('user-paywall-payment', $paywall->id);
            //     }
            // } elseif (($order->cms_user_id == 98) && ($order->total > 0)) {
            //     $due_status = ['nicht_bezahlt'];
            //     if (in_array($order->status, $due_status)) {
            //         $is_charge_email = true;
            //         $pay_url = route('user-invoice-payment', $order->id);
            //     }
            // }

            //Email content
            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';
            $logo = $invoice_data['setting']->logo ?? '';

            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => \Carbon\Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array($order->cms_user_id, [212, 2592])) ){
                
                if($order->marketplace_order_ref){
                    $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();
                    
                    $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                }else{
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $tags['shipping_address'] = $shipping;
                $tags['credit_note'] = $order->credit_number;

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', $order->cms_user_id)->pluck('signature','id')->toArray();
                
                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            // (isLocal() || in_array($order->cms_user_id, [212, 2592])) && 
            if( ($order_status == 'Shipped') ){

                $tags['package_number'] = '';
                $tags['parcel_service'] = '';

                $tracking = OrderTrackings::with('parcel')->where('order_id', '=', $order->id)->latest()->first();
                if($tracking && $tracking->id)
                {
                    $parcel_name = ($tracking->parcel) ? $tracking->parcel->parcel_name : '';
                    $package_number = $tracking->package_number;

                    if (strtolower($parcel_name) == 'dhl') {
                        $package_number = '<a href="https://www.dhl.de/de/privatkunden/pakete-empfangen/verfolgen.html?piececode=' . $package_number . '" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'dpd') {
                        $package_number = '<a href="https://www.dpd.com/de/de/empfangen/sendungsverfolgung-und-live-tracking" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'hermes') {
                        $package_number = '<a href="https://www.myhermes.de/empfangen/sendungsverfolgung" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'ups') {
                        $package_number = '<a href="https://www.ups.com/track?loc=de_DE&requester=ST&trackNums=' . $package_number . '/trackdetails" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'tnt') {
                        $package_number = '<a href="https://www.tnt.com/express/de_de/site/shipping-tools/tracking.html?searchType=con&cons=' . $package_number . '" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'gls') {
                        $package_number = '<a href="https://www.gls-pakete.de/sendungsverfolgung?trackingNumber=' . $package_number . '" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'dachser') {
                        $package_number = '<a href="https://elogistics.dachser.com/shp2s/?search=' . $package_number . '&javalocale=de_DE" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'swisspost') {
                        $package_number = '<a href="https://service.post.ch/ekp-web/ui/entry/search/' . $package_number . '" target="_blank">' . $package_number . '</a>';
                    }

                    $tags['package_number'] = $package_number;
                    $tags['parcel_service'] = $parcel_name;
                }
            }

            // if( (isLocal() || in_array($order->cms_user_id, [212, 2592])) ){
                // $combine_status = ['Erstattet', 'credit_note_created', 'refund_initiated', 'refund_completed'];
                $combine_status = config('order_status.combine_status');

                $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

                if(in_array($order_status, $combine_status)){

                    if($order->cms_user_id > 2990){
                        $channel_combine_status_mail = DB::table('combine_status_email_settings')
                        ->where(['cms_user_id' => $user_id, 'channel' => $channel])
                        ->exists();
    
                        if($channel_combine_status_mail){
                            $template = DRMParseOrderCombineStatusEmailTemplate($tags, $user_id, $channel);
                        }else{
                            $template = DRMParseOrderCombineStatusEmailTemplate($tags, $user_id);
                        }
                    }else{
                        $channel_order_status_mail = DB::table('order_mail_templates')
                        ->where(['user_id' => $user_id, 'channel' => $channel, 'order_status' => $order_status])
                        ->exists();

                        if($channel_order_status_mail){
                            //Get appropriate template
                            $template = DRMParseOrderStatusEmailTemplate($tags, $user_id, $order_status, $channel);
                        }else{
                            //Get appropriate template
                            $template = DRMParseOrderStatusEmailTemplate($tags, $user_id, $order_status);
                        }
                    }

                }else{

                    $channel_order_status_mail = DB::table('order_mail_templates')
                    ->where(['user_id' => $user_id, 'channel' => $channel, 'order_status' => $order_status])
                    ->exists();

                    if($channel_order_status_mail){
                        //Get appropriate template
                        $template = DRMParseOrderStatusEmailTemplate($tags, $user_id, $order_status, $channel);
                    }else{
                        //Get appropriate template
                        $template = DRMParseOrderStatusEmailTemplate($tags, $user_id, $order_status);
                    }
                }

            // }else{
            // //Get appropriate template
            // $template = DRMParseOrderStatusEmailTemplate($tags, $user_id, $order_status);
            // }

            $data['email_to'] = $email_to = $invoice_data['customer']->email;
            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];
            $data['subject'] = $template['subject'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $is_send_invoice = $template['send_invoice'] ?? 0;

            // TODO:: DROPMATIX
            $pdf_stream = $is_send_invoice ? (new \App\Services\Order\InvoiceDoc($order->id))->output() : '';

            //Send mail
            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream, $is_send_invoice, $note_name, $options) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                //Send invoice
                if ($is_send_invoice) {
                    $messages->attachData($pdf_stream, $note_name, [
                        'mime' => 'application/pdf',
                    ]);
                }

                if( isset($options['files']) && !empty($options['files']) )
                {
                    foreach($options['files'] as $file_url)
                    {
                        $messages->attach($file_url);
                    }
                }

                //BCC
                if ($data['bcc']) {
                    $messages->bcc($data['bcc']);
                }

            });

            // if ($paywall) {
            //     MonthlyPaywall::whereNull('paid_at')->whereNull('start_at')->where('id', $paywall->id)->update(['start_at' => now()->addDays(7)]);
            // }
            // //DB::commit();

            try {
                $is_bcc = ($data['bcc']) ? ' BCC: ' . $data['bcc'] : '';
                $message_text = ucfirst(drmHistoryLabel($order_status)) . ' - Email sent to: ' . $email_to . '' . $is_bcc;
                updateOrderHistory($order, 'send_email_' . $order_status, $message_text);
            } catch (Exception $ee) {
            }

            return true;
        } catch (Exception $e) {
            dd($e);
            // //DB::rollBack();
            User::find(71)->notify(new DRMNotification('Send customer invoice error ' . $e->getMessage() . ' Line:' . $e->getLine(), '', '#'));
            return false;
        }
    }
}

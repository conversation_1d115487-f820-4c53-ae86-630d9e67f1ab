<?php 
namespace App\Helper;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;

class LengowApi {

    public $token = '';
    public $account_id = '';
    


    public function __construct($access_token, $secret)
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
          CURLOPT_URL => 'https://api.lengow.io/access/get_token',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS => 'access_token='.$access_token.'&secret='.$secret,
          CURLOPT_HTTPHEADER => array(
            'Content-Type: application/x-www-form-urlencoded'
          ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        $response = json_decode($response, true);


        if(isset($response['token']) && isset($response['account_id']))
        {
            $this->token  = $response['token'];
            $this->account_id  = $response['account_id'];
        }
    }

    public function gettoken(){
        echo $this->token;
    }

    public function getAccount() {
        $client = new Client();
        $url="http://api.lengow.io/v3.0/account/show?account_id=".$this->account_id;
        $headers = [
            'Authorization' => $this->token,
        ];


        try
        {
            $request = $client->get($url,  ['headers'=>$headers]);
        }
        catch (\GuzzleHttp\Exception\RequestException $e)
        {
            // dd($e->getCode());
            return $e->getCode();
            // return false
        }

        
        $response = json_decode($request->getBody());
        dd($response);
    }

    public function getOrder($page=1, $marketplace_order_date_from = '') {
        $client = new Client();
        $page_size=100;
        // $marketplace_order_date_from = '2019-01-01';
        $ordering='marketplace_order_date';
        $url='http://api.lengow.io/v3.0/orders/?account_id='.$this->account_id.'&page='.$page.'&page_size='.$page_size.'&marketplace_order_date_from='.$marketplace_order_date_from.'&ordering='.$ordering;
       //$url="http://api.lengow.io/v3.0/orders/?account_id=".$this->account_id;
        $headers = [
            'Authorization' => $this->token,
        ];        
        $request = $client->get($url,  ['headers'=>$headers]);
        // dd($request->getStatusCode());
        $response = json_decode($request->getBody());
        if($request->getStatusCode() != 200)
        {
            return false;
        }
        // $data['status_code'] = $request->getStatusCode();
        // $data['response'] = $response;
        // dd($response);
        return $response;
    }

    public function getOrderByID($order_id) {
        $client = new Client();
        $page_size=100;
        $url='http://api.lengow.io/v3.0/orders/?account_id='.$this->account_id.'&marketplace_order_id='.$order_id;
        $headers = [
            'Authorization' => $this->token,
        ];        
        $request = $client->get($url,  ['headers'=>$headers]);
        $response = json_decode($request->getBody());
        if($request->getStatusCode() != 200)
        {
            return false;
        }
        return $response;
    }
    
}

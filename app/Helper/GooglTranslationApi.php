<?php 
namespace App\Helper;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use DB;
use Log;
use Google\Cloud\Translate\V2\TranslateClient;
class GooglTranslationApi {

public function Dotranslation ($import_id, $source, $user_lang=array()) {
        //dd($import_id, $source);
        $products = DB::table('drm_products')->where('drm_products.drm_import_id',$import_id)->get();
        //dd($products);
        //   $products = DB::table("drm_products")->select('*')->where('drm_products.drm_import_id',$import_id)->whereNotIn('id',function($query) {
        //       $query->select('drm_products_id')->from('drm_translation');

        // })->get();
        // foreach ($products as $value) {
        //     $translateData[]=DB::table('drm_translation')->where('drm_products_id','!=',$value->id)->first();
        // }
        // dd($translateData);
        
       
            $translate = new TranslateClient(['key' =>'AIzaSyCXmhbZQ5pBqXjKgAKwYQVoXF7FHZEE9FA']);
            $charCount = 0;
            
            foreach($products as $product) {
                
                $names        = ""; 
                $descriptions = "";
                
                $name        = ($product->name=='')?"":$product->name;
                $description = ($product->description=='')?"":$product->description;
                
                $charCount += strlen($name) + strlen($description);
                if($charCount > 95000) { sleep(100); $charCount = 0; }
                
                try{
                
                    foreach($user_lang as $target) {
                        
                        $target_lang = $target;
                        $results = $translate->translateBatch([$name, $description], ['source'=>$source, 'target' =>$target]);
                        $names = $results[0]['text'];
                        $descriptions = $results[1]['text'];
                       // dd($results);
                    }
                    
                }catch(Exception $e){
             
                        dd($e);
                        
                    }finally {
                    
                        if($names == "" or $descriptions == "")
                        {
                            sleep(2);
                            foreach($user_lang as $target) {
                            
                                $target_lang = $target;
                                $results = $translate->translateBatch([$name, $description], ['source'=>$source, 'target' =>$target]);
                                $names = $results[0]['text'];
                                $descriptions = $results[1]['text'];
                            }
                        }
                    
                    
                        $rows['drm_products_id'] = $product->id;
                        $rows['title'] = strip_tags($names);
                        $rows['description'] = strip_tags($descriptions);
                        $convert_lang = DB::table('languages')->select("id")->where("shortcode", $target_lang)->first();
                        $rows['language_id'] = $convert_lang->id;
                        $rows['ean'] = $product->ean;
                       // dd($rows);
                        DB::table('drm_translation')->updateOrInsert(['drm_products_id' => $product->id, 'language_id' => $convert_lang->id], $rows);
            
                    }
                }
            }
}

<?php

namespace App\Helper;

use DB;
use \Hkonnet\LaravelEbay\EbayServices;
use Ebay;
use GuzzleHttp\Client;
use \DTS\eBaySDK\Constants;
use \DTS\eBaySDK\Trading\Services;
use \DTS\eBaySDK\Trading\Types;
use \DTS\eBaySDK\Trading\Enums;
use App\Notifications\DRMNotification;
use App\User;
use Illuminate\Support\Str;

class EbayApi
{
    public $products_id, $auth, $shop_details, $lang, $user_id, $shipping_title;

    function __construct($products_id, $shop_details, $lang, $user_id, $shipping_title = NULL)
    {
        $this->products_id = $products_id;
        $this->shop_details = $shop_details;
        $this->lang = $lang;
        $this->user_id = $user_id;
        $this->shipping_title = $shipping_title;
    }


    public function drmProduct()
    {
        if (count((array) $this->products_id) > 0) {

            $new_products = [];

            $table = "drm_translation_" .$this->lang;

            foreach ($this->products_id as $i => $id) {

                $product = DB::table($table)
                                ->join('drm_products', 'drm_products.id', '=', $table . '.product_id')
                                ->where('drm_products.user_id', $this->user_id)
                                ->whereNull('drm_products.deleted_at')
                                ->select('drm_products.*', 'drm_products.description as descriptionProduct', 'drm_products.name as product_category_nameroduct', 'drm_products.id as product_id', $table . '.description', $table . '.title as name')
                                ->where('drm_products.id', $id)->first();

                $category_ebay = DB::table('drm_product_categories')
                                ->join('drm_category', 'drm_category.id', '=', 'drm_product_categories.category_id')
                                ->join('drm_category_mapping', 'drm_category_mapping.drm_category_id', '=', 'drm_product_categories.category_id')
                                ->select('drm_category_mapping.mapping_id')
                                ->where(['drm_product_categories.product_id' => $id,'shop_id' => $this->shop_details->id])->first();

                $category_id = $category_ebay->mapping_id;
                $product_price = DB::table('drm_product_shop_prices')->where(['drm_product_id' => $id, 'shop_id' => $this->shop_details->id])->first();

                $variables_tag = ["#number", "#ean", "#ek_price", "#vk_price", "#weight", "#size", "#color", "#note", "#production_year", "#brand", "#materials", "#tags","#status", "#gender"];

                foreach ($variables_tag as $key => $variables_tag_value) {
                   if($variables_tag_value == "#number"){$variables_tag_info[$key] = $product->item_number;}
                   if($variables_tag_value == "#ean"){$variables_tag_info[$key] = $product->ean;}
                   if($variables_tag_value == "#ek_price"){$variables_tag_info[$key] = $product->ek_price;}
                   if($variables_tag_value == "#vk_price"){$variables_tag_info[$key] = $product->vk_price;}
                   if($variables_tag_value == "#weight"){$variables_tag_info[$key] = $product->item_weight;}
                   if($variables_tag_value == "#size"){$variables_tag_info[$key] = $product->item_size;}
                   if($variables_tag_value == "#color"){$variables_tag_info[$key] = $product->item_color;}
                   if($variables_tag_value == "#note"){$variables_tag_info[$key] = $product->note;}
                   if($variables_tag_value == "#production_year"){$variables_tag_info[$key] = $product->production_year;}
                   if($variables_tag_value == "#brand"){$variables_tag_info[$key] = $product->brand;}
                   if($variables_tag_value == "#materials"){$variables_tag_info[$key] = $product->materials;}
                   if($variables_tag_value == "#tags"){$variables_tag_info[$key] = $product->tags;}
                   if($variables_tag_value == "#status"){$variables_tag_info[$key] = $product->status;}
                   if($variables_tag_value == "#gender"){$variables_tag_info[$key] = $product->gender;}
                }

                $images = json_decode($product->image);
                if (is_array($images)) {
                    $explode_images = (isset($images[0])) ? $explode_images = getImageArray($images) : "";
                }

                if ($this->shipping_title->shortened == 'on') {
                    $product->title = Str::limit(trim(str_replace($variables_tag, $variables_tag_info, $product->name)),80,'');
                }else{
                    $product->title = trim(str_replace($variables_tag, $variables_tag_info, $product->name));
                }

                $product->description = trim(str_replace($variables_tag, $variables_tag_info, $product->description));
                $product->price = $product_price->vk_price??$product->vk_price;

                $product->tags = str_replace('#', '', $product->tags);
                $product->images = $explode_images;

                /* Start Shipping and Item Level Condition */

                $item_conditions = DB::table('ebay_item_conditions')->where('category_id',$category_id)->first();
                if (!empty($product->status) && collect(explode(',', $item_conditions->condition_ids))->contains($product->status)) {
                    $product->status = (int)$product->status;
                } elseif (empty($product->status) || !collect(explode(',', $item_conditions->condition_ids))->contains($product->status)) {
                    $product->status = 1000;
                }

                $product->shipping_info = array_map('trim', explode('|',$this->shipping_title->ShippingType)) ;

                /* End Shipping and Item Level Condition */

                $final_images = NULL;
                $exist = DB::table('ebay_products')->where(['ean' => $product->ean, 'shop_id' => $this->shop_details->id])->first();

                if(isset($product) && !empty($category_id)){
                    if($exist){
                        $product->item_id = $exist->item_id;
                        $this->updateExportProduct($product, $category_id);

                    }elseif(!$exist && (Str::length($product->title)<=80)){
                        $this->newProductExport($product, $category_id);

                    }
                }

            }


        }
    }

    public function newProductExport($new_products, $category_id)
    {
            $ebay_service = new EbayServices();
            $service = $ebay_service->createTrading();
            $siteId = Constants\SiteIds::DE;
            $request = new Types\AddItemsRequestType();

            $request->RequesterCredentials = new Types\CustomSecurityHeaderType();
            $request->RequesterCredentials->eBayAuthToken = $this->shop_details->password;

            $item = new Types\ItemType();

            $item->Title = $new_products->title;
            $item->Description = $new_products->description;
            $item->Site = "Germany";

            $item->PrimaryCategory = new Types\CategoryType();
            $item->PrimaryCategory->CategoryID = $category_id;

            $item->ItemSpecifics = new Types\NameValueListArrayType();

            $specific = new Types\NameValueListType();
            $specific->Name = 'Language';
            $specific->Value[] = 'Germany';
            $item->ItemSpecifics->NameValueList[] = $specific;

            $specific = new Types\NameValueListType();
            $specific->Name = 'Marke';
            $specific->Value[] = $new_products->brand?:'DRM';
            $item->ItemSpecifics->NameValueList[] = $specific;

            $specific = new Types\NameValueListType();
            $specific->Name = 'Produktart';
            $specific->Value[] = 'Not Found';
            $item->ItemSpecifics->NameValueList[] = $specific;

            if ($new_products->item_weight) {
                $specific = new Types\NameValueListType();
                $specific->Name = 'Artikelgewicht';
                $specific->Value[] = $new_products->item_weight;
                $item->ItemSpecifics->NameValueList[] = $specific;
            }
            if ($new_products->item_size) {
                $specific = new Types\NameValueListType();
                $specific->Name = 'Artikelgröße';
                $specific->Value[] = $new_products->item_size;
                $item->ItemSpecifics->NameValueList[] = $specific;
            }
            if ($new_products->item_color) {
                $specific = new Types\NameValueListType();
                $specific->Name = 'Farbe';
                $specific->Value[] = $new_products->item_color;
                $item->ItemSpecifics->NameValueList[] = $specific;
            }
            if ($new_products->production_year) {
                $specific = new Types\NameValueListType();
                $specific->Name = 'Produktionsjahr';
                $specific->Value[] = $new_products->production_year;
                $item->ItemSpecifics->NameValueList[] = $specific;
            }
            if ($new_products->materials) {
                $specific = new Types\NameValueListType();
                $specific->Name = 'Materialien';
                $specific->Value[] = $new_products->materials;
                $item->ItemSpecifics->NameValueList[] = $specific;
            }
            if ($new_products->gender) {
                $specific = new Types\NameValueListType();
                $specific->Name = 'Geschlecht';
                $specific->Value[] = $new_products->gender;
                $item->ItemSpecifics->NameValueList[] = $specific;
            }

            $specific = new Types\NameValueListType();
            $specific->Name = 'UPC';
            $specific->Value[] = $new_products->ean;
            $item->ItemSpecifics->NameValueList[] = $specific;

            $specific = new Types\NameValueListType();
            $specific->Name = 'Publisher';
            $specific->Value[] = 'DRM';
            $item->ItemSpecifics->NameValueList[] = $specific;

            $specific = new Types\NameValueListType();
            $specific->Name = 'MPN';
            $specific->Value[] = 'Does Not Apply';
            $item->ItemSpecifics->NameValueList[] = $specific;

            // $specific = new Types\NameValueListType();
            // $specific->Name = 'Model';
            // $specific->Value[] = $new_products->ean;
            // $item->ItemSpecifics->NameValueList[] = $specific;

            $specific = new Types\NameValueListType();
            $specific->Name = 'EAN';
            $specific->Value[] = $new_products->ean;
            $item->ItemSpecifics->NameValueList[] = $specific;

            $item->ProductListingDetails = new Types\ProductListingDetailsType();
            $item->ProductListingDetails->EAN  = $new_products->ean;

            $item->PictureDetails = new Types\PictureDetailsType();

            foreach ($new_products->images as $value) {
                if (preg_match('/\.(jpeg|jpg|png|gif)$/i', $value)) {
                    $item->PictureDetails->PictureURL[] = url($value);
                }
            }

            $item->ListingType = Enums\ListingTypeCodeType::C_FIXED_PRICE_ITEM;
            $item->Quantity = (int)$new_products->stock;
            $item->ListingDuration = Enums\ListingDurationCodeType::C_GTC;
            $item->StartPrice = new Types\AmountType(['value' => $new_products->price]);
            //$item->Site = 'https://www.ebay.de/';
            // $item->ListingDuration = '';
            $item->Country = 'DE';
            $item->Currency = 'EUR';
            $item->Location = 'Germany';
            $item->ConditionID = (int)$new_products->status;
            $item->ListingDuration = 'GTC';

            $payment_info = $this->paymentPolicy();

            $item->PaymentMethods[] = $payment_info[0] ?? 'PayPal';
            $user_email = DB::table('cms_users')->where('id',$this->user_id)->first();
            $item->PayPalEmailAddress = $payment_info[1] ?? $user_email->email;

            // dd($new_products->shipping_info);
            if ($new_products->shipping_info[0] == 'Local Pickup') {

                $item->ShipToLocations[] = 'None';
                $item->DispatchTimeMax = 1; //Shipping handlingTime

            } elseif ($new_products->shipping_info[0] == 'DOMESTIC') {

                $item->ShippingDetails = new Types\ShippingDetailsType();
                $item->ShippingDetails->ShippingType = Enums\ShippingTypeCodeType::C_FLAT;

                if (Str::containsAll($new_products->shipping_info[1],['Free Shipping'])) {

                    $shippingService = new Types\ShippingServiceOptionsType();
                    $shippingService->ShippingServicePriority = 1;
                    $shippingService->ShippingService = trim(str_replace('(Free Shipping)','',$new_products->shipping_info[1]));
                    $shippingService->ShippingServiceCost = new Types\AmountType(['value' => (float)$new_products->shipping_info[3] ]);
                    $shippingService->ShippingServiceAdditionalCost = new Types\AmountType(['value' => (float)$new_products->shipping_info[4] ]);
                    $item->ShippingDetails->ShippingServiceOptions[] = $shippingService;

                }else{

                    $shippingService = new Types\ShippingServiceOptionsType();
                    $shippingService->ShippingServicePriority = 1;
                    $shippingService->ShippingService = $new_products->shipping_info[1];
                    $shippingService->ShippingServiceCost = new Types\AmountType(['value' => (float)$new_products->shipping_info[3] ]);
                    $shippingService->ShippingServiceAdditionalCost = new Types\AmountType(['value' => (float)$new_products->shipping_info[4] ]);
                    $item->ShippingDetails->ShippingServiceOptions[] = $shippingService;

                }

                $item->DispatchTimeMax = (int) str_replace('Day','',$new_products->shipping_info[2]); //Shipping handlingTime

            } elseif ($new_products->shipping_info[0] == 'INTERNATIONAL' && !empty($new_products->shipping_info[5])) {

                $item->ShippingDetails = new Types\ShippingDetailsType();
                $item->ShippingDetails->ShippingType = Enums\ShippingTypeCodeType::C_FLAT;

                if (Str::containsAll($new_products->shipping_info[1],['Free Shipping'])) {

                    $shippingService = new Types\InternationalShippingServiceOptionsType();
                    $shippingService->ShippingServicePriority = 1;
                    $shippingService->ShippingService = trim(str_replace('(Free Shipping)','',$new_products->shipping_info[1]));
                    $shippingService->ShippingServiceCost = new Types\AmountType(['value' => (float)$new_products->shipping_info[3] ]);
                    $shippingService->ShippingServiceAdditionalCost = new Types\AmountType(['value' => (float)$new_products->shipping_info[4] ]);
                    $shippingService->ShipToLocation = [$new_products->shipping_info[5]];
                    $item->ShippingDetails->InternationalShippingServiceOption[] = $shippingService;

                }else{

                    $shippingService = new Types\InternationalShippingServiceOptionsType();
                    $shippingService->ShippingServicePriority = 1;
                    $shippingService->ShippingService = $new_products->shipping_info[1];
                    $shippingService->ShippingServiceCost = new Types\AmountType(['value' => (float)$new_products->shipping_info[3] ]);
                    $shippingService->ShippingServiceAdditionalCost = new Types\AmountType(['value' => (float)$new_products->shipping_info[4] ]);
                    $shippingService->ShipToLocation = [$new_products->shipping_info[5]];
                    $item->ShippingDetails->InternationalShippingServiceOption[] = $shippingService;

                }

                $item->DispatchTimeMax = (int) str_replace('Day','',$new_products->shipping_info[2]); //Shipping handlingTime

            } elseif (empty($new_products->shipping_info)) {
                $item->ShipToLocations[] = 'None';
                $item->DispatchTimeMax = 1;  //Shipping handlingTime
            }

            $item->ReturnPolicy = new Types\ReturnPolicyType();
            $item->ReturnPolicy->ReturnsAcceptedOption = 'ReturnsNotAccepted';

            $container = new Types\AddItemRequestContainerType();
            $container->MessageID = '123';
            $container->Item = $item;

            //dd($item);
            $request->AddItemRequestContainer[] = $container;

            $response = $service->addItems($request);


            if ($response->Ack !== 'Failure') {
                $row['user_id'] = $this->user_id;
                $row['shop_id'] = $this->shop_details->id;
                $row['item_id'] = $response->AddItemResponseContainer[0]->ItemID;
                $row['ean'] = $new_products->ean;
                $row['created_at'] = now();
                $ebay_products = DB::table('ebay_products')->where(['ean' => $new_products->ean, 'shop_id' => $this->shop_details->id])->first();
                if($ebay_products){
                    DB::table('ebay_products')->where(['ean' => $new_products->ean, 'shop_id' => $this->shop_details->id])->update($row);
                }elseif(!$ebay_products && $row['item_id']){
                    DB::table('ebay_products')->where(['ean' => $new_products->ean, 'shop_id' => $this->shop_details->id])->insert($row);
                }
            } elseif (isset($response->Errors)) {

                $response_container = $response->AddItemResponseContainer[0];
                if (!empty($response_container)) {
                    User::find($this->user_id)->notify(new DRMNotification('Ebay Export error product ean '. $new_products->ean . $response_container->Errors[0]->LongMessage . $response_container->Errors[0], 'EBAY_API_ERROR', '#'));
                }else{
                    User::find($this->user_id)->notify(new DRMNotification('Ebay Export error product ean '. $new_products->ean . $response->Errors[0]->LongMessage . $response->Errors[0], 'EBAY_API_ERROR', '#'));
                }

            }
            // dd($response);

    }

    public function updateExportProduct($old_products, $category_id)
    {
            $ebay_service = new EbayServices();
            $service = $ebay_service->createTrading();
            $siteId = Constants\SiteIds::DE;
            $request = new Types\ReviseItemRequestType();

            $request->RequesterCredentials = new Types\CustomSecurityHeaderType();
            // $request->RequesterCredentials->eBayAuthToken = Ebay::getAuthToken();
            $request->RequesterCredentials->eBayAuthToken = $this->shop_details->password;

            $item = new Types\ItemType();
            $item->ItemID = $old_products->item_id;
            $item->Title = Str::limit($old_products->title, 80,'');
            $item->Description = $old_products->description;
            $item->ConditionID = (int)$old_products->status;

            $item->PrimaryCategory = new Types\CategoryType();
            $item->PrimaryCategory->CategoryID = $category_id;

            $item->ItemSpecifics = new Types\NameValueListArrayType();

            $specific = new Types\NameValueListType();
            $specific->Name = 'Language';
            $specific->Value[] = 'Germany';
            $item->ItemSpecifics->NameValueList[] = $specific;

            $specific = new Types\NameValueListType();
            $specific->Name = 'EAN';
            $specific->Value[] = $old_products->ean;
            $item->ItemSpecifics->NameValueList[] = $specific;

            $specific = new Types\NameValueListType();
            $specific->Name = 'Marke';
            $specific->Value[] = $old_products->brand?:'DRM';
            $item->ItemSpecifics->NameValueList[] = $specific;

            $specific = new Types\NameValueListType();
            $specific->Name = 'Produktart';
            $specific->Value[] = 'Not Found';
            $item->ItemSpecifics->NameValueList[] = $specific;

            $specific = new Types\NameValueListType();
            $specific->Name = 'UPC';
            $specific->Value[] = $old_products->ean;
            $item->ItemSpecifics->NameValueList[] = $specific;

            if ($old_products->item_weight) {
                $specific = new Types\NameValueListType();
                $specific->Name = 'Artikelgewicht';
                $specific->Value[] = $old_products->item_weight;
                $item->ItemSpecifics->NameValueList[] = $specific;
            }
            if ($old_products->item_size) {
                $specific = new Types\NameValueListType();
                $specific->Name = 'Artikelgröße';
                $specific->Value[] = $old_products->item_size;
                $item->ItemSpecifics->NameValueList[] = $specific;
            }
            if ($old_products->item_color) {
                $specific = new Types\NameValueListType();
                $specific->Name = 'Color';
                $specific->Value[] = $old_products->item_color;
                $item->ItemSpecifics->NameValueList[] = $specific;
            }
            if ($old_products->production_year) {
                $specific = new Types\NameValueListType();
                $specific->Name = 'Produktionsjahr';
                $specific->Value[] = $old_products->production_year;
                $item->ItemSpecifics->NameValueList[] = $specific;
            }
            if ($old_products->materials) {
                $specific = new Types\NameValueListType();
                $specific->Name = 'Materialien';
                $specific->Value[] = $old_products->materials;
                $item->ItemSpecifics->NameValueList[] = $specific;
            }
            if ($old_products->gender) {
                $specific = new Types\NameValueListType();
                $specific->Name = 'Geschlecht';
                $specific->Value[] = $old_products->gender;
                $item->ItemSpecifics->NameValueList[] = $specific;
            }

            $specific = new Types\NameValueListType();
            $specific->Name = 'Publisher';
            $specific->Value[] = 'DRM';
            $item->ItemSpecifics->NameValueList[] = $specific;

            $specific = new Types\NameValueListType();
            $specific->Name = 'MPN';
            $specific->Value[] = 'Does Not Apply';
            $item->ItemSpecifics->NameValueList[] = $specific;

            $item->ProductListingDetails = new Types\ProductListingDetailsType();
            $item->ProductListingDetails->EAN  = $old_products->ean;

            $item->PictureDetails = new Types\PictureDetailsType();
            foreach ($old_products->images as $value) {
                if (preg_match('/\.(jpeg|jpg|png|gif)$/i', $value)) {
                    $item->PictureDetails->PictureURL[] = url($value);
                }
            }

            $item->ListingType = Enums\ListingTypeCodeType::C_FIXED_PRICE_ITEM;
            $item->Quantity = (int)$old_products->stock;
            $item->ListingDuration = Enums\ListingDurationCodeType::C_GTC;
            $item->StartPrice = new Types\AmountType(['value' => $old_products->price]);
            $item->Country = 'DE';
            $item->Location = 'Germany';

            $payment_info = $this->paymentPolicy();

            $item->PaymentMethods[] = $payment_info[0] ?? 'PayPal';
            $user_email = DB::table('cms_users')->where('id',$this->user_id)->first();
            $item->PayPalEmailAddress = $payment_info[1] ?? $user_email->email;

            //$item->AutoPay = false;
            // $item->PaymentMethods[] = 'Discover';
            if ($old_products->shipping_info[0] == 'Local Pickup') {

                $item->ShipToLocations[] = 'None';
                $item->DispatchTimeMax = 1; //Shipping handlingTime

            } elseif ($old_products->shipping_info[0] == 'DOMESTIC') {

                $item->ShippingDetails = new Types\ShippingDetailsType();
                $item->ShippingDetails->ShippingType = Enums\ShippingTypeCodeType::C_FLAT;

                if (Str::containsAll($old_products->shipping_info[1],['Free Shipping'])) {

                    $shippingService = new Types\ShippingServiceOptionsType();
                    $shippingService->ShippingServicePriority = 1;
                    $shippingService->ShippingService = trim(str_replace('(Free Shipping)','',$old_products->shipping_info[1]));
                    $shippingService->ShippingServiceCost = new Types\AmountType(['value' => (float)$old_products->shipping_info[3] ]);
                    $shippingService->ShippingServiceAdditionalCost = new Types\AmountType(['value' => (float)$old_products->shipping_info[4] ]);
                    $item->ShippingDetails->ShippingServiceOptions[] = $shippingService;

                }else{

                    $shippingService = new Types\ShippingServiceOptionsType();
                    $shippingService->ShippingServicePriority = 1;
                    $shippingService->ShippingService = $old_products->shipping_info[1];
                    $shippingService->ShippingServiceCost = new Types\AmountType(['value' => (float)$old_products->shipping_info[3] ]);
                    $shippingService->ShippingServiceAdditionalCost = new Types\AmountType(['value' => (float)$old_products->shipping_info[4] ]);
                    $item->ShippingDetails->ShippingServiceOptions[] = $shippingService;

                }

                $item->DispatchTimeMax = (int) str_replace('Day','',$old_products->shipping_info[2]); //Shipping handlingTime

            } elseif ($old_products->shipping_info[0] == 'INTERNATIONAL' && !empty($old_products->shipping_info[5])) {

                $item->ShippingDetails = new Types\ShippingDetailsType();
                $item->ShippingDetails->ShippingType = Enums\ShippingTypeCodeType::C_FLAT;

                if (Str::containsAll($old_products->shipping_info[1],['Free Shipping'])) {

                    $shippingService = new Types\InternationalShippingServiceOptionsType();
                    $shippingService->ShippingServicePriority = 1;
                    $shippingService->ShippingService = trim(str_replace('(Free Shipping)','',$old_products->shipping_info[1]));
                    $shippingService->ShippingServiceCost = new Types\AmountType(['value' => (float)$old_products->shipping_info[3] ]);
                    $shippingService->ShippingServiceAdditionalCost = new Types\AmountType(['value' => (float)$old_products->shipping_info[4] ]);
                    $shippingService->ShipToLocation = [$old_products->shipping_info[5]];
                    $item->ShippingDetails->InternationalShippingServiceOption[] = $shippingService;

                }else{

                    $shippingService = new Types\InternationalShippingServiceOptionsType();
                    $shippingService->ShippingServicePriority = 1;
                    $shippingService->ShippingService = $old_products->shipping_info[1];
                    $shippingService->ShippingServiceCost = new Types\AmountType(['value' => (float)$old_products->shipping_info[3] ]);
                    $shippingService->ShippingServiceAdditionalCost = new Types\AmountType(['value' => (float)$old_products->shipping_info[4] ]);
                    $shippingService->ShipToLocation = [$old_products->shipping_info[5]];
                    $item->ShippingDetails->InternationalShippingServiceOption[] = $shippingService;

                }

                $item->DispatchTimeMax = (int) str_replace('Day','',$old_products->shipping_info[2]); //Shipping handlingTime

            }

            $item->ReturnPolicy = new Types\ReturnPolicyType();
            $item->ReturnPolicy->ReturnsAcceptedOption = 'ReturnsNotAccepted';

            //dd($item);
            $request->Item = $item;

            $response = $service->reviseItem($request);

            //dd($response);

            if ($response->Ack !== 'Failure') {
                DB::table('ebay_products')->where(['item_id' => $old_products->item_id, 'ean' => $old_products->ean, 'shop_id' => $this->shop_details->id])->update(['updated_at' => now()]);
            } elseif (isset($response->Errors)) {

                $response_container = $response->AddItemResponseContainer[0];
                if (!empty($response_container)) {
                    User::find($this->user_id)->notify(new DRMNotification('Ebay Export error product ean '. $old_products->ean . $response_container->Errors[0]->LongMessage . $response_container->Errors[0], 'EBAY_API_ERROR', '#'));
                }else{
                    User::find($this->user_id)->notify(new DRMNotification('Ebay Export error product ean '. $old_products->ean . $response->Errors[0]->LongMessage . $response->Errors[0], 'EBAY_API_ERROR', '#'));
                }

            }
    }

    public static function ebayProductDelete($shop, $item_ids)
    {
        $delete_item_ids = [];

        foreach ($item_ids as $item_id) {
            $ebay_service = new EbayServices();
            $service = $ebay_service->createTrading();
            $siteId = Constants\SiteIds::DE;
            $request = new Types\EndItemRequestType();
            $request->RequesterCredentials = new Types\CustomSecurityHeaderType();
            $request->RequesterCredentials->eBayAuthToken = $shop->password;

            $request->ItemID = $item_id;
            $request->EndingReason = Enums\EndReasonCodeType::C_NOT_AVAILABLE;
            $response = $service->endItem($request);
            if ($response->Ack == 'Success') {
                $delete_item_ids[] = $item_id;
            }

        }
        DB::table('ebay_products')->whereIn('item_id',$delete_item_ids)->where('shop_id',$shop->id)->delete();

            // dd($response);
    }


    /* Get Ebay Payment Policy Information*/
     public function paymentPolicy()
    {
        $service = new \DTS\eBaySDK\Account\Services\AccountService([
            'authorization' => $this->shop_details->password
        ]);

        $request = new \DTS\eBaySDK\Account\Types\GetPaymentPoliciesByMarketplaceRestRequest();

        $request->marketplace_id = \DTS\eBaySDK\Account\Enums\MarketplaceIdEnum::C_EBAY_DE;

        $response = $service->getPaymentPoliciesByMarketPlace($request);
        $response = json_decode($response,true);

        if ($response['paymentPolicies']) {
            $payment_policies_info = $response['paymentPolicies'][0]['description'];
            $payment_info = array_map('trim',explode(',',$payment_policies_info));
        }

        return $payment_info;

    }

    /* Get Ebay Shipping Information Information*/
    public function shippingInformation()
    {
        $service = new \DTS\eBaySDK\Account\Services\AccountService([
            'authorization' => $this->shop_details->password
        ]);

        $request = new \DTS\eBaySDK\Account\Types\GetFulfillmentPoliciesByMarketplaceRestRequest();

        $request->marketplace_id = \DTS\eBaySDK\Account\Enums\MarketplaceIdEnum::C_EBAY_DE;

        $response = $service->getFulfillmentPoliciesByMarketPlace($request);
        $response = json_decode($response,true);

        if ($response['fulfillmentPolicies']) {
            $shipping_info['freeShipping'] = $response['fulfillmentPolicies'][0]['shippingOptions'][0]['shippingServices'][0]['freeShipping'];
            $shipping_info['name'] = $response['fulfillmentPolicies'][0]['shippingOptions'][0]['shippingServices'][0]['shippingServiceCode'];
            $shipping_info['cost'] = $response['fulfillmentPolicies'][0]['shippingOptions'][0]['shippingServices'][0]['shippingCost']['value'];
            $shipping_info['additionalCost'] = $response['fulfillmentPolicies'][0]['shippingOptions'][0]['shippingServices'][0]['additionalShippingCost']['value'];
        }

        return $shipping_info;

    }

    /* Get Ebay Shipping Information Information Before Export*/
    public static function shippingInformationBeforeExport($shop_details)
    {
        $service = new \DTS\eBaySDK\Account\Services\AccountService([
            'authorization' => $shop_details->password
        ]);

        $request = new \DTS\eBaySDK\Account\Types\GetFulfillmentPoliciesByMarketplaceRestRequest();

        $request->marketplace_id = \DTS\eBaySDK\Account\Enums\MarketplaceIdEnum::C_EBAY_DE;

        $response = $service->getFulfillmentPoliciesByMarketPlace($request);
        //$response = json_decode($response, true);
        
        $shipping = [];

        foreach ($response->fulfillmentPolicies as $fulfillmentPolicie) {
            if ($fulfillmentPolicie->localPickup == true) {
                $shipping[] = 'Local Pickup';
            }
            foreach ($fulfillmentPolicie->shippingOptions as $shipping_options) {
                if ($shipping_options->shippingServices[0]->freeShipping == true) {
                    $shipping[] = $shipping_options->optionType . ' | ' . $shipping_options->shippingServices[0]->shippingServiceCode . ' (Free Shipping) | ' . $fulfillmentPolicie->handlingTime->value . ' Day | ' .$shipping_options->shippingServices[0]->shippingCost->value . ' | ' . $shipping_options->shippingServices[0]->additionalShippingCost->value;
                } elseif ($shipping_options->optionType == 'INTERNATIONAL') {
                    //$shipping[] = $shipping_options->optionType . ' | ' . $shipping_options->shippingServices[0]->shippingServiceCode . ' | ' . $fulfillmentPolicie->handlingTime->value . ' Day | ' . $shipping_options->shippingServices[0]->shippingCost->value . ' | ' . $shipping_options->shippingServices[0]->additionalShippingCost->value . ' | ' . collect($shipping_options->shippingServices[0]->shipToLocations->regionIncluded)->pluck('regionName')->implode(',');
                } else {
                    $shipping[] = $shipping_options->optionType . ' | ' . $shipping_options->shippingServices[0]->shippingServiceCode . ' | ' . $fulfillmentPolicie->handlingTime->value . ' Day | ' . $shipping_options->shippingServices[0]->shippingCost->value . ' | ' . $shipping_options->shippingServices[0]->additionalShippingCost->value;
                }
            }
        }

        return $shipping;
    }

    public function shippingDetails()
    {
        /**
         * Setting up the shipping details.
         * We will use a Flat shipping rate for both domestic and international.
         */
        $item->ShippingDetails = new Types\ShippingDetailsType();
        $item->ShippingDetails->ShippingType = Enums\ShippingTypeCodeType::C_FLAT;

        /**
         * Create our first domestic shipping option.
         * Offer the Economy Shipping (1-10 business days) service at $2.00 for the first item.
         * Additional items will be shipped at $1.00.
         */
        $shippingService = new Types\ShippingServiceOptionsType();
        $shippingService->ShippingServicePriority = 1;
        $shippingService->ShippingService = 'Other';
        $shippingService->ShippingServiceCost = new Types\AmountType(['value' => 2.00]);
        $shippingService->ShippingServiceAdditionalCost = new Types\AmountType(['value' => 1.00]);
        $item->ShippingDetails->ShippingServiceOptions[] = $shippingService;

        /**
         * Create our second domestic shipping option.
         * Offer the USPS Parcel Select (2-9 business days) at $3.00 for the first item.
         * Additional items will be shipped at $2.00.
         */
        $shippingService = new Types\ShippingServiceOptionsType();
        $shippingService->ShippingServicePriority = 2;
        $shippingService->ShippingService = 'USPSParcel';
        $shippingService->ShippingServiceCost = new Types\AmountType(['value' => 3.00]);
        $shippingService->ShippingServiceAdditionalCost = new Types\AmountType(['value' => 2.00]);
        $item->ShippingDetails->ShippingServiceOptions[] = $shippingService;

        /**
         * Create our first international shipping option.
         * Offer the USPS First Class Mail International service at $4.00 for the first item.
         * Additional items will be shipped at $3.00.
         * The item can be shipped Worldwide with this service.
         */
        $shippingService = new Types\InternationalShippingServiceOptionsType();
        $shippingService->ShippingServicePriority = 1;
        $shippingService->ShippingService = 'USPSFirstClassMailInternational';
        $shippingService->ShippingServiceCost = new Types\AmountType(['value' => 4.00]);
        $shippingService->ShippingServiceAdditionalCost = new Types\AmountType(['value' => 3.00]);
        $shippingService->ShipToLocation = ['WorldWide'];
        $item->ShippingDetails->InternationalShippingServiceOption[] = $shippingService;

        /**
         * Create our second international shipping option.
         * Offer the USPS Priority Mail International (6-10 business days) service at $5.00 for the first item.
         * Additional items will be shipped at $4.00.
         * The item will only be shipped to the following locations with this service.
         * N. and S. America
         * Canada
         * Australia
         * Europe
         * Japan
         */
        $shippingService = new Types\InternationalShippingServiceOptionsType();
        $shippingService->ShippingServicePriority = 2;
        $shippingService->ShippingService = 'USPSPriorityMailInternational';
        $shippingService->ShippingServiceCost = new Types\AmountType(['value' => 5.00]);
        $shippingService->ShippingServiceAdditionalCost = new Types\AmountType(['value' => 4.00]);
        $shippingService->ShipToLocation = [
            'Americas',
            'CA',
            'AU',
            'Europe',
            'JP'
        ];
        $item->ShippingDetails->InternationalShippingServiceOption[] = $shippingService;

    }


    /* Get Ebay Store*/
    public static function getStore($shop_details)
    {
            $ebay_service = new EbayServices();
            $service = $ebay_service->createTrading();
            $siteId = Constants\SiteIds::DE;
            $request = new Types\GetStoreRequestType();

            $request->RequesterCredentials = new Types\CustomSecurityHeaderType();
            $request->RequesterCredentials->eBayAuthToken = $shop_details;

            $response = $service->getStore($request);
            return json_decode($response,true);
    }

    /* Ebay Refresh User Access Token*/

    public static function refreshUserToken($shop_detail)
    {

        if (env('EBAY_PROD_RuName')) {

            $service = new \DTS\eBaySDK\OAuth\Services\OAuthService([
                'credentials' => Ebay::getConfig()['credentials'],
                'ruName'      => env('EBAY_PROD_RuName'),
                'sandbox'     => false
            ]);

            $request = new \DTS\eBaySDK\OAuth\Types\RefreshUserTokenRestRequest();
            $request->refresh_token = $shop_detail->username;
            $request->scope = [
                'https://api.ebay.com/oauth/api_scope/sell.account',
                'https://api.ebay.com/oauth/api_scope/sell.inventory'
            ];

            $response = $service->refreshUserToken($request);
            $response = json_decode($response);

        } else {
            echo 'Not Acceptable';
        }

        if (!empty($response->access_token)) {
            \App\Shop::where(['id'=>$shop_detail->id,'user_id'=>$shop_detail->user_id])->update([
                        'password' => $response->access_token,
                        'updated_at' => now()
                    ]);
        }
    }


    /* Ebay Set Store Category Create Process*/
    public function category($category_name)
    {
            $ebay_service = new EbayServices();
            $service = $ebay_service->createTrading();
            $request = new Types\SetStoreCategoriesRequestType();

            $request->RequesterCredentials = new Types\CustomSecurityHeaderType();
            $request->RequesterCredentials->eBayAuthToken =  $this->shop_details->password;
            $request->Action = 'Add';
            // $request->ItemDestinationCategoryID = 0;
            // $request->DestinationParentCategoryID = 0;
            $request->StoreCategories = new Types\StoreCustomCategoryArrayType();
            $category = new Types\StoreCustomCategoryType();
            $category->Name = $category_name;
            $category->Order = 1;
            $request->StoreCategories->CustomCategory[] = $category;
            $request->StoreCategories->CustomCategory[] = $category;
            $response = $service->SetStoreCategories($request);

            // if($response->Status == 'Failed'){
            //     echo json_decode($response->CustomCategory)->CustomCategory[0]->CategoryID;
            // }
    }

    /* Ebay Set Store Category Edit Process*/
    public function editStoreCategory()
    {
            $ebay_service = new EbayServices();
            $service = $ebay_service->createTrading();
            $siteId = Constants\SiteIds::DE;
            $request = new Types\SetStoreCategoriesRequestType();
            $request->RequesterCredentials = new Types\CustomSecurityHeaderType();
            $request->RequesterCredentials->eBayAuthToken = $this->shop_details->password;

            $request->Action = 'Rename';
            // $request->ItemDestinationCategoryID = 20081;
            // $request->DestinationParentCategoryID = 14125;
            $request->StoreCategories = new Types\StoreCustomCategoryArrayType();
            $category = new Types\StoreCustomCategoryType();
            $category->CategoryID = 72448567013;
            $category->Name = 'Baby>Spielzeug';
            $category->Order = 1;
            $request->StoreCategories->CustomCategory[] = $category;

            $response = $service->SetStoreCategories($request);
            // dd($response);
    }

    /* Ebay Set Store Category Delete Process*/
    public function deleteStoreCategory($category_id){
            $ebay_service = new EbayServices();
            $service = $ebay_service->createTrading();
            $request = new Types\SetStoreCategoriesRequestType();
            $request->RequesterCredentials = new Types\CustomSecurityHeaderType();
            $request->RequesterCredentials->eBayAuthToken =  $this->shop_details->password;
            $request->Action = 'Delete';
            $request->StoreCategories = new Types\StoreCustomCategoryArrayType();
            $category = new Types\StoreCustomCategoryType();
            $category->CategoryID = $category_id;
            $request->StoreCategories->CustomCategory[] = $category;
            $response = $service->SetStoreCategories($request);
            // dd($response);
    }

}

<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;
use App\DropfunnelTag;
use App\EmailMarketing;
use Illuminate\Support\Facades\DB;

use App\Services\Mailgun\MailgunOptions;
use App\Services\Mailgun\Dropfunel;
use App\MailGunWebHookHistory;
use Illuminate\Container\Container;
use Illuminate\Contracts\Mail\Factory as MailFactory;

class DRMEmailMarketingMailable extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels, MailgunOptions, Dropfunel;

    public $connectionName = 'dropfunnel';
    public $timeout = 1140;
    public $tries = 2;
    public $retryAfter = 3;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    protected $body;
    public $subject;
    public $mail_from;
    protected $info;
    protected $campaign_id;
    protected $lastStep = false;

    public function __construct($info)
    {
        $this->body=$info['body'];
        $this->subject=$info['subject'];
        $this->mail_from= (isset($info['from']) && $info['from'])? $info['from'] : null;
        $this->lastStep = (isset($info['last_step']) && $info['last_step']) ? $info['last_step'] : $this->lastStep;
        $this->info = $info;
        $this->campaign_id = $info['campaign_id'];
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $this->subject($this->subject);

        $data = $this->info;

        //Tracking email open and clicks
        $this->df_track();

        //add email tag
        $this->df_tags('drm_dropfunnel_email');

        $newData = [
            'user_id' => $data['user_id'],
            'customer_id' => $data['customer_id'],
            'campaign_name' => $data['campaign_name'],
            'campaign_id' => $data['campaign_id'],
            'email' => $data['email'],
        ];

        if ($this->lastStep) {
            $newData['last_step'] = 'main_step_mail';
        }
        //send variables
        $this->df_variables($newData);

        // if($this->mail_from){
        //     $this->from($this->mail_from);
        // }

        $customSmtp = false;
        // CSA SMTP
        if( $this->isCsaSender($this->mail_from) )
        {
            $this->from($this->mail_from);
            $this->mailer('campaign');
            $this->setHeader('X-CSA-Complaints', '<EMAIL>');
            $customSmtp = true;
        }
        else if($this->isDtSender($this->mail_from)){
            $this->from(config('mail.dt'));
    
        } 
        else if($this->mail_from && ($data['user_id'] == 3721)) { // Franz: dubai-rendite.com
            $this->from($this->mail_from);
            $this->mailer('dropmatix_mailgun');
        }else{
            $this->from(config('mail.drm'));
        }

        // Custom SMTP content
        if($customSmtp)
        {
            if(isset($data['unsubscribe_url']) && $unsubscribe_url = $data['unsubscribe_url'])
            {
                $this->setHeader('List-Unsubscribe-Post', 'List-Unsubscribe=One-Click');
                $this->setHeader('List-Unsubscribe', '<'.$unsubscribe_url.'>'); 
            }

            $this->body .= $this->trackingImage($newData);
        }

        return $this->html($this->body);
    }


    /**
     * Send the message using the given mailer.
     *
     * @param  \Illuminate\Contracts\Mail\Factory|\Illuminate\Contracts\Mail\Mailer  $mailer
     * @return void
     */
    public function send($mailer)
    {
        try{

            //Check already send
            $data = $this->info ?? [];
            $customer_id = $data['customer_id'];
            $campaign_id = $data['campaign_id'];

             // Calculate the datetime 20 minutes ago
             $twentyMinutesAgo = Carbon::now()->subMinutes(20);

             $checkProcess = DB::table('temp_campaign_process')
                 ->where('campaign_id', $campaign_id)
                 ->where('customer_id', $customer_id)
                 ->whereTime('created_at', '>=', $twentyMinutesAgo->toTimeString())
                 ->exists();
             
             if($checkProcess){
                 throw new \Exception('Campaign email previous process not completed yet!');
             }else{
                 $this->insertTempMailData($data);
             }

            usleep(100);
            if( DB::table('campaign_history')->where('campaign_id', $campaign_id)->where('customer_id', $customer_id)->exists() ){
                throw new \Exception('Campaign email try to resend again!');
            }
            //End check already send

            // Check mail validation
            if(!$this->emailValidate($data)) {
                throw new \Exception('Invalid customer email!');
            }

            return $this->withLocale($this->locale, function () use ($mailer, $data) {
                Container::getInstance()->call([$this, 'build']);

                $mailer = $mailer instanceof MailFactory
                    ? $mailer->mailer($this->mailer)
                    : $mailer;

                $callback = $mailer->send($this->buildView(), $this->buildViewData(), function ($message) {
                    $this->buildFrom($message)
                        ->buildRecipients($message)
                        ->buildSubject($message)
                        ->runCallbacks($message)
                        ->buildAttachments($message);
                });

                $this->campaignHistoryData();
                $this->removeTempMailData($this->info);

                if (isset($this->info['for_campus_user']) && $this->info['for_campus_user'] == 1) {
                    $this->removeCampusUserPassword($this->info);
                }

                return $callback;
            });
        }catch(\Exception $e){
            $this->df_exception($this->campaign_id, $e);
        }
    }

    //Tracking email
    protected function campaignHistoryData(){
        $data = $this->info;

        $tags = $data['tags'] ?? [];

        $user_id = $data['user_id'];
        $customer_id = $data['customer_id'];

        $campaign_name = $data['campaign_name'];
        $campaign_id = $data['campaign_id'];

        if(empty($campaign_id)) return;

        try{

            //Insert tag to customer profile
            $customer_tags =  DropfunnelTag::whereIn('id', $tags)->pluck('tag', 'id')->toArray();
            if($customer_tags){
                $user_history = $user_historycheck = [];

                $user_historycheck['campaign_id'] = $campaign_id;
                $user_historycheck['customer_id'] = $customer_id;
                $user_historycheck['user_id'] = $user_id;

                $user_history['tag'] = json_encode($customer_tags);
                $user_history['created_at'] = Carbon::now();
                $user_history['updated_at'] = Carbon::now();
                $user_history['campaigen_name'] = $campaign_name;

                $user_historycheck = array_filter($user_historycheck);
                if($user_historycheck){
                    DB::table('dropfunnel_email_sent_histories')->updateOrInsert($user_historycheck, $user_history);
                }
            }
        }catch(\Exception $e){
            $this->df_exception($this->campaign_id, $e);
        }

        try{
            $campaign_data = [];
            $campaign_data['campaign_id'] = $campaign_id;
            $campaign_data['customer_id'] = $customer_id;
            $campaign_data['user_id'] = $user_id;
            $campaign_data = array_filter($campaign_data);

            if($campaign_data && !DB::table('campaign_history')->where($campaign_data)->exists() ){
                $campaign_data['created_at'] = Carbon::now();
                $campaign_data['updated_at'] = Carbon::now();
                DB::table('campaign_history')->insert($campaign_data);
            }

        }catch(\Exception $e){
            $this->df_exception($this->campaign_id, $e);
        }

        try{
            EmailMarketing::find($campaign_id)->increment('completed');
        }catch(\Exception $e){
            $this->df_exception($this->campaign_id, $e);
        }

        // Mailer Campaign
        try {
            if($this->mailer === 'campaign' || ($data['user_id'] == 3721))
            {
                $payload = $this->info;
                $insert_data = [
                    'delivered' => now()->format('Y-m-d H:i:s'),
                    'customer_email' => $payload['email'],
                ];
                $check_arr = [
                    'user_id' => $payload['user_id'],
                    'customer_id' => $payload['customer_id'],
                    'campaign_id' => $payload['campaign_id'],
                ];
                if(!(MailGunWebHookHistory::where($check_arr)->exists()))
                {
                    MailGunWebHookHistory::create(array_merge($check_arr, $insert_data));
                }
            }
        } catch(Exception $e) {}
    }


    public function tags()
    {
        $data = $this->info;
        $user_id = $data['user_id'];
        $customer_id = $data['customer_id'];
        $campaign_name = $data['campaign_name'];
        $campaign_id = $data['campaign_id'];
        return ['Campaign ID: '.$campaign_id, 'User ID: '.$user_id, 'Customer ID:'.$customer_id, 'Campaign Name: '. $campaign_name];
    }

    public function insertTempMailData($data){

        try{

            $customer_id = $data['customer_id'];
            $campaign_id = $data['campaign_id'];

            DB::table('temp_campaign_process')->updateOrInsert([
                'customer_id'=> $customer_id,
                'campaign_id'=> $campaign_id
            ],[
                'user_id' => $data['user_id'],
                'created_at'=> Carbon::now(),
                'updated_at'=> Carbon::now()
            ]);

        }catch(Exception $e){
           
        }
        
    }

    public function removeTempMailData($data){
        
        try{
            DB::table('temp_campaign_process')
            ->where('campaign_id', $data['campaign_id'])
            ->where('customer_id', $data['customer_id'])
            ->delete();
         }catch(Exception $e){

         }
       
    }

    public function removeCampusUserPassword($data){
        try {
            DB::table('drop_campus_tariff_user')
                ->where('drm_user_id', $data['user_id'])
                ->update([
                    'password' => null,
                    'updated_at' => now(),
                ]);
         } catch(\Exception $e) {

         }
    }
}

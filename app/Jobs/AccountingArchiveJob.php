<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AccountingArchiveJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $connectionName = 'file-archive';
    public $timeout = 7200;
    public $tries = 2;
    public $retryAfter = 7280;
    public $ids = [];
    public $user;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user, $ids)
    {
        $this->ids = $ids;
        $this->user = $user;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        ini_set("memory_limit",-1);
        app('App\Http\Controllers\AdminUpcomingInvoicesController')->archiveInvoices($this->user, $this->ids);

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        return ['Accounting Invoice Archive: '.$this->user->name ];
    }
}

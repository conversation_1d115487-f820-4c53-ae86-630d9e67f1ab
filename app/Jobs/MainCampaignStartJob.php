<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\EmailMarketing;
use App\Traits\EmailCampaignTrait;

class MainCampaignStartJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, EmailCampaignTrait;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $emailMarketing;

    public function __construct(EmailMarketing $emailMarketing)
    {
        $this->emailMarketing = $emailMarketing;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $this->sendDropFunnelEmail($this->emailMarketing);

        } catch(\Exception $e) {}
    }
}

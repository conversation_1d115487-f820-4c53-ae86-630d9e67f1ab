<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use DB;
use Exception;
use Carbon\Carbon;
use Log;
use App\Services\ProductApi\Webhook\Countdown;
use App\Services\ProductApi\Webhook\Googleshopping;
use App\Services\ProductApi\Webhook\Rainforest;
use App\Http\Controllers\CPAnalysisWebhookController;
use App\Services\ProductApi\Services\Countdown as ServicesCountdown;
use App\Services\ProductApi\Services\GoogleShopping as ServicesGoogleShopping;
use App\Services\ProductApi\Services\Rainforest as ServicesRainforest;

class CPAnalysisWebhook implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $collection_data;
    protected $type;

    public function __construct($collection , $type)
    {
        $this->collection_data = $collection;
        $this->type = $type;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        if ($this->type == 'Countdown') {
            $driver = new Countdown;
            $service = new ServicesCountdown;
        }
        else if($this->type == 'Googleshopping'){
            $driver = new Googleshopping;
            $service = new ServicesGoogleShopping;
        } else {
            $driver = new Rainforest;
            $service = new ServicesRainforest;
        }
        app('App\Http\Controllers\CPAnalysisWebhookController')->processWebhook($this->collection_data, $driver, $service, $this->type);

        } catch(\Exception $e) {}
    }

}


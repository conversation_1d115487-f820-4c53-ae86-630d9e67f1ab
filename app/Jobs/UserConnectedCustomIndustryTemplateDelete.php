<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UserConnectedCustomIndustryTemplateDelete implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $ids;
    protected $user_id;

    public function __construct($ids, $user_id)
    {
        $this->ids = $ids;
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app('App\Services\CustomIndustryTemplateService')->userConnTempProductsDelete($this->ids, $this->user_id);

        } catch(\Exception $e) {}
    }

    public function tags()
    {   
        return ['Starting Connceted Custom Industry Template Delete User - '.$this->user_id];
    }
}

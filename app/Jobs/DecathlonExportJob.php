<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use App\Models\ChannelProduct;
use League\Csv\Writer;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use App\Notifications\DRMNotification;
use Storage;
use App\User;

class DecathlonExportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected array $product_ids;
    protected int $user_id;
    protected int $payload_id;

    public function __construct($product_ids, $user_id, $payload_id)
    {
        $this->product_ids = $product_ids;
        $this->user_id = $user_id;
        $this->payload_id = $payload_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $this->start($this->user_id);

        } catch(\Exception $e) {}
    }

    public function start($user_id)
    {
        try {

            $all_products = new Collection();

            foreach (array_chunk($this->product_ids, 1) as $items) {
                $products = ChannelProduct::whereIntegerInRaw('id', $items)->get();
                $all_products = $all_products->merge($products);
            }

            $table_column = $this->table_column();
            $csv_cols = array_keys($table_column);

            $file = new \SplTempFileObject();
            $file->setFlags(\SplFileObject::READ_CSV);
            $file->setCsvControl(',');
            $csv = Writer::createFromFileObject($file);

            $header = [];
            foreach($table_column as $key => $value){
                $header[] = $value['label'];
            }
            $csv->insertOne($header);

            $all_rows = collect([]);

            $rows = $all_products->map(function($item) use ($user_id) {

                $row  = [
                    'id' => $item->id,
                    'item_number' => $item->item_number,
                    'ean' => $item->ean,
                    'id_type' => 'EAN',
                    'vk_price' => $item->vk_price,
                    'status' => 11,
                    'stock' => $item->stock,
                    'stock_of_status' => json_decode($item->stock_of_status, true)['1000']
                ];

                return $row;
            })
            ->map(function($r) use ($csv_cols, $file_ext) {
                $csv_r = [];

                $html_tr = '';
                foreach($csv_cols as $c)
                {
                    $c_val = $r[$c] ?? null;

                    if ($c == 'vk_price') {

                        $c_val = $c_val ? number_format((float)$c_val, 2) : 0;

                        $c_val = $c_val * 1.19;

                    }elseif ($c =='ean') {

                        $c_val = $r['ean'];

                    }elseif ($c == 'item_number') {

                        $c_val = $c_val ? $c_val : '...';

                    }elseif($c == 'stock'){
                        $c_val = $r['stock_of_status'] ?? $r['stock'];
                        $c_val = $c_val ?? 0;
                    }

                    if(!isset($csv_r[$c]))
                    {
                        $csv_r[$c] = $c_val;
                    }

                }

                return array_merge(array_flip($csv_cols), $csv_r);
            });

            $csv->insertAll($rows);
            $all_rows = $rows;

            $file_name = \Str::random(40).'.'.$file_ext;
            $path = 'cp-export-file/'.$file_name;
            $time = date('Y-m-d H:i:s');

            $file_url = [];

            $spreadsheet = new Spreadsheet();
            $rows_array = $all_rows->toArray();

            // Get sheet dimension
            $sheet_dimension = $spreadsheet->getActiveSheet()->calculateWorksheetDimension();
            $spreadsheet->getActiveSheet()->getStyle($sheet_dimension)->getNumberFormat()->setFormatCode('@');

            $sheet = $spreadsheet->getActiveSheet();

            $first_row = $all_rows->first();
            if($first_row)
            {
                array_unshift($rows_array, $header);
            }

            $sheet->fromArray($rows_array, NULL, 'A1');
            $writer = new Xlsx($spreadsheet);

            ob_start();
            $writer->save('php://output');
            $content = ob_get_contents();
            ob_end_clean();

            $url = Storage::disk('spaces')->put($path, $content, 'public');
            $file_url[] = Storage::disk('spaces')->url($path);

            DB::table('decathlon_export_requests')->where('id', '=', $this->payload_id)->update(['url' => json_encode($file_url), 'updated_at' => now()]);

            $link = url('admin/jobs?action=decathlon-export&target='.$this->payload_id);
            $message = 'Decathlon export file '.$time.' completed.';
            User::find($user_id)->notify(new DRMNotification($message, '', $link));

        }catch(Exception $e) {
            DB::table('decathlon_export_requests')->where('id', '=', $this->payload_id)->update(['last_error' => $e, 'updated_at' => now()]);
        }
    }

    public function table_column(){
        return [
            'id'                    => ["label" => "id" , "sorting" => true],
            'item_number'           => ["label" => "sku" , "sorting" => true],
            'ean'                   => ["label" => "product-id" , "sorting" => false],
            'id_type'               => ["label" => "product-id-type" , "sorting" => true],
            'vk_price'              => ["label" => "price" , "sorting" => true],
            'stock'                 => ["label" => "stock" , "sorting" => true],
            'status'                => ["label" => "state" , "sorting" => true],
        ];
    }
}

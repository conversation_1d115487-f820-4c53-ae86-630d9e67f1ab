<?php

namespace App\Jobs;

use App\Helper\GambioApi;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use DB;
use App\Events\ExportProgressEvent;
use Illuminate\Support\Facades\Session;
use App\Jobs\GambioCategorySync;
use App\Services\Modules\Export\Gambio\GambioApiService;

class DrmExport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $ids;
    protected $user_id;
    protected $shop_details;
    protected $langarray;
    protected $total_products;
    protected $export_process_id;
    public $timeout = 0;
    public $tries = 3;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($ids, $shop_details, $langarray, $user_id, $export_process_id = NULL, $total_product = NULL)
    {
        $this->ids = $ids;
        $this->user_id = $user_id;
        $this->shop_details = $shop_details;
        $this->langarray=$langarray;
        $this->total_products = $total_product;
        $this->export_process_id = $export_process_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $gambioApi = new GambioApiService($this->shop_details,$this->user_id);
        $gambioApi->export($this->ids);
        if (count($this->ids) == 1) {
            GambioApiService::GambioImageUploadArray($this->shop_details, $this->ids);
        }

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        $user = DB::table('cms_users')->where('id',$this->shop_details->user_id)->first();

        return [$this->shop_details->shop_name .' Shop - '.$user->name];
    }
}

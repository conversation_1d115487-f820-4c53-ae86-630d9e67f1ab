<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use App\DrmProduct;

class ProcessDRMBulkTaxTypeUpdate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $user_id;
    protected $product_ids;
    protected $tax_type;

    public function __construct($user_id, $product_ids, $tax_type)
    {
        $this->user_id = $user_id;
        $this->product_ids = $product_ids;
        $this->tax_type = $tax_type;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $chunks = array_chunk($this->product_ids, 1000);
        foreach ($chunks as $chunk) {
            DrmProduct::whereIn('id', $chunk)->update([
                'tax_type' => $this->tax_type
            ]);
        }

        } catch(\Exception $e) {}
    }
}

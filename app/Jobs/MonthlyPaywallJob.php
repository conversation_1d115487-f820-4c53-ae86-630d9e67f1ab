<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MonthlyPaywallJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 1140;
    public $tries = 2;
    public $retryAfter = 3;
    public $paywall_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($paywall_id)
    {
        $this->paywall_id = $paywall_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        ini_set("memory_limit", -1);
        app('App\Http\Controllers\AdminMonthlyPaywallsController')->sendMonthlyPaywall($this->paywall_id);

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        return ['Monthly Paywall: '.$this->paywall_id];
    }
}

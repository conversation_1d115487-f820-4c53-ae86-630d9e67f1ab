<?php

namespace App\Jobs;

use App\Models\Product\AnalysisProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\MarketplaceProducts;
use App\Services\ProductApi\ProductApi;
use App\Models\Product\AnalysisBuyingChoices;
use App\Services\DRM\CurrencyApi;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\ProductPriceApi;

class ProcessTransferToCPBuyingChoices implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $user_id;
    protected $product_ids;

    public function __construct($user_id, $product_ids)
    {
        $this->user_id = $user_id;
        $this->product_ids = $product_ids;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $analysisProducts = AnalysisProduct::whereIn('id', $this->product_ids)->select('id', 'ean', 'amazon_also_bought')->get();
        $data = [];

        foreach($analysisProducts as $item){
            if(empty(json_decode($item->amazon_also_bought))){
                $item->amazon_also_bought = $this->updateProduct($item->ean);
            }
            // $data[] = ['analysis_product_id' => $item->id, 'user_id' => $this->user_id];
            $buying_choices = json_decode($item->amazon_also_bought) ?? [];
            foreach($buying_choices as $choice){
                $asin = $choice->asin;
                $dbEan = DB::table('amazon_asin_collections')->where('asin', $asin)->where('domain', 3)->first();
                $ean = '';
                if($dbEan){
                    $ean = $dbEan->ean;
                }
                else{
                    $ean = $this->convertASIN($asin);
                }

                $choice->ean = $ean;

                if($choice->price){
                    $price = 0;
                    if($choice->price->currency === 'EUR' || $choice->price->symbol === "€"){
                        $price = $choice->price->value;
                    }
                    else if($choice->price->currency && $choice->price->value){
                        $price = app(CurrencyApi::class)->convertToEUR($choice->price->value, $choice->price->currency);
                    }
                    $choice->price->currency = 'EUR';
                    $choice->price->value = $price;
                }
            }
            AnalysisProduct::where('id', $item->id)->update([
                'amazon_also_bought' => json_encode($buying_choices)
            ]);
            AnalysisBuyingChoices::where('analysis_product_id', $item->id)->update([
                'loading' => 0
            ]);
        }

        } catch(\Exception $e) {}
    }

    public function convertASIN($asin){

        $domain = "amazon.de";

        $productApi = new ProductApi($domain);
        $gtinData = $productApi->convertASIN($asin, "asin_to_gtin")->fetchEANFromASIN();

        if($gtinData->asinConversionData()){
            $gtinData = $gtinData->asinConversionData();
        }
        if(empty($gtinData)){
            return null;
        }else{
            foreach($gtinData as $data){
                if($data->format == 'EAN-13'){
                    DB::table('amazon_asin_collections')->updateOrInsert(
                        [
                            'ean' => $data->value,
                            'domain' => 3
                        ],
                        [
                            'asin' => $asin
                        ]
                    );
                    return $data->value;
                }
            }
        }
        return null;
    }

    public function updateProduct($ean){

        $domain = 'amazon.de';
        $domain_column = 'amazon';
        $price_column = $domain_column.'_price';
        $rating_column = $domain_column.'_rating';
        $rating_count_column = $domain_column.'_rating_count';
        $product_number_column = $domain_column.'_product_number';
        $last_sync_column = $domain_column.'_last_sync';
        $also_bought_column = $domain_column.'_also_bought';

        $productApi = new ProductApi($domain);
        $product = $productApi->search('ean', $ean)->fetch();

        $price = 0;
        $rating = 0;
        $isPrime = false;

        if($product->price() || $product->offerPrice() || $product->rating() || $product->productNumber() || $product->ratingCount() || $product->title() || $product->sellerName() || $product->sellerLink() || $product->isPrime() || $product->getOtherSeller()){

            if($product->price()){
                $price = $product->price();
            }

            if($product->title()){
                $title = $product->title();
            }

            // elseif($product->offerPrice()){
            //     $price = $product->price();
            // }

            if($product->rating()){
                $rating = $product->rating();
            }

            if($product->productNumber()){
                $productNumber = $product->productNumber();
            }

            if($product->ratingCount()){
                $ratingCount = $product->ratingCount();
            }

            if($product->image()){
                $image = $product->image();
            }

            if($product->sellerName()){
                $sellerName = $product->sellerName();
            }

            if($product->sellerLink()){
                $sellerLink = $product->sellerLink();
            }

            if($product->isPrime()){
                $isPrime = $product->isPrime();
            }

            if($product->getOtherSeller()){
                $otherSeller = $product->getOtherSeller();
            }
            // $price = (float)$price / 100;

            // if($domain_column === 'amazon')
            // {
            //     $price = $product->offerPrice();
            // }

            if($product->getPeopleAlsoBought()){
                $alsoBought = $product->getPeopleAlsoBought();
            }

            $analysis_product = AnalysisProduct::where('ean', $ean)->first();

            if(strlen($productNumber) > 0){
                DB::table('amazon_asin_collections')->updateOrInsert(
                    [
                        'ean' => $ean,
                        'domain' => 3
                    ],
                    [
                        'asin' => $productNumber
                    ]
                );
            }
            AnalysisProduct::where('ean', $ean)->update([
                $rating_column => $rating,
                $price_column => $price,
                $rating_count_column => $ratingCount,
                $product_number_column => $productNumber,
                'amazon_seller_name' => $sellerName,
                'amazon_seller_link' => $sellerLink,
                'is_prime' => $isPrime,
                'amazon_other_sellers' => $otherSeller,
                $also_bought_column => $alsoBought,
                $last_sync_column => now(),
                'manual_update' => now(),
            ]);

            if($analysis_product->image == null){
                AnalysisProduct::where('ean', $ean)->update([
                    'image' => json_encode([$image])
                ]);
            }
            if($analysis_product->title == null){
                AnalysisProduct::where('ean', $ean)->update([
                    'title' => $title
                ]);
            }
            $addPrices[] = [
                'ean' => $ean,
                'source' => $domain,
                'title' => $title,
                'price' => $price,
                'rating' => $rating,
                'rating_count' => $ratingCount,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
            ProductPriceApi::insert($addPrices);
        }
        else{
            AnalysisProduct::where('ean', $ean)->update([
                $last_sync_column => now(),
                'manual_update' => now(),
            ]);
        }

        return $alsoBought;
    }
}
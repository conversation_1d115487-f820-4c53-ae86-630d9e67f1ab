<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Http\Controllers\AdminDrmImportsController;
use DB;

class ProcessManualUpdateUrl implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public $timeout = 0;
    // public $delay = 1;
    protected $import_id;
    protected $user_id;

    public function __construct($import_id,$user_id)
    {
        $this->user_id = $user_id;
        $this->import_id = $import_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if(isset(\App\Enums\V2UserAccess::USERS[$this->user_id])) return;
        
        try {
            $url = config('import.base_url').config('import.manual_url_sync');
			$data = [
				'import_id' => $this->import_id,
				'user_id'   => $this->user_id
			];

			$ch = curl_init($url);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_HTTPHEADER, array("accept: application/json","content-type: application/json"));
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
			curl_setopt($ch, CURLOPT_POSTFIELDS,json_encode($data));
			$data = curl_exec($ch);
			$info = curl_getinfo($ch);
			curl_close($ch);
			$result = json_decode($data,true);
			
			return $result['result'];

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        $drm_imports = DB::table('drm_imports')->where('id',$this->import_id)->first();
        $user = DB::table('cms_users')->where('id',$drm_imports->user_id)->first();
        return ['Manual URL Update - '.$user->name.' feed - '.$this->import_id];
    }
}

<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\ChannelProductService;
use App\Models\Marketplace\MpCoreDrmTransferProduct;
use App\Models\ChannelProduct;

class RemoveProductsFromBanededChannels implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $products;
    protected $channels;

    public function __construct($products, $channels)
    {
        $this->products = $products;
        $this->channels = $channels;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        $drm_product_id =MpCoreDrmTransferProduct::where('marketplace_product_id', $this->product_id)->pluck('drm_product_id')->toArray();
        if($drm_product_id){
            $channel_product_ids = ChannelProduct::whereIn('drm_product_id', $drm_product_id)->whereIn('channel', $this->remove_channels)->select('id', 'channel', 'user_id')->get();                                
            // dump($drm_product_id, $channel_product_ids,"check products");
        }

        if($channel_product_ids){
            foreach($channel_product_ids as $channel_product){
                app(ChannelProductService::class)->destroy($channel_product->id, $channel_product->channel, $channel_product->user_id);

            }
        }
    }
}

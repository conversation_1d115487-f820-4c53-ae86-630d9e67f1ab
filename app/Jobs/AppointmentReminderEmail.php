<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use DB;
use Exception;
use Carbon\Carbon;
use Log;
use App\Traits\EmailCampaignTrait;
use App\DropfunnelCustomerTag;

class AppointmentReminderEmail implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, EmailCampaignTrait;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            DB::table('appointments')
            ->where('status', '0')
            ->where('customer_id', '<>', '0')
            ->where('placed_optional_tag', '<>', 1)
            ->whereDate('start_time', '=', now())
            ->orderBy('id', 'desc')
            ->chunk(20, function($chunk){
                $chunk->each(function($item) {
                    $this->sendRemainder($item);
                    $this->addPostAppointmentTag($item);
                    $this->insertMentonTag($item);
                });
            });
        } catch (Exception $e) {
            Log::channel('command')->info("AppointmentReminderEmail");
            Log::channel('command')->info($e);
        }

        try{

            $today = Carbon::today();
            $user_tags = DB::table('dropfunnel_customer_tags')
                        ->join('campaign_tags','dropfunnel_customer_tags.tag_id','campaign_tags.tag_id')
                        ->leftJoin('dropfunnel_email_sent_histories', 'dropfunnel_customer_tags.customer_id','dropfunnel_email_sent_histories.customer_id')
                        ->whereNull('dropfunnel_email_sent_histories.customer_id')
                        ->whereDate('dropfunnel_customer_tags.created_at', $today)
                        ->select('campaign_tags.campaign_id')
                        ->groupBy('campaign_tags.campaign_id')
                        ->get();

            foreach($user_tags as $user){
                app('App\Http\Controllers\NewShopSyncController')->campaignMailJob($user->campaign_id);
            }


        }catch(Exception $e){
            Log::channel('command')->info('email campaign send failed from SendStepMail');
         }


    }


    // Send remainder
    private function sendRemainder($item)
    {
        $currentTime = Carbon::now()->format('Y-m-d H:i');

        $appointmentDateTimes = explode('T', json_decode($item->appointment)['0']->start);
        $appointment_date = $appointmentDateTimes['0'];
        $appointment_time = $appointmentDateTimes['1'];

        $remainder_time = json_decode(DB::table('appointment_slot')->where('user_id', $item->user_id)->value('input_array'));
        if(empty($remainder_time)) return;

        $time = $remainder_time->reminder_time;
        $type = $remainder_time->type;
        if($type == 'minute')
            $type = 'Minuten';
        if($type == 'hour')
            $type = 'Stunden';
        $isNotifyMe = $remainder_time->remainder_to_me;

        $appointmentTime = Carbon::parse($appointment_date . ' ' . $appointment_time);
        if (!empty($time) && $appointmentTime) {
            if ($type == 'hour') {
                $compareTime = $appointmentTime->subHours($time)->format('Y-m-d H:i');
            } else {
                $compareTime = $appointmentTime->subMinutes($time)->format('Y-m-d H:i');
            }
            $to = json_decode($item->appointment)['0']->email;
            $from = DB::table('cms_users')->where('id', $item->user_id)->value('email');

            if ($currentTime == $compareTime && !empty($to) && !empty($from)) {
                $this->AppointmentRemainder($to, $from, $time, $type, $item->user_id, $item->id);
                if(($isNotifyMe == 'on')){
                    $this->AppointmentRemainder($from, $from, $time, $type, $item->user_id, $item->id);
                }
            }
        }
    }

    // Add post appointment tag
    private function addPostAppointmentTag($item)
    {
        if(!empty($item->placed_optional_tag)) return;

        if(!($item->id > \App\Enums\Apps::APPOINTMENT_LAST_ID && !empty($item->customer_id) && empty($item->placed_optional_tag))) return;

        $appointmentDateTimes = explode('T', json_decode($item->appointment)['0']->end);
        $appointment_date = $appointmentDateTimes['0'];
        $appointment_time = $appointmentDateTimes['1'];

        $curr_date = Carbon::now()->format('Y-m-d');
        $curr_time = Carbon::now()->format('H:i');

        $cur_date = strtotime($curr_date);
        $app_date = strtotime($appointment_date);

        $cur_time = strtotime($curr_time);
        $app_time = strtotime($appointment_time);

        if(!($cur_date >= $app_date && $cur_time > $app_time)) return;

        $typeId = @json_decode($item->appointment)['0']->appointment_type;
        if(empty($typeId)) return;

        $tagId = DB::table('appointment_type')->where('id', $typeId)->value('optional_tag');
        if(empty($tagId)) return;

        $tag = DB::table('dropfunnel_tags')->where('id', $tagId)->value('tag');
        if(empty($tag)) return;

        $this->appointmentTagInsert($item, $tag, $item->user_id);
    }

    // Insert mentor tag
    private function insertMentonTag($item)
    {
        if(!empty($item->placed_optional_tag)) return;
        $col = @json_decode($item->appointment, true);
        if(empty($col) || !is_array($col) || empty($col[0])) return;

        $app = $col[0];
        if(empty($app)) return;

        if(!Carbon::parse($app['end'])->lt(now())) return;

        if($this->canSendMentorTag($item, $app))
        {
            $tag = $app['mentor_type'] == 1 ? 'Manager appointment occurred': 'Onboarding occurred';
            $this->appointmentTagInsert($item, $tag);
        } else {
            DB::table('appointments')->where('id', $item->id)->update(['placed_optional_tag' => 1]);
        }
    }


    private function canSendMentorTag($item, $app): bool
    {
        if(!in_array($item->user_id, [3303, 2955])) return false;
        if(empty($item->customer_id)) return false;

        return $app['type'] == 2 && in_array($app['mentor_type'], [1, 2]);
    }

    public function tags()
    {
        return ['Appointment remainder '];
    }


    private function appointmentTagInsert($item, $tag, $user_id = 2455)
    {
        try {
            $customer_id = app('App\Http\Controllers\AdminDrmAllCustomersController')->userBillingToCustomerProfile($item->customer_id, $user_id);
            if($customer_id)
            {
                //insert tag
                DropfunnelCustomerTag::insertTag($tag, $user_id, $customer_id, 17);
                //update optional tag column
                DB::table('appointments')->where('id', $item->id)->update(['placed_optional_tag'=>1]);
            }
        } catch(\Exception $e) {}
    }
}

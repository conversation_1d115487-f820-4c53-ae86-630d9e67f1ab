<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendPaywall<PERSON>mailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $order_id;

    public function __construct($order_id)
    {
        $this->order_id = $order_id;

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        //dd('SendPaywallEmailJob',$this->order_id);
        app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($this->order_id);

    }
}

<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Product\WebshopAnalysisReport;
use App\Models\Product\WebshopAnalysisReportData;
use App\Models\Product\StoreleadDataCollection;
use Exception;
use App\Services\WebshopAnalysisServices\WebshopAnalysisServices;

class ProcessWebshopAnalysisReportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $report_id;
    protected $user_id;

    public function __construct($report_id, $user_id)
    {
        $this->report_id = $report_id;
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{
            $credit_type = \App\Enums\CreditType::WEBSHOP_ANALYSIS;

            $tariffEligibility = checkTariffEligibility($this->user_id);

            WebshopAnalysisReportData::where('report_id', $this->report_id)->chunk(10, function($products) use($credit_type, $tariffEligibility){
                foreach($products as $product){

                    $webshopService = new WebshopAnalysisServices;

                    $search_term = preg_replace('/[^\p{L}\p{N}_]+/u', ' ', $product->query);
    
                    $data = StoreleadDataCollection::where('merchant_name', $product->query)->first();
                    
                    if($data){
                        $tariff_available = app('App\Http\Controllers\tariffController')->creditReduction($this->user_id, 1, 0.01, $credit_type);

                        if($tariff_available == 1 || !$tariffEligibility){
                            $data = json_decode(json_encode($data), true);
                            unset($data['id']);
                            WebshopAnalysisReportData::where('id', $product->id)->update($data);
                            $webshopService->formatData($data, $this->user_id);
                        }
                        
                    }

                    else{

                        $data = StoreleadDataCollection::whereRaw("MATCH(merchant_name) AGAINST(? IN BOOLEAN MODE)", [$search_term])->first();

                        if($data){
                            $tariff_available = app('App\Http\Controllers\tariffController')->creditReduction($this->user_id, 1, 0.01, $credit_type);
    
                            if($tariff_available == 1 || !$tariffEligibility){
                                $data = json_decode(json_encode($data), true);
                                unset($data['id']);
                                WebshopAnalysisReportData::where('id', $product->id)->update($data);
                                $webshopService->formatData($data, $this->user_id);
                            }
                            
                        }

                        else{
                            $host = $this->hostNameFromDomain($product->query);
                            if($host){
                                $domains = $this->hostVarients($host);
    
                                foreach($domains as $domain)
                                {
            
                                    $data = StoreleadDataCollection::where('domain', $domain)->first();
            
                                    if($data){
                                        $tariff_available = app('App\Http\Controllers\tariffController')->creditReduction($this->user_id, 1, 0.01, $credit_type);
    
                                        if($tariff_available == 1 || !$tariffEligibility){
                                            $data = json_decode(json_encode($data), true);
                                            unset($data['id']);
                                            WebshopAnalysisReportData::where('id', $product->id)->update($data);
                                            $webshopService->formatData($data, $this->user_id);
                                            break;
                                        }
                                        
                                    }
                                }
    
                                foreach($domains as $domain)
                                {
    
                                    $data = StoreleadDataCollection::whereRaw("MATCH(domain) AGAINST(? IN BOOLEAN MODE)", [$search_term])->first();
                        
                                    if($data){
                                        $tariff_available = app('App\Http\Controllers\tariffController')->creditReduction($this->user_id, 1, 0.01, $credit_type);
    
                                        if($tariff_available == 1 || !$tariffEligibility){
                                            $data = json_decode(json_encode($data), true);
                                            unset($data['id']);
                                            WebshopAnalysisReportData::where('id', $product->id)->update($data);
                                            $webshopService->formatData($data, $this->user_id);
                                            break;
                                        }
                                        
                                    }
                                }
                            }
                        }
                    }

                }
            });

            WebshopAnalysisReport::where('id', $this->report_id)->update([
                'completed' => 1
            ]);
        } catch(Exception $e){
            WebshopAnalysisReport::where('id', $this->report_id)->update([
                'last_error' => $e->getMessage()
            ]);
        }
    }

    //parse host from domain
	private function hostNameFromDomain($domain)
	{	
		$domain = trim($domain);
		if(!str_starts_with($domain, 'http'))
		{
			$domain = 'http://'.$domain;
		}

		return parse_url($domain, PHP_URL_HOST);
	}

    //Domain varients
	private function hostVarients($host)
	{
		$domains = [$host];
		$domains[] = str_starts_with($host, 'www.') ? str_replace('www.', '', $host) : 'www.'.$host;

		return $domains;
	}
}

<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DRMManualSubscriptionNotifyJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 1140;
    public $tries = 2;
    public $retryAfter = 3;
    public $subscriptionId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($subscriptionId)
    {
        $this->subscriptionId = $subscriptionId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app('App\Http\Controllers\DrmSubscriptionController')->paymentNotify($this->subscriptionId);

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        return ['DRM Manual subscription billing notify.'];
    }
}

<?php

namespace App\Jobs;

use App\Models\AppStore\PurchasedApp;
use App\Notifications\DRMNotification;
use App\Services\AppStoreService;
use App\Services\Keepa\Keepa;
use App\TrendCategories;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use League\Csv\Writer;

class GenerateTrendReport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $drm_app_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($drm_app_id)
    {
        $this->drm_app_id = $drm_app_id;
    }

    /**
     * Generate monthly report csv from json inside category folder
     * @param drm_app_id
     */
    public function handle()
    {
        return true;
        ini_set("memory_limit", -1);
        ini_set('max_execution_time', 3600);
        $reports = []; # report links
        $full_csv = []; # full csv data, max 5000 row
        if(!$this->drm_app_id) return false;

        $json_path = "keepa_products/".date('Y-m')."/$this->drm_app_id/";

        $files = Storage::disk('spaces')->allFiles($json_path, 'public');

        if(!$files) return;

        foreach ($files as $key => $file) {
            if(strpos($file, 'monthly-report')) continue;
            $response = Storage::disk('spaces')->get($file, 'public');

            $keepa = new Keepa(env('KEEPA_API_KEY'));
            $keepa_response = $keepa->setResponse($response);

            $product_asins = $keepa_response->getASINList();

            $csv_header = [
                'Title',
                'Country',
                'Images',
                'ASIN',
                'Brand',
                'Sales Rank (current)',
                'Sales Rank (DROP 30)',
                'Rating',
                'Review Count',
                'Amazon (current)',
                'Amazon (AVG 90)',
                'New (current)',
                'New (AVG 90)',
            ];

            $csv_array = [];
            if(!$product_asins) continue;
            foreach ($product_asins as $key => $asin) {
                $keepa_product = $keepa_response->select($asin);

                // 100 product added
                $csv_array[] = [
                    $keepa_product->title(),
                    $keepa_product->country(),
                    implode(',', $keepa_product->images()),
                    $asin,
                    $keepa_product->brand(),
                    $keepa_product->stats('current', 'sales'),
                    $keepa_product->stats('salesRankDrops30'),
                    $keepa_product->stats('current', 'rating'),
                    $keepa_product->stats('current', 'count_reviews'),
                    $keepa_product->stats('current', 'amazon'),
                    $keepa_product->stats('avg90', 'amazon'),
                    $keepa_product->stats('current', 'new'),
                    $keepa_product->stats('avg90', 'new'),
                ];

            }
            // $csv_array -> 100 product
            $full_csv = array_merge( $full_csv, $csv_array);
        }

        #create csv for each plan [500, 1000, 2000, 5000]
        $plans = config('global.trend_importer_app_plans');
        foreach ($plans as $plan_id => $report_size) {
            $csv_data = \array_slice($full_csv, 0, $report_size);

            #generate csv
            $csv = Writer::createFromString();
            $csv->insertOne($csv_header);
            $csv->insertAll($csv_data);
            $csv_data = makeUtf8($csv->getContent());

            $encrypted_id = Str::random(10);

            $report_file_path = $json_path."monthly-report-$encrypted_id-$report_size.csv";
            Storage::disk('spaces')->put($report_file_path, $csv_data, 'public');

            $file_url = Storage::disk('spaces')->url($report_file_path);
            print("Report generate: \n $file_url \n");

            $reports[$plan_id] = $file_url;
        }

        # list all users purchased this app
        $purchased_list = PurchasedApp::with('trend_categories:keepa_cat_id,drm_app_id,name')
                            ->where('app_id', $this->drm_app_id)
                            ->get();
        foreach ($purchased_list as $key => $purchased) {
            $plan_id = app(AppStoreService::class)->getAppPlan($this->drm_app_id, $purchased->cms_user_id);
            $plan = ($plan_id == 'trial' ) ? key($plans) : $plan_id; #get first plan id if trial

            $message = "Monthly trend report: ".$purchased->trend_categories['name'];
            #notify users their purchased plan trend report
            if( isset($reports[$plan]) ){
                User::find($purchased->cms_user_id)->notify(new DRMNotification( $message, "KEEPA_REPORT_NOTIFICATION", $reports[$plan] ));
            }
        }
    }
}

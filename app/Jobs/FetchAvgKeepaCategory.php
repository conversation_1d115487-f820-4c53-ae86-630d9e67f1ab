<?php

namespace App\Jobs;

use App\KeepaCategory;
use App\Services\Keepa\Keepa;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FetchAvgKeepaCategory implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $category_id;
    protected $range;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($keepa_cat_id, $range=0)
    {
        $this->category_id = $keepa_cat_id;
        $this->range = $range;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        ini_set("memory_limit", -1);
        $keepa = new Keepa(env('KEEPA_API_KEY'));
        echo "Started...";
        $asin_list = $keepa->setCategory($this->category_id)->bestSellers($this->range)->asinList(0, 5000);
        // print_r($asin_list);
        if(!$asin_list) return;
        $keepa_data = [
            'category_id' => $this->category_id,
            'asin_list' => $asin_list,
            'range' => $this->range
        ];
        $keepa_filter = [
            'category_id' => $this->category_id,
            'range' => $this->range
        ];
        KeepaCategory::updateOrCreate($keepa_filter, $keepa_data);

        } catch(\Exception $e) {}
    }
}

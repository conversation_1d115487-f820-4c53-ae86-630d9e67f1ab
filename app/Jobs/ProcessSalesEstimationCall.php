<?php

namespace App\Jobs;

use App\MarketplaceProducts;
use App\Models\Product\AnalysisProduct;
use App\ProductSalesEstimateApi;
use App\Services\ProductApi\SalesEstmnApi;
use App\Services\ProductApi\Services\Countdown;
use App\Services\ProductApi\Services\GoogleShopping;
use App\Services\ProductApi\Services\Rainforest;
use Carbon\Carbon;
use DateTime;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessSalesEstimationCall implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $analysisService;

    public function __construct()
    {
        $this->analysisService = [
            Countdown::class,
            Rainforest::class,
            GoogleShopping::class,
        ];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $products = AnalysisProduct::select('ean', 'amazon_product_number', 'ebay_product_number', 'google_product_number', 'amazon_last_sync', 'ebay_last_sync', 'google_last_sync')->where('amazon_product_number', '!=', null)->distinct()->get();

        foreach($products as $product){
            $analysisService = $this->analysisService;
            $sale = 0;
            foreach($analysisService as $service){
                $analysisApi = new $service;
                $driver = $analysisApi->driver();
                $weekly_sales_column = $driver.'_weekly_sales_estimate';
                $monthly_sales_column = $driver.'_monthly_sales_estimate';
                $last_sync_column = $driver.'_last_sync';
                $product_number_column = $driver.'_product_number';
                $domain = $driver.'.de';
                $product_data = ProductSalesEstimateApi::select('weekly_sales_estimate', 'monthly_sales_estimate', 'created_at')->where('ean', $product->ean)->where('source', $domain)->orderBy('id', 'desc')->first();

                $last_sync_date = new DateTime($product_data->created_at);
                $current_date = Carbon::now();
                $interval = $last_sync_date->diff($current_date)->format('%a');
                if($product_data->created_at == null || ($interval > 7 && ($product_data->weekly_sales_estimate != null || $product_data->monthly_sales_estimate != null))){
                    $salesApi = new SalesEstmnApi($domain);
                    $salesEstmn = $salesApi->search('ean', $product->$product_number_column)->fetch();
                    if(!empty($salesEstmn->salesEstm())){
                        $sales = $salesEstmn->salesEstm();
                        $sale+=$sales['monthly'];
                        AnalysisProduct::where('ean', $product->ean)->update([
                            $weekly_sales_column => $sales['weekly'],
                            $monthly_sales_column => $sales['monthly'],
                            $last_sync_column => now(),
                            'manual_update' => now(),
                        ]);

                        ProductSalesEstimateApi::create([
                            'ean' => $product->ean,
                            'source' => $domain,
                            'weekly_sales_estimate' => $sales['weekly'],
                            'monthly_sales_estimate' => $sales['monthly'],
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);
                    }
                    else{
                        AnalysisProduct::where('ean', $product->ean)->update([
                            $last_sync_column => now(),
                            'manual_update' => now(),
                        ]);

                        ProductSalesEstimateApi::create([
                            'ean' => $product->ean,
                            'source' => $domain,
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);
                    }
                }
            }
            MarketplaceProducts::where('ean', $product->ean)->update([
                'sales_estimate' => $sale,
            ]);
        }

        } catch(\Exception $e) {}
    }
}

<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\DropfunnelCustomerTag;

class CustomerBulkAddTagJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $customer_ids;
    protected $user_id;
    protected $tag;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($customer_ids, $user_id, $tag)
    {
        $this->customer_ids = explode(",", $customer_ids);
        $this->user_id = $user_id;
        $this->tag = $tag;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            foreach($this->customer_ids as $id){
                DropfunnelCustomerTag::insertTag($this->tag, $this->user_id, $id, 4);
            }
        }catch(\Exception $e) {}
    }
}

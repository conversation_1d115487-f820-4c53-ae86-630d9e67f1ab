<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\Traits\ShopOrderSync;
class ShopOrderSyncJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ShopOrderSync;

    public $connectionName = 'ordersync';
    public $timeout = 1140;
    public $tries = 2;
    public $retryAfter = 3;
    public $shop;
    public $channel;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shop,$channel)
    {
        $this->shop=$shop;
        $this->channel = $channel;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        $this->synApiShop($this->channel, $this->shop);
    }

    public function tags()
    {
        return ['Shop Order Sync: '.$this->shop->shop_name ];
    }
}

<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Shop;
use Carbon\Carbon;

class InactiveChannel<PERSON>eleteJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    private int $user_id;

    public function __construct($user_id)
    {
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $today = Carbon::now();
        $users_all_shops = Shop::where('user_id', $this->user_id)->where('status', 0)->whereNotNull('deactivated_at')->select('id', 'deactivated_at')->get();

        if($users_all_shops->isNotEmpty()){
            foreach($users_all_shops as $shop){
                $channel_will_delete_at = Carbon::parse($shop->deactivated_at)->addHours(24);
    
                if($channel_will_delete_at->lessThanOrEqualTo($today)){ // Compare delete time with current time
                    $shop->delete();
                }
            }
        }

        } catch(\Exception $e) {}
    }
}

<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\TransferMarketplaceToDrm;
use App\DrmProduct;

class MpTransferableIdStoreJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    private $user_id;
    private $product_ids = [];

    public function __construct($user_id, $product_ids)
    {
        $this->user_id = $user_id;
        $this->product_ids = $product_ids;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        if(!empty($this->product_ids)){
            $mp_id_exists = DrmProduct::where('user_id', $this->user_id)->whereIn('marketplace_product_id', $this->product_ids)->pluck('marketplace_product_id')->toArray();

            $tranferable_ids = array_diff($this->product_ids, $mp_id_exists);

            foreach($tranferable_ids as $product_id){
                TransferMarketplaceToDrm::firstOrCreate(['user_id' => $this->user_id, 'mp_product_id' => $product_id]);
            }
        }

        } catch(\Exception $e) {}
    }
}

<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use App\Notifications\DRMNotification;
use App\Jobs\ProcessCustomerSync;
use App\Jobs\SplitFile;
use App\KlickTippRequest;
use GuzzleHttp\Client;
use App\DrmCustomer;
use App\User;
use App\KlickTippHistory;

class KlickTippWebhookResponseProcessor implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $user_id;
    public $timestamp;

    public function __construct($user_id, $timestamp)
    {
        $this->user_id = $user_id;
        $this->timestamp = $timestamp;
    }

    public function handle()
    {
        ini_set("memory_limit",-1);
        // Redis::throttle('syncCustomer')->block(0)->allow(1)->every(5)->then(function () {
            try {
                $KlickTippExistingRequest =  KlickTippRequest::where('user_id', $this->user_id)->first();

                $last_sync_date = \Carbon\Carbon::now();

                $client = new Client();

                if ($KlickTippExistingRequest->sync_ready_at == null){

                    $klickTippRequestUpdate = KlickTippRequest::where('user_id', $this->user_id)->update([
                        'sync_ready_at' => $this->timestamp
                    ]);
                }

                $url = "http://*************/customers/".$this->user_id."/subscribers?offset=0&limit=1";

                $response  = $client->get($url);
                $result = json_decode($response->getBody()->getContents(), TRUE);

                $totalSubscribers = $result['result']['total'];
                $limit = 100;
                $slice_remainder = ($result['result']['total'] % $limit);
                if($slice_remainder > 0){
                    $offsetInterator =  $totalSubscribers + $limit;
                }else{
                    $offsetInterator =  $totalSubscribers;
                }

                if($slice_remainder>0.000){
                    $slices = (int)(($result['result']['total'] / $limit )+ 1);
                }else{
                    $slices = (int)($result['result']['total'] / $limit);
                }
                $existingRequestHistory= KlickTippHistory::where('klickTippId', $KlickTippExistingRequest->id)->orderBy('id', 'DESC')->first();
                $total_sync_now = ($totalSubscribers - $existingRequestHistory->$totalSync);
                if($total_sync_now <= 0){
                    User::find($this->user_id)->notify(new DRMNotification('No new KlickTipp Subscribers found to sync!', 'CustomerOnSync'));
                }else{
                    User::find($this->user_id)->notify(new DRMNotification('Your KlickTipp Subscribers is on sync!', 'CustomerOnSync'));

                    $offsets = range(0, $offsetInterator - $limit, $limit);
                    $counter =1;
                    foreach($offsets as $offset){
                        SplitFile::dispatch($this->user_id, $counter, $slices, $last_sync_date, $totalSubscribers, $offset);
                        $counter += 1;
                    }
                }
            } catch (\Exception $e) {
                User::find($this->user_id)->notify(new DRMNotification('Your KlickTipp Subscribers can not be synced. Please Contact Administrator!', 'CustomerOnSync'));
            }
        // }, function () {

        //     return $this->release(5);
        // });
    }

    public function tags()
    {
        return ['Starting KlickTipp Sync for User - '.$this->user_id];
    }

    public function failed(\Exception $exception)
    {
        Log::channel('command')->info("KlickTippWebhookResponseProcessor");
        Log::channel('command')->info($exception);
    }
}

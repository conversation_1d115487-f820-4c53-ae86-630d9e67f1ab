<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\Services\OrderSync\SprinterTiendanimalMediamarkt as SprinterTiendanimalMediamarktCustomerSyncHandler;

class SprinterTiendanimalMediamarktCustomerSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $shopId;
    protected $channelId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shopId, $channelId)
    {
        $this->shopId = $shopId;
        $this->channelId = $channelId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app(SprinterTiendanimalMediamarktCustomerSyncHandler::class)->sync($this->shopId, $this->channelId);

        } catch(\Exception $e) {}
    }
}
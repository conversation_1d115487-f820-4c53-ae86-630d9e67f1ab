<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

use App\Services\DRMProductService;

class DestroyProduct implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $ids;
    public int $timeout = 0;
    public int $tries = 3;
    protected int $user_id;
    protected $country_id;
    protected $lang;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($ids,$user_id,$lang = null,$country_id = null)
    {
        $this->user_id = $user_id;
        $this->ids = $ids;
        $this->country_id = $country_id;
        $this->lang = $lang;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app(DRMProductService::class)->destroy($this->ids,$this->user_id,$this->lang,$this->country_id);

        } catch(\Exception $e) {}
    }

    public function tags(): array
    {
        return ['Delete Products'];
    }
}

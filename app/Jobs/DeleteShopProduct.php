<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\DB;
use App\Services\Modules\Export\Gambio\GambioApiService;

class DeleteShopProduct implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $ids;
    protected $shop_details;
    protected $lang;
    protected $user_id;
    protected $total_products;
    protected $export_process_id;
    public $timeout = 0;
    public $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($ids, $shop_details, $lang, $user_id, $export_process_id = NULL, $total_product = NULL)
    {
        $this->ids = $ids;
        $this->shop_details = $shop_details;
        $this->lang = $lang;
        $this->user_id = $user_id;
        $this->total_products = $total_product;
        $this->export_process_id = $export_process_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        if($this->channel == 1){
            GambioApiService::deleteGambioShopProduct($this->ids, $this->shop_details);
        }

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        $user = DB::table('cms_users')->where('id',$this->shop_details->user_id)->first();

        return ['Delete : '.$this->shop_details->shop_name .' Shop - '.$user->name];
    }
}

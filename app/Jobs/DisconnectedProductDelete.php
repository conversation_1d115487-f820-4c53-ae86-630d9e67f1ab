<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\Services\DRMProductService;

class DisconnectedProductDelete implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $productIds;
    protected $userId;

    public function __construct($productIds, $userId)
    {
        $this->productIds = $productIds;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $drm_product_service = new DRMProductService;
        $drm_product_service->bulkDestroy($this->productIds, $this->userId);

        } catch(\Exception $e) {}

    }

    public function tags()
    {
        return ['Disconnected Products Deleting ...'];
    }
}

<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\Enums\Channel;
use DB;

class DtUserLockUn<PERSON><PERSON><PERSON> implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $user_id; 
    protected $user_account_status;

    public function __construct($user_id, $user_account_status)
    {
        $this->user_id = $user_id;
        $this->user_account_status = $user_account_status;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $user_dt_shop = DB::table('shops')->where([
            'user_id' => $this->user_id,
            'channel' => Channel::DROPTIENDA,
        ])->first();

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->getPrefix($user_dt_shop->url).'api/v1/shop-control',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array(
                'isLocked' => ($this->user_account_status == 1) ? 'true' : 'false'
            ),
            CURLOPT_HTTPHEADER => array(
                'userToken: '.$user_dt_shop->username,
                'userPassToken: '.$user_dt_shop->password
            ),
        ));

        curl_exec($curl);
        curl_close($curl);

        } catch(\Exception $e) {}
    }

    private function getPrefix($url): string
    {
        return trim( $url, '/').'/';
    }
}

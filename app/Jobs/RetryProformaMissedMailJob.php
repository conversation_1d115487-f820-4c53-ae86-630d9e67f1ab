<?php

namespace App\Jobs;

use App\NewOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class RetryProformaMissedMailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        $proferma_orders = DB::connection('mysql::write')
            ->table('new_orders')
            ->whereNull('mail_sent')
            ->where('invoice_number', '!=', -1)
            ->where('test_order', '!=', 1)
            ->where('invoice_number', '>', 0)
            ->where('credit_number', '<', 1)
            ->whereNull('credit_ref')
            ->whereNotNull('proforma_number')
            ->where('status', 'nicht_bezahlt')
            ->select('id')
            ->get();

        if($proferma_orders){

            foreach($proferma_orders as $p){
                    app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($p->id);
                 
            }

        }

       

    }
}

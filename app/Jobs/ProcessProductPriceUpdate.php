<?php

namespace App\Jobs;

use App\Models\Product\AnalysisProduct;
use App\Models\Product\CPAnalysisRequest;
use App\Services\ProductApi\Services\Countdown;
use App\Services\ProductApi\Services\GoogleShopping;
use App\Services\ProductApi\Services\Rainforest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class ProcessProductPriceUpdate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user_id;
    protected $product_ids;
    protected $analysisService;
    protected $payload_id;

    public function __construct($user_id, $product_ids, $payload_id)
    {
        $this->user_id = $user_id;
        $this->product_ids = $product_ids;
        $this->payload_id = $payload_id;
        $this->analysisService = [
            Countdown::class,
            Rainforest::class,
            GoogleShopping::class,
        ];
    }

    public function handle()
    {
        try {
        $analysisService = $this->analysisService;
        $products = [];
        $products = AnalysisProduct::select('ean')->whereIn('id', $this->product_ids)->get();
        DB::table('cp_export_request')->where('id', $this->payload_id)->update([
            'status' => 1,
        ]);
        foreach($analysisService as $service){
            $analysisApi = new $service;
            $collection_id_column = $analysisApi->collectionColumnName();
            $column_prefix = $analysisApi->columnPrefix();
            $collection_interval_data = [
                "schedule_type" => "manual",
            ];

            $collection_body = [
                "name" => "Collection - $this->user_id - " . date('Y-m-d') . 'forced_update',
                "enabled" => True,
                "priority" => "normal",
                "notification_as_csv" => True,
                "notification_as_json" => True,
                "include_html" => 'False'
            ];

            $collectionBody =  array_merge($collection_body, $collection_interval_data);
            $collection_id = $analysisApi->createCollection($collectionBody);
            $cp_request_model = CPAnalysisRequest::create([
                'user_id' => $this->user_id,
                $collection_id_column => $collection_id,
                'default' => 0,
                'forced' => 1,
                'payload_id' => $this->payload_id
            ]);
            if(!empty($products)){
                $product = [];
                foreach($products as $key){
                    $key->item_number = trim($key->ean)."-".$this->user_id;
                    $product[] = ['id' => $key->id, 'user_id' => $this->user_id, 'ean' => $key->ean, 'item_number' => $key->item_number];
                }
                if(!empty($product)){
                    $fIndex = 0;
                    $limit = 1000;
                    if(sizeof($product)>1000){
                        for($i = 0; $i <= (sizeof($product)/1000); $i++){
                            $products = array_slice($product, $fIndex, $limit);
                            $fIndex += 1000;
                            $analysisApi->addProducts($products, $collection_id);
                            unset($products);
                        }
                    }
                    else{
                        $analysisApi->addProducts($product, $collection_id);
                    }
                }
                $response = $analysisApi->startCollection($collection_id);
                if($response['requests_info']['success'] == false){
                    $column_name = $column_prefix."synced";
                    DB::table('cp_export_request')->where('id', $this->payload_id)->update([
                        $column_name => 1,
                    ]);
                }
            }
        }

        } catch(\Exception $e) {}
    }
}

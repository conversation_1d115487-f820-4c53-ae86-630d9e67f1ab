<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FinanceAutoExportFile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $connectionName = 'file-archive';
    public $timeout = 7200;
    public $tries = 2;
    public $retryAfter = 7280;

    public $user_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_id)
    {
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app(\App\Services\Accounting\AutoExport::class)->run($this->user_id);

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        return ["Finance auto export file {$this->user_id}"];
    }
}

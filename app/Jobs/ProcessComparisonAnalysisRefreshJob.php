<?php

namespace App\Jobs;

use App\Models\Product\ComparisonAnalysisProduct;
use Carbon\Carbon;
use DateTime;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use App\MarketplaceProducts;

class ProcessComparisonAnalysisRefreshJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    public function __construct(){}

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        foreach ($this->getComparisonAnalysisProductChunks() as $chunk) {
            $this->updateComparisonAnalysisProducts($chunk);
        }

        } catch(\Exception $e) {}
    }

    protected function getComparisonAnalysisProductChunks()
    {
        $offset = 0;
        $limit = 200;

        do {
            $products = ComparisonAnalysisProduct::whereNull('deleted_at')
                        ->select('ean', 'id')
                        ->offset($offset)
                        ->limit($limit)
                        ->get();

            if ($products->isNotEmpty()) {
                yield $products;
                $offset += $limit;
            } else {
                break; // No more products found, exit the loop
            }
        } while (true);
    }

    protected function updateComparisonAnalysisProducts($products)
    {
        $eans = $products->pluck('ean')->toArray();

        $marketplaceProducts = MarketplaceProducts::whereIn('ean', $eans)
                                ->orderBy('stock', 'asc')
                                ->select('stock', 'ean', 'ek_price')
                                ->get();

        $marketplaceProducts->each(function($item) use ($eans) {
            ComparisonAnalysisProduct::whereIn('ean', $eans)->update([
                'mp_stock' => $item->stock,
                'mp_ek_price' => $item->ek_price,
                'updated_at' => now()
            ]);
        });
    }

    // public function handle()
    // {
    //     ComparisonAnalysisProduct::whereNull('deleted_at')->select('ean', 'id')
    //     ->chunk(200, function($chunk) {
    //         $eans = $chunk->pluck('ean')->toArray();
    //         MarketplaceProducts::whereIn('ean', $eans)
    //         ->orderBy('stock', 'asc')
    //         ->select('stock', 'ean', 'ek_price')
    //         ->get()
    //         ->each(function($item) {
    //             ComparisonAnalysisProduct::where('ean', $item->ean)->update([
    //                 'mp_stock' => $item->stock,
    //                 'mp_ek_price' => $item->ek_price,
    //                 'updated_at' => now()
    //             ]);
    //         });
    //     });
    // }
}

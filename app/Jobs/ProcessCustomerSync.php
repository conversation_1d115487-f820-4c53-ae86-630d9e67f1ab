<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use App\Notifications\DRMNotification;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use GuzzleHttp\Client;

use Illuminate\Bus\Queueable;
use Carbon\Carbon;
use CRUDBooster;
use App\KlickTippRequest;
use App\KlickTippHistory;
use App\NewCustomer;
use App\DropfunnelCustomerTag;
use App\CustomerTag;
use App\User;

class ProcessCustomerSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $file;
    public $user_id;
    public $slices;
    public $counter;
    public $timeout = 0;
    public $tries = 3;
    public $last_sync_date;
    public $totalSubscribers;

    public function __construct($file, $user_id, $slices, $counter, $last_sync_date, $totalSubscribers)
    {
        $this->file = $file;
        $this->user_id = $user_id;
        $this->slices = $slices;
        $this->counter = $counter;
        $this->last_sync_date = $last_sync_date;
        $this->totalSubscribers = $totalSubscribers;
    }

    public function handle()
    {
        ini_set("memory_limit",-1);
        // Redis::throttle('syncCustomer')->block(0)->allow(1)->every(5)->then(function () {
            try {
                $cleanded_data = $this->file['result']['subscribers'];

                foreach($cleanded_data as $row){

                    if(isset($row['email'])){

                        (isset($row['fieldFirstName']['value'])) ?: $full_name = $row['fieldFirstName']['value'];
                        (isset($row['fieldFirstName']['value'])) ?: $full_name .= ' ' . $row['fieldLastName']['value'];

                        $dataToUpdate = [
                            'default_language'=> 'DE',
                            'currency'=> 'EUR',
                            'insert_type'=> 1
                        ];

                        if ($full_name) $dataToUpdate['full_name'] = $full_name;

                        if ($row['fieldCompanyName']['value']) $dataToUpdate['company_name'] = $row['fieldCompanyName']['value'];

                        if ($row['fieldMobilePhone']['value']) $dataToUpdate['phone'] = $row['fieldMobilePhone']['value'];

                        if ($row['fieldWebsite']['value']) $dataToUpdate['website'] = $row['fieldWebsite']['value'];

                        if ($row['fieldStreet1']['value']) $dataToUpdate['address'] = $row['fieldStreet1']['value'];

                        if ($row['fieldCity']['value']) $dataToUpdate['city'] = $row['fieldCity']['value'];

                        if ($row['fieldState']['value']) $dataToUpdate['state'] = $row['fieldState']['value'];

                        if ($row['fieldZip']['value']) $dataToUpdate['zip_code'] = $row['fieldZip']['value'];

                        if ($row['fieldCountry']['value']) $dataToUpdate['country'] = $row['fieldCountry']['value'];


                        if(isset($row['email']) && !empty($row['email'])){
                            $dataToUpdate = array_filter($dataToUpdate);
                            $customer = NewCustomer::updateOrCreate([
                                'email'=> $row['email'],
                                'user_id'=> $this->user_id,
                                ],
                                $dataToUpdate
                            );

                            //insert tag
                            try{
                                $tag_arr = isset($row['manual_tags']) ? $row['manual_tags'] : [];
                                if(!empty($tag_arr) && $customer->id){
                                   $labels = array_column($tag_arr, 'label');
                                   if(!empty($labels)){
                                        $label_arr = array_filter(array_map('trim', $labels));
                                        if(!empty($label_arr)){
                                            foreach ($label_arr as $tag) {
                                                DropfunnelCustomerTag::insertTag($tag, $this->user_id, $customer->id, 2);
                                            }
                                        }

                                   }
                                }
                            }catch(\Exception $ev){}
                            //Insert tag end
                        }

                    }
                }
                if(($this->slices - 1) == $this->counter){
                    $existingRequest= KlickTippRequest::where('user_id', $this->user_id)->first();
                    $klickTippRequest = KlickTippRequest::where('user_id', $this->user_id)->update([
                        'syncCount'=> $existingRequest->syncCount + 1,
                        'last_sync_status'=> 1,
                        'last_sync_date'=> $this->last_sync_date
                    ]);
                    $existingRequestHistory= KlickTippHistory::where('klickTippId', $existingRequest->id)->orderBy('totalSync', 'DESC')->first();
                    $total_sync_now = ($this->totalSubscribers - $existingRequestHistory->totalSync);
                    KlickTippHistory::insert([
                        'klickTippId'=> $existingRequest->id,
                        'totalSync'=> $total_sync_now,
                        'sync_status'=> 1,
                        'created_at'=> $this->last_sync_date
                    ]);
                    if($total_sync_now>0){
                        User::find($this->user_id)->notify(new DRMNotification(''.$total_sync_now. ' KlickTipp Subscribers has been synced!', 'CustomerOnSync'));
                    }else{
                        User::find($this->user_id)->notify(new DRMNotification('No new KlickTipp subscribers found!', 'CustomerOnSync'));
                    }
                }
            } catch (\Exception $e) {
                User::find($this->user_id)->notify(new DRMNotification('Your KlickTipp Subscribers can not be synced. Please Contact Administrator!', 'CustomerOnSync'));
            }

        // }, function () {

        //     return $this->release(5);
        // });
    }

    public function tags()
    {
        return ['Syncing KlickTipp Subscribers for user - '.$this->user_id];
    }

    public function failed(\Exception $exception)
    {
        Log::channel('command')->info('ProcessCustomerSync');
        Log::channel('command')->info($exception);
    }
}

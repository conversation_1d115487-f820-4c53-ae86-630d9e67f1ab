<?php

namespace App\Jobs;

use App\Http\Controllers\EtsyController;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class EtsyProductDeleteJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public $timeout = 300;
    public $tries = 3;

    protected $productIds;
    protected $userId;

    /**
     * Create a new job instance.
     *
     * @param $productIds
     * @param $userId
     */
    public function __construct($productIds, $userId)
    {
        $this->productIds = $productIds;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app(EtsyController::class)->deleteListing($this->productIds, $this->userId);

        } catch(\Exception $e) {}
    }
}

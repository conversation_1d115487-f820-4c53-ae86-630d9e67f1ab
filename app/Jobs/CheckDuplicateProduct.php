<?php

namespace App\Jobs;

use App\Helper\GambioApi;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use DB;
use App\Http\Controllers\AdminCategoriesController;
use App\Services\Modules\Export\Gambio\GambioApiService;

class CheckDuplicateProduct implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $shop_details;
    public $timeout = 0;
    public $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shop_details)
    {
        $this->shop_details = $shop_details;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        if(isLocal() || \in_array($this->shop_details->user_id,[62])){
            GambioApiService::findDuplicateProducts($this->shop_details);
        }
        else {
            GambioApi::findDuplicateProducts($this->shop_details);
        }

        } catch(\Exception $e) {}
        

        /* Gambio Category */
        // $Categoriescontroller = new AdminCategoriesController();
        // $Categoriescontroller->findDuplicateGambioCategory($this->shop_details);
    }


    public function tags()
    {
        $user = DB::table('cms_users')->where('id', $this->shop_details->user_id)->first();

        return ['Duplicate Product and Category Check : ' . $this->shop_details->shop_name . ' Shop - ' . $user->name];
    }
}

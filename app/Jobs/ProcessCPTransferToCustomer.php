<?php

namespace App\Jobs;

use App\AnalysisCategory;
use App\Models\Product\AnalysisProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class ProcessCPTransferToCustomer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $user_id;
    protected $product_ids;

    public function __construct($user_id, $product_ids)
    {
        $this->user_id = $user_id;
        $this->product_ids = $product_ids;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        foreach($this->product_ids as $id){
            $product = AnalysisProduct::where('id', $id)->first();
            $exists = AnalysisProduct::where('ean', $product->ean)->where('user_id', $this->user_id)->exists();
            if(!$exists){
                $duplicate = $product->replicate();
                $duplicate->user_id = $this->user_id;
                $duplicate->save();
            }
        }

        } catch(\Exception $e) {}
    }
}

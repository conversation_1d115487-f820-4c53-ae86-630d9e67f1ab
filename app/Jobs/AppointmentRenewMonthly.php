<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use DB;
use Exception;
use Carbon\Carbon;
use Log;

class AppointmentRenewMonthly implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{
            DB::table('takeappointment')->where(function ($a) {
                $a->whereNull('last_renewal_date')->orWhereDate('last_renewal_date', '<', Carbon::now());
                 })
                ->select('id', 'renewal_qty', 'manager_renewal_qty', 'payment_date_remaining', 'manager_date')
                ->orderBy('id')
                ->get()
                ->each(function ($item) {
                    // start add history
                    $remaining_date = DB::table('takeappointment')->where('id', $item->id)->first();

                        $this->decreaseAppointmentDates($remaining_date->user_id);
                        $this->decreaseAppointmentDates($remaining_date->user_id, 1);

                        if ($remaining_date->renewal_qty > 0) {
                            $this->increaseAppointmentDates($remaining_date->user_id, $remaining_date->renewal_qty);    //add mentor dates
                        }

                        if ($remaining_date->manager_renewal_qty> 0) {
                            $this->increaseAppointmentDates($remaining_date->user_id, $remaining_date->manager_renewal_qty, 1);    //add Manager dates
                        }

                        // if ($remaining_date->free_date > 0) {
                        //     $this->increaseAppointmentDates($remaining_date->user_id, 0, 2);    //reset Free dates
                        // }

                });
        }catch(Exception $e){
            Log::channel('command')->info("AppointmentRenewMonthly");
            Log::channel('command')->info($e);
        }

    }

 protected function decreaseAppointmentDates($user_id, $mentor_type = null){
        $last_month_mentor_date = DB::table('appointment_histories')
        ->where('user_id', $user_id)
        ->where('mentor_type', $mentor_type)
        ->where('type', 4)
        ->whereBetween('created_at',
                    [Carbon::now()->subMonth()->startOfMonth(), Carbon::now()->subMonth()->endOfMonth()]
                )
        ->orderby('id', 'desc')
        ->first();

        //temp..
        if($last_month_mentor_date->current == $last_month_mentor_date->previous){
            $mentor_renewal_dates = $last_month_mentor_date->current;
        }else{
           $mentor_renewal_dates = $last_month_mentor_date->current - $last_month_mentor_date->previous;
        }

        $last_month_mentor_used = DB::table('appointment_histories')
        ->where('user_id', $user_id)
        ->where('mentor_type', $mentor_type)
        ->whereBetween('created_at',
                    [Carbon::now()->subMonth()->startOfMonth(), Carbon::now()->subMonth()->endOfMonth()]
        );

        $message = ' ';
        if($mentor_type == 1){
            $last_month_mentor_used->where('message', 'like', '%User takes manager%');
            $message = 'Manager';
        }else{
            $last_month_mentor_used->where('message', 'like', '%User takes mentoring%');
            $message = 'Mentoring';
        }
        $used_dates = $last_month_mentor_used->count();


        // if($used_dates > 0){
        //     $last_month_canceled = DB::table('appointment_histories')
        //     ->where('user_id', $user_id)
        //     ->where('mentor_type', $mentor_type)
        //     ->whereBetween('created_at',
        //                 [Carbon::now()->subMonth()->startOfMonth(), Carbon::now()->subMonth()->endOfMonth()]
        //     )
        //     ->where('message', 'like', '%Appointment canceled%')
        //     ->count();
        //     $used_dates = $used_dates - $last_month_canceled;

        // }


        $expire_date = $mentor_renewal_dates - $used_dates;

        if(($expire_date) > 0){
            //expire unused dates b
            $appointment_history = DB::table('appointment_histories')
            ->where('user_id', $user_id)
            ->where('mentor_type', $mentor_type)
            ->orderby('id', 'desc')
            ->first();

        $previous = ($appointment_history->current) ? $appointment_history->current : 0;
        $final_dates = $previous - $expire_date;
        $final_dates = ($final_dates > 0) ? $final_dates : 0;

        DB::table('appointment_histories')->insert([
            'user_id' => $user_id,
            'previous' => $previous,
            'current' => $final_dates,
            'mentor_type' => $mentor_type,
            'type' => 6,  // 6 for expired remaining dates
            'message' => 'Renewal dates ' . $expire_date . ' expired on ' . Carbon::today()->toDateString(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        // end add history
        $update_column = 'payment_date_remaining';
        if ($mentor_type == 1) {
            $update_column = 'manager_date';
        } elseif ($mentor_type == 2) {
            $update_column = 'free_date';
        }

        DB::table('takeappointment')->where('user_id', $user_id)->update([$update_column => $final_dates, 'last_renewal_date' => Carbon::now()]);
        }
    }

    protected function increaseAppointmentDates($user_id, $renew_qty, $mentor_type = null)
    {
        $appointment_history = DB::table('appointment_histories')
            ->where('user_id', $user_id)
            ->where('mentor_type', $mentor_type)
            ->orderby('id', 'desc')
            ->first();

        $previous = ($appointment_history->current) ? $appointment_history->current : 0;

        DB::table('appointment_histories')->insert([
            'user_id' => $user_id,
            'previous' => $previous,
            'current' => $previous + $renew_qty,
            'mentor_type' => $mentor_type,
            'type' => 4,
            'message' => 'Renewal dates ' . $renew_qty . ' added on ' . Carbon::today()->toDateString(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        // end add history
        $update_column = 'payment_date_remaining';
        if ($mentor_type == 1) {
            $update_column = 'manager_date';
        } elseif ($mentor_type == 2) {
            $update_column = 'free_date';
        }
        DB::table('takeappointment')->where('user_id', $user_id)->update([$update_column => $previous + $renew_qty, 'last_renewal_date' => Carbon::now()]);
    }



    public function tags()
    {
        return ['Appoinment renewal monthly'];
    }
}

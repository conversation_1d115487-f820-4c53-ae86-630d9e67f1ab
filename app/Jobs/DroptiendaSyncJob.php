<?php

namespace App\Jobs;

use App\Models\DroptiendaSyncHistory;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DroptiendaSyncJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $connectionName = 'droptienda';
    public $timeout = 1140;
    public $tries = 2;
    public $retryAfter = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $syncHistories = DroptiendaSyncHistory::whereNull('synced_at')
            ->where('tries', '<', 3)
            ->limit(env('SYNC_ITEM_LIMIT', 50))
            ->get();

        foreach ($syncHistories as $syncHistory) {
            switch ($syncHistory->sync_type) {
                case \App\Enums\DroptiendaSyncType::CATEGORY:
                    app(\App\Services\Category\CategoryService::class)->syncCategoryToDroptienda($syncHistory);
                    break;

                case \App\Enums\DroptiendaSyncType::PRODUCT:
                    app(\App\Services\Product\ProductService::class)->syncProductToDroptienda($syncHistory);
                    break;
            }
        }

        } catch(\Exception $e) {}
    }
}

<?php

namespace App\Jobs;

use App\DrmProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use League\Csv\CannotInsertRecord;
use League\Csv\Writer;

class GenerateCsv implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $drm_import_id;
    protected string  $csv_file_path;
    protected int $user_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($drm_import_id, $csv_file_path,$user_id)
    {
        $this->drm_import_id = $drm_import_id;
        $this->csv_file_path = $csv_file_path;
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws CannotInsertRecord
     */
    public function handle()
    {
        try {
        $headers = ['item_number','ean','description','image','ek_price','stock','category','item_weight','item_size','item_color','note','production_year','brand','materials','tags','status','gender','delivery_days','uvp','title','short_description','shipping_cost'];
        $products = DrmProduct::where([
            'drm_import_id' => $this->drm_import_id
        ])->with('drm_categories.drm_category')->get();
        $lang = app('App\Services\UserService')->getLang($this->user_id);

        $csv = Writer::createFromString();
        $csv->insertOne($headers);

        foreach ($products as $product) {
            $insertData = array();
            foreach ($headers as $header){
                if(in_array($header,['title','description'])){
                    $insertData[$header] = $product->$header[$lang];
                }
                elseif($header == 'image'){
                    $insertData[$header] = implode(",", $product->$header);
                }
                elseif ($header == 'category'){
                    $insertData[$header] = implode(';',$product->drm_categories->pluck('drm_category.category_name_'.$lang)->toArray());
                }
                else{
                    $insertData[$header] = $product->$header;
                }
            }
            $csv->insertOne($insertData);
        }

        $csv->getContent();
        Storage::disk('spaces')->put($this->csv_file_path, $csv, 'public');

        } catch(\Exception $e) {}
    }
}

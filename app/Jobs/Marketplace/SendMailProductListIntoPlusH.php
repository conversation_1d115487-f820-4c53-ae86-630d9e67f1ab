<?php

namespace App\Jobs\Marketplace;

use App\Mail\DRMSEndMail;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class SendMailProductListIntoPlusH implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public $cloudFile_url;

    public function __construct($cloudFile_url)
    {
        $this->cloudFile_url = $cloudFile_url;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{
            $isLocal = isLocal();
            $slug = "plusH_delivery_note_list";
            $tags = [
                'delivery_file' => $this->cloudFile_url,
            ];
            $mail_data = DRMParseMailTemplate($tags, $slug);
            $to =  !$isLocal ? '<EMAIL>' : '<EMAIL>';
            $cc =  !$isLocal ? '<EMAIL>' : '<EMAIL>	';
            app('drm.mailer')->getMailer()->to($to)->cc($cc)->send(new DRMSEndMail($mail_data)); //Send
        }catch(\Exception $e){
            Log::info('!ops mail send error'.$e->getMessage());
        }
    }
}

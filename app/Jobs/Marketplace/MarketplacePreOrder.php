<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class MarketplacePreOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $pre_orders = DB::connection('marketplace')->table('pre_orders')->where('product_availability',0)->where('payment_status',1)->get();

        foreach($pre_orders as $order){

            $products = DB::connection('marketplace')->table('marketplace_products')->where('id',$order->product_id)->where('stock','>=',$order->quantity)->first();
            if(!empty($products)){
                DB::table('new_orders')->where('id',$order->order_id)->update([
                    'invoice_number' => 1
                ]);

                $profoma = DB::table('new_orders')->where('id',$order->order_id)->first();

                if($profoma->invoice_number > 0){

                    app(\App\Http\Controllers\AdminDrmAllOrdersController::class)->preOrderSupplier($profoma->id);

                }

                DB::connection('marketplace')->table('pre_orders')->where('id',$order->id)->update([
                    "product_availability"=>1
                ]);

            }

        }

        return true;
    }
}

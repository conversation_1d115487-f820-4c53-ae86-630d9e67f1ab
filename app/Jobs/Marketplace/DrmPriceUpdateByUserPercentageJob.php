<?php

namespace App\Jobs\Marketplace;

use App\DrmProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;
use App\Services\DRMProductService;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Services\Marketplace\InternelSyncService;

class DrmPriceUpdateByUserPercentageJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    private $user_id;
    private $price_markup;
    private $product_ids;
    
    public function __construct(int $user_id, $price_markup, $product_ids = [])
    {
        $this->user_id = $user_id;
        $this->price_markup = $price_markup;
        $this->product_ids = $product_ids;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $percentage = $this->price_markup;
        DrmProduct::select('id','user_id','marketplace_product_id','ek_price','mp_category_offer','uvp')
                ->with('marketplace_products:id,vk_price,uvp')
                ->where('user_id', $this->user_id)
                ->whereIn('id', $this->product_ids)
                ->chunk(100, function ($drm_products) use ($percentage) {
                    foreach ($drm_products as $drm_product) {
                        $data = [];

                        if(!empty($drm_product->marketplace_products)){
                            $new_price = $drm_product->marketplace_products->vk_price + ($drm_product->marketplace_products->vk_price * $percentage / 100);
                            $discount = $drm_product->mp_category_offer ?? 0.0;
                            $new_price = $new_price - ($new_price * $discount / 100);
                            
                            if(round($drm_product->ek_price,2) != round($new_price,2)){
                                $data['ek_price'] = round($new_price,2);
                            }

                            $new_uvp = $drm_product->marketplace_products->uvp + ($drm_product->marketplace_products->uvp * $percentage / 100);
                            if(round($drm_product->uvp,2) != round($new_uvp,2)){
                                $data['uvp'] = round($new_uvp,2);
                            }

                            $data['mp_price_markup'] = $percentage;
                            app(DRMProductService::class)->update($drm_product->id, $data);
                            info("DrmProduct id: ".$drm_product->id." updated by user id: ".$drm_product->user_id. " with new price: ".$new_price);
                            info("DrmProduct id: ".$drm_product->id." updated by user id: ".$drm_product->user_id. " with new UVP: ".$new_uvp);
                        }
                    }
                });
    }
}

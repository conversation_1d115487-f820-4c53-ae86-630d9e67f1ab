<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Enums\Apps;
use App\Jobs\ChannelManager\AutoTransfer;
use App\User;
use App\Country;
use Carbon\Carbon;
use App\DrmProduct;
use App\NewCustomer;
use App\BillingDetail;
use App\DeliveryCompany;
use App\Mail\DRMSEndMail;
use App\Models\DrmCategory;
use Illuminate\Support\Str;
use App\Traits\ProjectShare;
use Illuminate\Http\Request;
// use App\Enums\CollectionStatus;
use App\Enums\VisibilityStatus;
use App\Enums\Marketplace\Status;
use App\Models\Marketplace\Banner;
use Illuminate\Support\Facades\DB;
//  use App\Services\Marketplace\InternelSyncService;
use App\Models\Marketplace\Product;
use App\Http\Controllers\Controller;
// use App\Services\Otto\Api\Products;
use App\Models\Marketplace\Category;
use Illuminate\Support\Facades\Mail;
use App\Models\Marketplace\UserAccess;
// use App\Models\Marketplace\Collection;
use Illuminate\Support\Facades\Session;
use App\Enums\Marketplace\FakeSuppliers;
use App\Enums\Marketplace\ProductStatus;
// use App\Services\Marketplace\OrderService;
use Illuminate\Support\Facades\Validator;
use App\Services\Marketplace\ProductService;
use App\Services\ProductApi\TransferProduct;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Models\Marketplace\MpCoreDrmTransferProduct;
use App\Models\Marketplace\MarketplaceProductAnalysis;
use App\Models\Marketplace\MarketplaceProductExportFeed;
use App\Services\Marketplace\MarketplaceAnalysisService;
use App\Models\Marketplace\AutoTransferSubscription;
use App\DropmatixProductBrand;
use App\NewOrder;
use Exception;
use PDF;
use Illuminate\Support\Facades\Cache;
use App\Jobs\Marketplace\MarketplaceProductTransferBasedOnDrmEan;


class MarketplaceProductsDirectTransferToDrmUser implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $category_ids;
    protected $product_ids;
    protected $user_id;

    public function __construct($product_ids = [], $user_id = null, $category_ids = [])
    {
        $this->category_ids = $category_ids;
        $this->product_ids = $product_ids;
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // //DB::beginTransaction();
        try {

            $user_id = $this->user_id;
            $categories = array_unique($this->category_ids) ?? [];
            $tranferable_ids = $this->product_ids ?? [];

            $importProduct = app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->getImportPlan($user_id);
            $transferLimit = $importProduct ? $importProduct['product_amount'] : 0;
            $transferPlan = $importProduct['plan'] ? $importProduct['plan'] : null;
            $transferPlanLimit = $importProduct['limit'] ? $importProduct['limit'] : null;

            $i = 0;
            $trial_checked = 0;
            $mpCoreData = [];
            $autoTranferIds = [];
            $drm_product_categories = [];
            if((($transferPlan && $transferPlan == 'Trial') && ($transferPlanLimit && $transferPlanLimit == 'Unlimited')) || ($transferPlanLimit && $transferPlanLimit == 'Unlimited')){
                // $user_access_status = \App\Models\Marketplace\UserAccess::where('user_id', $user_id)->first()->accessable_categories ?? [];
                // $product_cats = array_diff($categories, $user_access_status);
                // $newArray = [];
                // // foreach($product_cats as $c){
                // //     $newArray[] =  array("accessable_categories"=>$c,"set_by_admin"=>0);
                // // }
                // $allowedCategories = $categories;
                // if(!empty($allowedCategories)){
                //     $all_access_category = UserAccess::where('user_id', $user_id)
                //                                 ->select('check_accessable_categories')
                //                                 ->get()->toArray();
                //     if(!empty($all_access_category)){
                //         $single_access_category = array_column($all_access_category[0]['check_accessable_categories'], 'accessable_categories');

                //         $single_access_category_user = array_column($all_access_category[0]['check_accessable_categories'], 'set_by_admin');

                //         foreach($allowedCategories as $category){
                //             if(!in_array($category,$single_access_category)){
                //                 $newArray[] = array("accessable_categories"=>$category,"set_by_admin"=>0);
                //             }else{
                //                 $index = array_keys($single_access_category, $category);
                //                 $val = $single_access_category_user[$index[0]];
                //                 $newArray[] =  array("accessable_categories"=>$category,"set_by_admin"=>$val);
                //             }
                //         }
                //         // dd($single_access_category);
                //         foreach($single_access_category as $key => $old_access){
                //             if(!in_array($old_access,$allowedCategories)){
                //                 $val = $single_access_category_user[$key];
                //                 if($val == 0){
                //                 $newArray[] = array("accessable_categories"=>$old_access,"set_by_admin"=>$val);
                //                 }
                //             }
                //         }
                //     }else{
                //         foreach($allowedCategories as $category){
                //             $newArray[] =  array("accessable_categories"=>$category,"set_by_admin"=>0);
                //         }
                //     }
                // }

                // //dd("checkif",$product_cats,$user_access_status);
                // if(!empty($product_cats)){
                //     $new_merge = array_unique(array_merge($user_access_status , $product_cats));
                // }else{
                //     $new_merge = $user_access_status;
                // }

                // if(!empty($newArray)){
                //     $attributes =  array_merge([
                //         'user_id'           => $user_id,
                //         'status'            => 1,
                //     ],['accessable_categories' => $new_merge], ['check_accessable_categories' => $newArray]);

                //     $newRow = UserAccess::updateOrCreate(['user_id'=>$user_id], $attributes);
                // }
                foreach($tranferable_ids as $productId){
                    $product = Product::with('additionalInfo','productBrand')->where('id', $productId)->first();
                    $ean_exist = DrmProduct::where('ean', $product->ean)->where('user_id', $user_id)->first();
                    if(!$ean_exist){
                        // Additional columns check
                        $manufacturer = null;
                        $manufacturer_link = null;
                        $manufacturer_id = null;
                        $custom_tariff_number = null;
                        $shipping_company_id = null;
                        $region = null;
                        $country_of_origin = null;
                        $min_stock = null;
                        $min_order = null;
                        $gross_weight = null;
                        $net_weight = null;
                        $product_length = null;
                        $product_width = null;
                        $product_height = null;
                        $volume = null;
                        $packaging_length = null;
                        $packaging_width = null;
                        $packaging_height = null;
                        $item_unit = null;
                        $packing_unit = null;
                        $volume_gross = null;

                        if( $product->additionalInfo ){
                            $manufacturer = $product->additionalInfo->manufacturer;
                            $manufacturer_link = $product->additionalInfo->manufacturer_link;
                            $manufacturer_id = $product->additionalInfo->manufacturer_id;
                            $custom_tariff_number = $product->additionalInfo->custom_tariff_number;
                            $shipping_company_id = $product->additionalInfo->shipping_company_id;
                            $region = $product->additionalInfo->region;
                            $country_of_origin = $product->additionalInfo->country_of_origin;
                            $min_stock = $product->additionalInfo->min_stock;
                            $min_order = $product->additionalInfo->min_order;
                            $gross_weight = $product->additionalInfo->gross_weight;
                            $net_weight = $product->additionalInfo->net_weight;
                            $product_length = $product->additionalInfo->product_length;
                            $product_width = $product->additionalInfo->product_width;
                            $product_height = $product->additionalInfo->product_height;
                            $volume = $product->additionalInfo->volume;
                            $packaging_length = $product->additionalInfo->packaging_length;
                            $packaging_width = $product->additionalInfo->packaging_width;
                            $packaging_height = $product->additionalInfo->packaging_height;
                            $item_unit = $product->additionalInfo->item_unit;
                            $packing_unit = $product->additionalInfo->packing_unit;
                            $volume_gross = $product->additionalInfo->volume_gross;
                        }

                        $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
                        $lang = app('App\Services\UserService')->getProductLanguage($country_id);

                        $discount = 0.0;
                        if ( $product->category_id ) {
                            $category = Category::where('id',$product->category_id)->first();
                            $drmCategory = DrmCategory::where('category_name_'.$lang, $category->name)->where('user_id',$user_id)->first();
                            if( date($category->start_date) <= date("Y-m-d") && date($category->end_date) >= date("Y-m-d") ){
                                $discount = $category->is_offer_active ? $category->discount_percentage : 0.0;
                            }
                            if ( !$drmCategory ) {
                                $drmCategory = DrmCategory::create([
                                    'category_name_'.$lang => $category->name,
                                    'user_id' => $user_id,
                                    'country_id' => $country_id,
                                ]);
                            }
                        }

                        $deliveryCompanyRow = [
                            'user_id'       => \App\Enums\Apps::DROPMATIX_ID,
                            'name'          => 'Dropmatix Systema SL',
                            'contact_name'  => 'Dropmatix Systema SL',
                            'zip'           => '07200',
                            'state'         => 'FELANITX',
                            'country_id'    => 8,
                            'email'         => '<EMAIL>',
                            'address'       => 'C/ HORTS, 33',
                            'note'          => 'Marketplace Supplier',
                            'is_marketplace_supplier' => 1,
                        ];

                        $deliveryCompany = DeliveryCompany::where('user_id', $user_id)
                            ->where('name', $deliveryCompanyRow['name'])
                            ->where('email', '<EMAIL>')
                            ->first();

                        if ( !$deliveryCompany ) {
                            $deliveryCompanyRow['user_id'] = $user_id;
                            $deliveryCompany = DeliveryCompany::create($deliveryCompanyRow);
                        }

                        $stock = ($product->shipping_method == 1) ? $product->stock : $product->internel_stock;

                        if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::FULFILLment) {
                            $shippingCost = 5.20;
                        } else if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::DROPSHIPPING) {
                            $shippingCost = $product->shipping_cost;
                        }

                        if ( $product->brand ) {
                            $mpBrandName = (is_numeric($product->brand) ? $product->productBrand->brand_name : $product->brand) ?? '';
                            $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper( $mpBrandName) . '%')->where('user_id',$user_id)->first();
                            if ( $drmBrand ) {
                                $drmBrand = $drmBrand;
                            } else {
                                if(!empty($mpBrandName)){
                                    $drmBrand = DropmatixProductBrand::create([
                                        'brand_name' =>  $mpBrandName,
                                        'user_id' => $user_id,
                                        'brand_logo'=>($product->productBrand->brand_logo ?? [])
                                    ]);
                                }
                            }
                        }

                        if( $product->offer_start_date <= now() && $product->offer_end_date >= now() && $product->is_offer_active == 1){
                            $discount += $product->discount_percentage ? ($product->discount_percentage - $product->discount_percentage/4) : 0.0;
                        }

                        $drmProductInfo             = [
                            'user_id'               => $user_id,
                            'country_id'            => $country_id,
                            'language_id'           => null,
                            'name'                  => $product->brand .' '. $product->name,
                            'item_number'           => $product->item_number,
                            'ean'                   => $product->ean,
                            'additional_eans'       => json_encode($product->additional_eans),
                            'image'                 => $product->image,
                            'ek_price'              => userWiseVkPriceCalculate($product->vk_price ?? 0.0, $user_id, false, $discount, $product->api_id ?? null),
                            'vk_price'              => 0.00,
                            'vat'                   => $product->vat ?? null,
                            'tax_type'              => $product->tax_type ?? 1,
                            'stock'                 => (empty($stock)) ? 0 : $stock,
                            'category'              => $drmCategory->id ?? null,
                            'ean_field'             => 1,
                            'item_weight'           => $product->item_weight ?? null,
                            'item_size'             => $product->item_size ?? null,
                            'item_color'            => $product->item_color ?? null,
                            'note'                  => $product->note ?? null,
                            'production_year'       => $product->production_year ?? null,
                            'brand'                 => $drmBrand ? $drmBrand->id : null,
                            'materials'             => $product->materials ?? null,
                            'tags'                  => $product->tags ?? null,
                            'update_enabled'        => $product->update_enabled ?? null,
                            'status'                => $product->status ?? null,
                            'gender'                => $product->gender ?? null,
                            'uvp'                   => $product->uvp ?? 0.00,
                            'title'                 => [
                                                        $lang => $product->name ?? null,
                                                    ],
                            'update_status'         => makeUpdateStatusJson(),

                            // 'short_description' => json_encode($product->description),
                                'description' => [
                                    $lang => $product->description ?? null,
                                ],
                            'delivery_company_id'     => $deliveryCompany->id,
                            'marketplace_supplier_id' => $product->supplier_id ?? '',
                            'marketplace_product_id'  => $product->id,
                            'marketplace_shipping_method' => $product->shipping_method,
                            'shipping_cost'               => $shippingCost ?? 0,
                            'delivery_days'               => $product->delivery_days,
                            'industry_template_data'      => $product->industry_template_data ? json_encode($product->industry_template_data) : null,
                            'product_type'                => $user_id,//\App\Enums\ProductType::MP_MANUAL_TRANSFER,
                            'mp_category_offer'           => $discount,
                            // Additional columns
                            'manufacturer'                => $manufacturer,
                            'manufacturer_link'           => $manufacturer_link,
                            'manufacturer_id'             => $manufacturer_id,
                            'custom_tariff_number'        => $custom_tariff_number,
                            'shipping_company_id'         => $shipping_company_id ?? 8,
                            'region'                      => $region,
                            'country_of_origin'           => $country_of_origin,
                            'min_stock'                   => $min_stock,
                            'min_order'                   => $min_order,
                            'gross_weight'                => $gross_weight,
                            'net_weight'                  => $net_weight,
                            'product_length'              => $product_length,
                            'product_width'               => $product_width,
                            'product_height'              => $product_height,
                            // 'volume'                      => $volume,
                            'packaging_length'            => $packaging_length,
                            'packaging_width'             => $packaging_width,
                            'packaging_height'            => $packaging_height,
                            'item_unit'                   => $item_unit,
                            'packaging_unit'                => $packing_unit,
                            // 'volume_gross'                => $volume_gross,
                        ];

                        // $drmProductInfo = array_merge($drmProductInfo,$attributes);
                        // $drmProduct = DB::table('drm_products')->insert($drmProductInfo);
                        $drmProduct = DrmProduct::create($drmProductInfo);

                        if ($trial_checked == 0) {
                            app(\App\Http\Controllers\AdminDrmImportsController::class)->setImportTrial($user_id);
                            $trial_checked = 1;
                        }

                        $drm_product_categories[] = [
                            'product_id' => $drmProduct->id,
                            'category_id' => $drmCategory->id,
                        ];

                        $mpCoreData[] = [
                            'drm_product_id' => $drmProduct->id,
                            'marketplace_product_id' => $drmProduct->marketplace_product_id,
                            'user_id' => $drmProduct->user_id,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                        $i++;
                        $autoTranferIds[$productId] = $drmProduct->id;
                    }
                }
            } else if($transferLimit && $transferLimit > 0) {
                // $user_access_status = \App\Models\Marketplace\UserAccess::where('user_id', $user_id)->first()->accessable_categories ?? [];
                // $product_cats = array_diff($categories, $user_access_status);
                // $newArray = [];
                // $allowedCategories = $categories;
                // if(!empty($allowedCategories)){
                //     $all_access_category = UserAccess::where('user_id', $user_id)->select('check_accessable_categories')->get()->toArray();
                //     if(!empty($all_access_category)){
                //         $single_access_category = array_column($all_access_category[0]['check_accessable_categories'], 'accessable_categories');
                //         $single_access_category_user = array_column($all_access_category[0]['check_accessable_categories'], 'set_by_admin');

                //         foreach($allowedCategories as $category){
                //             if(!in_array($category,$single_access_category)){
                //                 $newArray[] = array("accessable_categories"=>$category,"set_by_admin"=>0);
                //             }else{
                //                 $index = array_keys($single_access_category, $category);
                //                 $val = $single_access_category_user[$index[0]];
                //                 $newArray[] =  array("accessable_categories"=>$category,"set_by_admin"=>$val);
                //             }
                //         }
                //         foreach($single_access_category as $key => $old_access){
                //             if(!in_array($old_access,$allowedCategories)){
                //             $val = $single_access_category_user[$key];
                //             if($val == 0){
                //                 $newArray[] = array("accessable_categories"=>$old_access,"set_by_admin"=>$val);
                //             }
                //             }
                //         }
                //     }else{
                //         foreach($allowedCategories as $category){
                //             $newArray[] =  array("accessable_categories"=>$category,"set_by_admin"=>0);
                //         }
                //     }
                // }

                // if(!empty($product_cats)){
                //     $new_merge = array_unique(array_merge($user_access_status , $product_cats));
                // }else{
                //     $new_merge = $user_access_status;
                // }

                // if(!empty($newArray)){
                //     $attributes =  array_merge([
                //         'user_id'           => $user_id,
                //         'status'            => 1,
                //     ],['accessable_categories' => $new_merge], ['check_accessable_categories' => $newArray]);

                //     $newRow = UserAccess::updateOrCreate(['user_id'=>$user_id], $attributes);
                // }

                foreach($tranferable_ids as $productId){
                    if($transferLimit > $i) {
                        $product = Product::with('additionalInfo','productBrand')->where('id', $productId)->first();
                        $ean_exist = DrmProduct::where('ean', $product->ean)->where('user_id', $user_id)->first();
                        if(!$ean_exist){
                            // Additional columns check
                            $manufacturer = null;
                            $manufacturer_link = null;
                            $manufacturer_id = null;
                            $custom_tariff_number = null;
                            $shipping_company_id = null;
                            $region = null;
                            $country_of_origin = null;
                            $min_stock = null;
                            $min_order = null;
                            $gross_weight = null;
                            $net_weight = null;
                            $product_length = null;
                            $product_width = null;
                            $product_height = null;
                            $volume = null;
                            $packaging_length = null;
                            $packaging_width = null;
                            $packaging_height = null;
                            $item_unit = null;
                            $packing_unit = null;
                            $volume_gross = null;

                            if( $product->additionalInfo ){
                                $manufacturer = $product->additionalInfo->manufacturer;
                                $manufacturer_link = $product->additionalInfo->manufacturer_link;
                                $manufacturer_id = $product->additionalInfo->manufacturer_id;
                                $custom_tariff_number = $product->additionalInfo->custom_tariff_number;
                                $shipping_company_id = $product->additionalInfo->shipping_company_id;
                                $region = $product->additionalInfo->region;
                                $country_of_origin = $product->additionalInfo->country_of_origin;
                                $min_stock = $product->additionalInfo->min_stock;
                                $min_order = $product->additionalInfo->min_order;
                                $gross_weight = $product->additionalInfo->gross_weight;
                                $net_weight = $product->additionalInfo->net_weight;
                                $product_length = $product->additionalInfo->product_length;
                                $product_width = $product->additionalInfo->product_width;
                                $product_height = $product->additionalInfo->product_height;
                                $volume = $product->additionalInfo->volume;
                                $packaging_length = $product->additionalInfo->packaging_length;
                                $packaging_width = $product->additionalInfo->packaging_width;
                                $packaging_height = $product->additionalInfo->packaging_height;
                                $item_unit = $product->additionalInfo->item_unit;
                                $packing_unit = $product->additionalInfo->packing_unit;
                                $volume_gross = $product->additionalInfo->volume_gross;
                            }

                            $i++;
                            $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
                            $lang = app('App\Services\UserService')->getProductLanguage($country_id);

                            $discount = 0.0;
                            if ( $product->category_id ) {
                                $category = Category::where('id',$product->category_id)->first();
                                $drmCategory = DrmCategory::where('category_name_'.$lang, $category->name)->where('user_id',$user_id)->first();
                                if( $category->start_date <= now() && $category->end_date >= now()){
                                    $discount = $category->is_offer_active ? $category->discount_percentage : 0.0;
                                }
                                if ( !$drmCategory ) {
                                    $drmCategory = DrmCategory::create([
                                        'category_name_'.$lang => $category->name,
                                        'user_id' => $user_id,
                                        'country_id' => $country_id,
                                    ]);
                                }
                            }
                            $deliveryCompanyRow = [
                                'user_id'       => \App\Enums\Apps::DROPMATIX_ID,
                                'name'          => 'Dropmatix Systema SL',
                                'contact_name'  => 'Dropmatix Systema SL',
                                'zip'           => '07200',
                                'state'         => 'FELANITX',
                                'country_id'    => 8,
                                'email'         => '<EMAIL>',
                                'address'       => 'C/ HORTS, 33',
                                'note'          => 'Marketplace Supplier',
                                'is_marketplace_supplier' => 1,
                            ];
                            $deliveryCompany = DeliveryCompany::where('user_id', $user_id)
                                ->where('name', $deliveryCompanyRow['name'])
                                ->where('email', '<EMAIL>')
                                ->first();
                            if ( !$deliveryCompany ) {
                                $deliveryCompanyRow['user_id'] = $user_id;
                                $deliveryCompany = DeliveryCompany::create($deliveryCompanyRow);
                            }

                            $stock = ($product->shipping_method == 1) ? $product->stock : $product->internel_stock;

                            if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::FULFILLment) {
                                $shippingCost = 5.20;
                            } else if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::DROPSHIPPING) {
                                $shippingCost = $product->shipping_cost;
                            }

                            if ( $product->brand ) {
                                $mpBrandName = (is_numeric($product->brand) ? $product->productBrand->brand_name : $product->brand) ?? '';
                                $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper( $mpBrandName) . '%')->where('user_id',$user_id)->first();
                                if ( $drmBrand ) {
                                    $drmBrand = $drmBrand;
                                } else {
                                    if(!empty($mpBrandName)){
                                        $drmBrand = DropmatixProductBrand::create([
                                            'brand_name' =>  $mpBrandName,
                                            'user_id' => $user_id,
                                            'brand_logo'=>($product->productBrand->brand_logo ?? [])
                                        ]);
                                    }
                                }
                            }

                            if( $product->offer_start_date <= now() && $product->offer_end_date >= now() && $product->is_offer_active == 1){
                                $discount += $product->discount_percentage ? ($product->discount_percentage - $product->discount_percentage/4) : 0.0;
                            }

                            $drmProductInfo             = [
                                'user_id'               => $user_id,
                                'country_id'            => $country_id,
                                'language_id'           => null,
                                'name'                  => $product->brand .' '. $product->name,
                                'item_number'           => $product->item_number,
                                'ean'                   => $product->ean,
                                'additional_eans'       => json_encode($product->additional_eans),
                                'image'                 => $product->image,
                                'ek_price'              => userWiseVkPriceCalculate($product->vk_price ?? 0.0, $user_id, false, $discount, $product->api_id ?? null),
                                'vk_price'              => 0.00,
                                'vat'                   => $product->vat ?? null,
                                'tax_type'              => $product->tax_type ?? 1,
                                'stock'                 => (empty($stock)) ? 0 : $stock,
                                'category'              => $drmCategory->id ?? null,
                                'ean_field'             => 1,
                                'item_weight'           => $product->item_weight ?? null,
                                'item_size'             => $product->item_size ?? null,
                                'item_color'            => $product->item_color ?? null,
                                'note'                  => $product->note ?? null,
                                'production_year'       => $product->production_year ?? null,
                                'brand'                 => $drmBrand ? $drmBrand->id : null,
                                'materials'             => $product->materials ?? null,
                                'tags'                  => $product->tags ?? null,
                                'update_enabled'        => $product->update_enabled ?? null,
                                'status'                => $product->status ?? null,
                                'gender'                => $product->gender ?? null,
                                'uvp'                   => $product->uvp ?? 0.00,
                                'title'                 => [
                                                            $lang => $product->name ?? null,
                                                        ],
                                'update_status'         => makeUpdateStatusJson(),
                                // 'short_description' => json_encode($product->description),
                                    'description' => [
                                        $lang => $product->description ?? null,
                                    ],
                                'delivery_company_id'     => $deliveryCompany->id,
                                'marketplace_supplier_id' => $product->supplier_id ?? '',
                                'marketplace_product_id'  => $product->id,
                                'marketplace_shipping_method' => $product->shipping_method,
                                'shipping_cost'               => $shippingCost ?? 0,
                                'delivery_days'               => $product->delivery_days,
                                'industry_template_data'      => $product->industry_template_data ? json_encode($product->industry_template_data) : null,
                                'product_type'                => $user_id,//\App\Enums\ProductType::MP_MANUAL_TRANSFER,
                                'mp_category_offer'           => $discount,
                                // Additional columns
                                'manufacturer'                => $manufacturer,
                                'manufacturer_link'           => $manufacturer_link,
                                'manufacturer_id'             => $manufacturer_id,
                                'custom_tariff_number'        => $custom_tariff_number,
                                'shipping_company_id'         => $shipping_company_id ?? 8,
                                'region'                      => $region,
                                'country_of_origin'           => $country_of_origin,
                                'min_stock'                   => $min_stock,
                                'min_order'                   => $min_order,
                                'gross_weight'                => $gross_weight,
                                'net_weight'                  => $net_weight,
                                'product_length'              => $product_length,
                                'product_width'               => $product_width,
                                'product_height'              => $product_height,
                                // 'volume'                      => $volume,
                                'packaging_length'            => $packaging_length,
                                'packaging_width'             => $packaging_width,
                                'packaging_height'            => $packaging_height,
                                'item_unit'                   => $item_unit,
                                'packaging_unit'              => $packing_unit,
                                // 'volume_gross'                => $volume_gross,
                            ];
                            // $drmProductInfo = array_merge($drmProductInfo,[]);
                            // $drmProduct = DB::connection('marketplace')->table('drm_products')->insert($drmProductInfo);
                            // $drmProduct = DB::connection('drm_db')->table('drm_products')->insert($drmProductInfo);

                            $drmProduct = DrmProduct::create($drmProductInfo);

                            if ($trial_checked == 0) {
                                app(\App\Http\Controllers\AdminDrmImportsController::class)->setImportTrial($user_id);
                                $trial_checked = 1;
                            }
                            $drm_product_categories[] = [
                                'product_id' => $drmProduct->id,
                                'category_id' => $drmCategory->id,
                            ];
                            $mpCoreData[] = [
                                'drm_product_id' => $drmProduct->id,
                                'marketplace_product_id' => $drmProduct->marketplace_product_id,
                                'user_id' => $drmProduct->user_id,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ];

                            $autoTranferIds[$productId] = $drmProduct->id;
                        }

                    }
                }
            } else {
                Log::info('User DRM products transfer limit exceed! Please Upgrade your tarif plan.');
            }


            DB::table('drm_product_categories')->insert($drm_product_categories);

            MpCoreDrmTransferProduct::insert($mpCoreData);

            // //DB::commit();

            $one_one_sync = DB::table('cms_users')->select('one_one_sync')->where('id', $user_id)->get();
            if($one_one_sync[0]->one_one_sync == 1){
                app(TransferProduct::class)->transferSynchronizedProductsToAnalysis($user_id);
            }

            if(professionalOrHigher($user_id)){
                AutoTransfer::dispatch(array_values($autoTranferIds),$user_id,$lang ?? "de");
            }


        } catch (\Exception $e) {
            Log::info('MP product direct transfer job fail');
            Log::info($e);
            // //DB::rollBack();
        }
    }
}

<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class MarketplaceProductCalculationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    protected $product_ids;
    protected $calculation;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($product_ids, $calculation)
    {
        $this->product_ids = $product_ids;
        $this->calculation = $calculation;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
       info('Marketplace Product CalculationJob start..............');
       app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->assignMarketplaceProductCalculation($this->product_ids, $this->calculation);
    }
}

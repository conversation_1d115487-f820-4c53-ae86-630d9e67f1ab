<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Marketplace\Product;
use App\Models\Marketplace\Category;
use App\Models\Marketplace\UserAccess;


class MarketplaceProductTransferBasedOnDrmEan implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $ean;
    protected $user_id;

    public function __construct($ean = [], $user_id = null)
    {
        $this->ean = $ean;
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $category_ids = array_unique(Product::whereIn('ean', $this->ean)->orWhereIn('item_number',$this->ean)->pluck('category_id')->toArray() ?? []);
        $active_cateogry = Category::whereIn('id', $category_ids)->where('is_active', 1)->pluck('id')->toArray() ?? [];
        $access_category = UserAccess::where('user_id', $this->user_id)->pluck('accessable_categories')->toArray();
        $new_access_category = array_merge(array_diff(array_map('strval',$active_cateogry), $access_category[0] ?? []), $access_category[0] ?? []);
        UserAccess::where('user_id', $this->user_id)->update(['accessable_categories' => $new_access_category]);
        $product_ids = Product::whereIn('category_id', $active_cateogry)->whereIn('ean', $this->ean)->orWhereIn('item_number',$this->ean)->pluck('id')->toArray() ?? [];

        $importProduct = app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->getImportPlan($user_id);
        $transferLimit = $importProduct ? $importProduct['product_amount'] : 0;
        $transferPlan = $importProduct['plan'] ? $importProduct['plan'] : null;
        $transferPlanLimit = $importProduct['limit'] ? $importProduct['limit'] : null;

        if((($transferPlan && $transferPlan == 'Trial') && ($transferPlanLimit && $transferPlanLimit == 'Unlimited')) || ($transferPlanLimit && $transferPlanLimit == 'Unlimited')){
            app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferUnlimitedProductsToDrm($this->tranferable_ids, $this->user_id, $this->category_id);
        } else if($transferLimit && $transferLimit > 0){
            app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferTarifLimitedProductsToDrm($this->tranferable_ids, $this->user_id, $this->category_id);
        } else {
            
        }
        // app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferAllFilteredProductsToDrm($product_ids, [], null, $this->user_id);
    }
}

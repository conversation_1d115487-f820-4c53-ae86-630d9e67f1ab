<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;


class MarketplaceZeroShippingProductUpdate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $drm_product_ids;
    protected $marketplace_product_ids;
    protected $tariff_id;


    public function __construct($drm_product_ids = [], $marketplace_product_ids = [], $tariff_id = null)
    {
        $this->drm_product_ids   = $drm_product_ids;
        $this->marketplace_product_ids = $marketplace_product_ids;
        $this->tariff_id = $tariff_id;
    }
   
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() 
    {
        if ($this->marketplace_product_ids) {
            // info('Zero shipping update amount: '.count($this->marketplace_product_ids));
            $convert_drm_products = array_flip($this->drm_product_ids);
            foreach ($this->marketplace_product_ids as $marketplace_product_id => $shipping_cost) {
                $updateableColumns['shipping_cost'] = ($this->tariff_id == 27) ? 0.00 : $shipping_cost;
                app(\App\Services\DRMProductService::class)->update($convert_drm_products[$marketplace_product_id], $updateableColumns);
            }
        }
    }
}

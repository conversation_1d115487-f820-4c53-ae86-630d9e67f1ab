<?php

namespace App\Jobs\Marketplace;

use App\DrmProduct;
use Illuminate\Bus\Queueable;
use App\Models\Marketplace\Product;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\Marketplace\MpCoreDrmTransferProduct;
use Carbon\Carbon;

class CategoryDiscountRemoveSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $category;
    public function __construct($category)
    {
        $this->category = $category;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $category = $this->category;
        $productIds = Product::where('category_id',$category->id)->pluck('id')->toArray();
        $checkExistInDrm = MpCoreDrmTransferProduct::whereIn('marketplace_product_id',$productIds)->get();
        if(!blank($checkExistInDrm)){
            foreach($checkExistInDrm as $product){
                $marketPlaceProduct = Product::select('vk_price','supplier_id','offer_start_date','offer_end_date','is_offer_active','discount_percentage', 'api_id')->where('id', $product->marketplace_product_id)->first()->toArray();
                $drmProduct = DrmProduct::select('id','update_status','user_id')->where('id', $product->drm_product_id)->first();
                if(!blank($drmProduct)){
                    $product_discount = 0.0;
                    if($marketPlaceProduct['offer_start_date'] <= Carbon::now() && $marketPlaceProduct['offer_end_date'] >= Carbon::now() && $marketPlaceProduct['is_offer_active'] == 1){
                        $product_discount = $marketPlaceProduct['discount_percentage'] ? ($marketPlaceProduct['discount_percentage'] - $marketPlaceProduct['discount_percentage'] / 4) : 0.0;
                    }
                    $updateableColumns = [];
                    $country_id = app('App\Services\UserService')->getProductCountry($marketPlaceProduct['supplier_id']);
                    $lang = app('App\Services\UserService')->getProductLanguage($country_id);
    
                    $update_status = json_decode($drmProduct['update_status'], 1);
    
                    if ( isset($update_status['ek_price']) && $update_status['ek_price'] == 1 ) {
                        $updateableColumns['ek_price'] = userWiseVkPriceCalculate($marketPlaceProduct['vk_price'] ?? 0.0, $drmProduct['user_id'], false, $product_discount, $marketPlaceProduct['api_id'] ?? null);
                    }
    
                    $updateableColumns['mp_category_offer'] = 0.0 + $product_discount;
                    app(\App\Services\DRMProductService::class)->update($drmProduct['id'], $updateableColumns, $lang);
                }
            }
        }
        $category->is_offer_active = 0;
        $category->is_parent_offer = 0;
        $category->update();
    }
}

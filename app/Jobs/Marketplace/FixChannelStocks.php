<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Marketplace\CoreSyncReport;
use App\DrmProduct;

class FixChannelStocks implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    public function __construct()
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        if(empty(\App\Enums\V2UserAccess::USERS)) return;

        $count = 0;
        $deletedCount = 0;
        $reports = CoreSyncReport::where('fixed', 0)
            ->whereNotIn('user_id', \App\Enums\V2UserAccess::USERS)
            ->where(function ($q) {
                $q->whereRaw('CAST(channel_price as SIGNED) != CAST(drm_price as SIGNED)')
                    ->orWhereRaw('CAST(channel_stock as SIGNED) != CAST(drm_stock AS SIGNED)');
            })->cursor();


            foreach($reports->chunk(1000) as $chunk){
                $drmProducts = DrmProduct::whereIn('id', $chunk->pluck('drm_product_id')->toArray())
                ->select(
                    'id',
                    'stock',
                    'ek_price'
                )->get();

                foreach ($chunk as $report) {
                    $drmProduct = $drmProducts->where('id', $report->drm_product_id)->first();

                    if($drmProduct)
                    {
                        if($report->stock_percent > 0){
                            $drmProduct->stock = floor($drmProduct->stock * $report->stock_percent / 100);
                        }

                        $drmProduct = $drmProduct->toArray();
                        unset($drmProduct['id']);
                        app('App\Services\ChannelProductService')->update(
                            $report->channel_product_id,
                            $drmProduct
                        );
                        // echo "\r Processed " . $count++ . " Products";
                    }
                    else{
                        app('App\Services\ChannelProductService')->destroy(
                            $report->channel_product_id,
                            $report->channel,
                            $report->user_id
                        );
                        // echo "\r Deleted " . $deletedCount++ . " Products";
                    }

                    $report->fixed = true;
                    $report->save();
                }
            }
    }
}

<?php

namespace App\Jobs\Marketplace;

use App\Mail\DRMSEndMail;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class AlarmQuantityJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $mail;
    protected $count;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($mail,$count)
    {
        $this->mail = $mail;
        $this->count = $count;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $slug = "product_quantity_alarm";
        $tags = [
            'user' => $this->count,
            'marketplace_product_list' => 'drm.software/admin/marketplace_products?low_stock_product',
        ];
        $mail_data = DRMParseMailTemplate($tags, $slug);
        app('drm.mailer')->getMailer()->to($this->mail)->send(new DRMSEndMail($mail_data));
    }
}

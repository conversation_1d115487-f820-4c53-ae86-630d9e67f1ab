<?php

namespace App\Jobs\ChannelManager;

use Illuminate\Bus\Queueable;
use App\Models\ChannelProduct;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use App\Services\Modules\Export\Colizey;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Enums\ChannelProductConnectedStatus;

class ColizeyFeedBack implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $shop_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shop_id)
    {
        $this->shop_id = $shop_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {

        $shop = \App\Shop::where(['id' => $this->shop_id, 'status' => true])->first();
        $reports = app(Colizey::class)->productUpdateByFeedBack($shop);

        if ($reports['success']) {
            $skus = $reports['data']->where('sku', '!=', null);

            // Update ChannelProduct statuses and unpublish products in chunks of 100
            ChannelProduct::whereIn('item_number', $skus->pluck('sku')->toArray())
                ->where('channel', $reports['channel'])
                ->chunk(100, function ($chunkedSkus) use ($skus, $shop) {
                    foreach ($chunkedSkus as $sku) {
                        // Find the corresponding SKU in the original data
                        $originalSku = $skus->firstWhere('sku', $sku->item_number);

                        // Update ChannelProduct status
                        $sku->update([
                            'connection_status' => ChannelProductConnectedStatus::ERROR,
                            'error_response' => [$originalSku['error']]
                        ]);
                    }

                    // Unpublish products using Skus
                    // $skusToUnpublish = $chunkedSkus->pluck('sku')->unique()->toArray();
                    // app(Colizey::class)->unpublishProductsUsingSkus(
                    //     json_encode($skusToUnpublish),
                    //     $shop->password
                    // );
                });

                // Update ChannelProduct statuses for remaining items
                ChannelProduct::where([
                    'channel' => $reports['channel'],
                    'connection_status' => ChannelProductConnectedStatus::PENDING
                ])->chunk(100, function ($channelProducts) use ($shop) {
                    $skusToPublish = $channelProducts->pluck('item_number')->toArray();

                    app(Colizey::class)->publishProductsUsingSkus(
                        json_encode($skusToPublish),
                        $shop->password
                    );

                    // Update the connection status to CONNECTED in bulk
                    ChannelProduct::whereIn('id', $channelProducts->pluck('id')->toArray())
                        ->update(['connection_status' => ChannelProductConnectedStatus::CONNECTED]);
                });


        }

        } catch(\Exception $e) {}
    }
}

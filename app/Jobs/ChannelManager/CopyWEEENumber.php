<?php

namespace App\Jobs\ChannelManager;

use App\DropmatixProductBrand;
use App\Models\ChannelProduct;
use App\Services\ChannelProductService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CopyWEEENumber implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 0;
    public int $tries = 3;
    private array $product_ids;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($product_ids)
    {
        $this->product_ids = $product_ids;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        try{
        foreach (array_chunk($this->product_ids,500) as $items) {
            $products = ChannelProduct::whereNotNull('brand')->where('brand','!=','')->whereIn('id',$items)->get();

            foreach ($items as $product_id) {
                $product = $products->where('id',$product_id)->first();
                $aspects = $product->category_aspects ?? array();

                if (!is_array($aspects)) {
                    $aspects = json_decode($aspects,true);
                }

                $weee_number = DropmatixProductBrand::where('otto_brand_id',$product->brand)->value('weee_number') ?? '';
                $aspects['WEEE-Reg.-Nr. DE'] = $weee_number;
                app(ChannelProductService::class)->update($product_id,['category_aspects' => $aspects]);
            }
        }

        } catch(\Exception $e) {}

    }

    public function tags()
    {

    }
}

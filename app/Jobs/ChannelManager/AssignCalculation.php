<?php

namespace App\Jobs\ChannelManager;

use App\Enums\CreditType;
use App\Models\Product\ProfitCalculation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AssignCalculation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $ids;
    protected int $channel;
    protected ProfitCalculation $calculation;
    protected bool $is_repricing;
    protected $repricing_calculation;
    public int $timeout = 0;
    public int $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($ids, ProfitCalculation $calculation, $channel, $is_repricing = false, $repricing_calculation = null)
    {
        $this->ids = $ids;
        $this->channel = $channel;
        $this->calculation = $calculation;
        $this->is_repricing = $is_repricing;
        $this->repricing_calculation = $repricing_calculation;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            app('\App\Services\ChannelProductService')->assignCalc(
                $this->ids,
                $this->calculation,
                $this->channel,
                $this->is_repricing,
                $this->repricing_calculation,
            );
        } catch(\Exception $e) {}
    }

    public function tags()
    {

    }
}

<?php

namespace App\Jobs\ChannelManager;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AssignCategory implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 0;
    public int $tries = 3;
    protected array $product_ids;
    protected array $categories;
    protected int $channel;
    protected int $user_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($product_ids,$categories,$channel,$user_id)
    {
        $this->product_ids = $product_ids;
        $this->categories = $categories;
        $this->channel = $channel;
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{
            app('App\Services\ChannelProductService')->bulkUpdateCategory(
                $this->product_ids,
                $this->channel,
                $this->user_id,
                $this->categories
            );
        } catch(\Exception $e) {}
    }

    public function tags()
    {

    }
}

<?php

namespace App\Jobs\ChannelManager;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SyncFromMainLevel implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $ids;
    protected array $fields;
    protected string $lang;
    protected int $shop_id;
    protected int $channel_id;
    public int $timeout = 0;
    public int $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($ids, $channel, $shop_id, $fields, $lang)
    {
        $this->ids = $ids;
        $this->channel_id = $channel;
        $this->fields = $fields;
        $this->lang = $lang;
        $this->shop_id = $shop_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        foreach ($this->ids as $id) {
            app('\App\Services\ChannelProductService')->syncFromMainLevel(
                $id,
                $this->channel_id,
                $this->shop_id,
                $this->fields,
                $this->lang
            );
        }

        } catch(\Exception $e) {}
    }

    public function tags()
    {

    }
}

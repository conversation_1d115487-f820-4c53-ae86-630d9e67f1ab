<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class NotifyDroptiendaProductStock implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($id)
    {
        $this->id = $id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        ini_set("memory_limit",-1);
        app('App\Http\Controllers\Api\ApiController')->sendDroptiendaStockMail($this->id, 'drm_dt_stock_mail');

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        return ['Droptienda product notify: '.$this->id ];
    }
}

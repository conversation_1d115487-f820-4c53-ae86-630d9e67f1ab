<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Models\Product\GoogleMerchantReport;
use App\Models\Product\GoogleMerchantHistory;
use App\Models\ChannelProduct;
use App\Models\Product\AnalysisProduct;
use Carbon\Carbon;

class ProcessGoogleMerchantUpdateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $id;
    protected $user_id;
    protected $uri;
    protected $refresh_uri;

    public function __construct($id, $user_id)
    {
        $this->id = $id;
        $this->user_id = $user_id;
        $this->uri = "https://content-shoppingcontent.googleapis.com/content/v2.1/";
        $this->refresh_uri = "https://www.googleapis.com/oauth2/v4/token";
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{
            $authToken = DB::table('google_oauth')->where('id', $this->id)->first();

            $refresh_token_body['client_id'] = "252032317121-vsmbde25r7hj2juklrgmfp65888o1o6n.apps.googleusercontent.com";
            $refresh_token_body['client_secret'] = "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";
            $refresh_token_body['refresh_token'] = $authToken->google_refresh_token;
            $refresh_token_body['grant_type'] = "refresh_token";

            $response = Http::withBody(
                json_encode($refresh_token_body), 'json'
            )->post($this->refresh_uri);

            $access_token = json_decode($response, true)['access_token'];
            DB::table('google_oauth')->where('id', $this->id)->update([
                'google_access_token' => $access_token
            ]);

            $this->uri = $this->uri . $authToken->merchant_id . "/reports/search";

            $currentDate = Carbon::now()->toDateString();
            $startDate = Carbon::now()->subMonths(12)->toDateString();

            $query['query'] = "SELECT metrics.impressions, metrics.clicks, metrics.ctr, segments.offer_id FROM MerchantPerformanceView WHERE segments.date BETWEEN '" . $startDate . "' AND '" . $currentDate ."'";
            $historyQuery['query'] = "SELECT metrics.impressions, metrics.clicks, metrics.ctr, segments.date FROM MerchantPerformanceView WHERE segments.date BETWEEN '" . $startDate . "' AND '" . $currentDate ."'";

            $response = Http::withBody(
                json_encode($query), 'json'
            )->withToken($access_token)->post($this->uri, [
                'alt' => "json",
                'key' => "AIzaSyDY1u43idcPtU0X2Mcqv6IZ9l--ZvLplGE"
            ]);
            $result = json_decode($response, true);
            $products = $result['results'];

            $query['query'] = "SELECT product_view.id, product_view.offer_id, product_view.title, product_view.brand, product_view.gtin FROM ProductView";

            $response = Http::withBody(
                json_encode($query), 'json'
            )->withToken($access_token)->post($this->uri, [
                'alt' => "json",
                'key' => "AIzaSyDY1u43idcPtU0X2Mcqv6IZ9l--ZvLplGE"
            ]);
            $result = json_decode($response, true);
            $gtinList = $result['results'];

            $reportList = [];
            if($gtinList){
                foreach($gtinList as $item){
                    if($item['productView']['gtin']){
                        $ean = ltrim($item['productView']['gtin'][0], "0");
                        $ean = str_pad($ean,13,"0",STR_PAD_LEFT);
                        $offerId = $item['productView']['offerId'];
                        $report = [];
                        $report['user_id'] = $this->user_id;
                        $report['offer_id'] = $offerId;
                        $report['ean'] = $ean;
                        $report['title'] = $item['productView']['title'] ?? "N/A";
                        $report['brand'] = $item['productView']['brand'] ?? "N/A";
                        $report['clicks'] = 0;
                        $report['impressions'] = 0;
                        $report['ctr'] = 0;
                        $report['channel_product_id'] = null;
                        $report['analysis_product_id'] = null;
                        $channel_product = ChannelProduct::where('user_id', $this->user_id)->where('channel', 10)->where('ean', $ean)->first();
                        $analysis_product = AnalysisProduct::where('user_id', $this->user_id)->where('ean', $ean)->where('archived', '!=', 1)->first();
                        if($channel_product){
                            $report['channel_product_id'] = $channel_product->id;
                        }
                        else{
                            $report['channel_product_id'] = null;
                        }
                        if($analysis_product){
                            $report['analysis_product_id'] = $analysis_product->id;
                        }
                        else{
                            $report['analysis_product_id'] = null;
                        }
                        if($products){
                            foreach($products as $product){
                                if($product['segments']['offerId'] == $offerId){
                                    $report['clicks'] = $product['metrics']['clicks'];
                                    $report['impressions'] = $product['metrics']['impressions'];
                                    $report['ctr'] = $product['metrics']['ctr'];
                                    break;
                                }
                            }
                        }
                        array_push($reportList, $report);
                    }
                }
            }
            $chunks = array_chunk($reportList, 2000);
            foreach($chunks as $chunk){
                foreach($chunk as $product){
                    GoogleMerchantReport::updateOrInsert(
                        [
                            'offer_id' => $product['offer_id'],
                            'user_id' => $this->user_id
                        ],
                        $product
                    );
                }
                
            }

            $response = Http::withBody(
                json_encode($historyQuery), 'json'
            )->withToken($access_token)->post($this->uri, [
                'alt' => "json",
                'key' => "AIzaSyDY1u43idcPtU0X2Mcqv6IZ9l--ZvLplGE"
            ]);

            $result = json_decode($response, true);
            $history = $result['results'];

            $clicksHistory = [];
            $impressionsHistory = [];
            $ctrHistory = [];

            if($history){
                foreach ($history as $metric) {

                    $date = $metric['segments']['date']['year'] . "-" . str_pad($metric['segments']['date']['month'], 2, '0', STR_PAD_LEFT) . "-" . str_pad($metric['segments']['date']['day'], 2, '0', STR_PAD_LEFT);
    
                    $clicksHistory[] = ['x' => $date, 'y' => $metric['metrics']['clicks'] ];
          
                    $impressionsHistory[] = ['x' => $date, 'y' => $metric['metrics']['impressions'] ];
          
                    $ctrHistory[] = ['x' => $date, 'y' => round($metric['metrics']['ctr'], 6) ];
    
                }
            }
      
            $googleHistory = [];
      
            $googleHistory[] = [
                'name' => 'Klicks',
                'data' => $clicksHistory
            ];
            $googleHistory[] = [
                'name' => 'Impressionen',
                'data' => $impressionsHistory
            ];
            $googleHistory[] = [
                'name' => 'CTR',
                'data' => $ctrHistory
            ];

            GoogleMerchantHistory::create([
                'user_id' => $this->user_id,
                'history' => json_encode($googleHistory),
            ]);
        } catch (Exception $e){}
    }
}

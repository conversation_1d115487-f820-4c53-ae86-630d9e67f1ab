<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use DB;
use App\Services\Modules\Import\ImageBackupService;

class ImportImageBackup implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $ids;
    protected $import;
    public $timeout = 0;
    public $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($ids,$import)
    {
        $this->ids = $ids;
        $this->import = $import;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $service = new ImageBackupService($this->import);
        $service->backupImages($this->ids);

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        return ['Image backup: ' . $this->import->csv_file_name];
    }
}

<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\NewOrder;
use App\Shop;
use DB;
use App\DrmUserLockUnlock;

class UserAccountStatusJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $this->duePaymentUsersLock();

        $this->withoutImportPlanUsersLock();

        } catch(\Exception $e) {}
    }

    private function duePaymentUsersLock()
    {
        $due_payment_orders_user_ids = NewOrder::where('status', 'inkasso')
        ->where('test_order', '<>', 1)
        ->where('invoice_number', '>', 0)
        ->whereNull('credit_ref')
        ->where('marketplace_paid_status', '<>', 1)
        ->whereNotNull('cms_client')
        ->distinct()
        ->pluck('cms_client')
        ->toArray();

        if(!empty($due_payment_orders_user_ids)){
            foreach($due_payment_orders_user_ids as $user_id){
                $this->lockUser($user_id);
            }
        }
    }

    private function withoutImportPlanUsersLock()
    {
        // Get users id from shops table, who has at least one channel
        $has_channel_user_ids = Shop::where('status', 1)
        ->distinct()
        ->pluck('user_id')
        ->toArray();

        if(!empty($has_channel_user_ids)){
            foreach($has_channel_user_ids as $user_id){
                $user_status = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);

                if(checkTariffEligibility($user_id)){
                    if(in_array($user_status['plan'], ['500 Free Products'])){
                        $this->lockUser($user_id);
                    }
                }else{
                    if($user_status['blocked'] != "" || $user_status['product_amount'] <= 0){
                        $this->lockUser($user_id);
                    }
                }
            }
        }
    }

    private function lockUser($user_id)
    {
        DrmUserLockUnlock::updateOrCreate(
            ['user_id' => $user_id],
            ['status' => 1] // Status 1 = Lock and 0 = Unlock
        );

        $user = \App\User::where('id', $user_id)->first();
		resolve(\App\Services\DropCampus\DropmatixCampus::class)->deactivateDropmatixCampusUser($user);
    }
}

<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\Services\ChannelProductService;

class DestroyChannelProduct implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $ids;
    protected int $shop_id;
    protected int $user_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($ids, $shop_id, $user_id)
    {
        $this->ids = $ids;
        $this->shop_id = $shop_id;
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app(ChannelProductService::class)->destroy($this->ids, $this->shop_id, $this->user_id);

        } catch(\Exception $e) {}
    }

    public function tags(): array
    {
        return ['Deleting Channel Products ...'];
    }
}

<?php

namespace App\Jobs;

use App\Http\Controllers\EtsyController;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class EtsyProductExportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 600;
    public $tries = 1;

    protected $productIds;
    protected $userId;
    protected $shippingTemplateId;

    /**
     * Create a new job instance.
     *
     * @param $productIds
     * @param $userId
     * @param $shippingTemplateId
     */
    public function __construct($productIds, $userId, $shippingTemplateId = null)
    {
        $this->productIds = $productIds;
        $this->userId = $userId;
        $this->shippingTemplateId = $shippingTemplateId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app(EtsyController::class)->exportDrmProduct($this->productIds, $this->userId, $this->shippingTemplateId);

        } catch(\Exception $e) {}
    }
}

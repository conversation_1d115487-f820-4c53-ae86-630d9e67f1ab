<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Jobs\SendProformaRestoreEmail;
use App\Notifications\DRMNotification;
use App\User;
use App\NewOrder;
use DB;

class ProformaInvoiceRestoreJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {


        try{
            $today_date = date('Y-m-d');
            $proferma_orders = NewOrder::with(['customer', 'client'])
                ->where('invoice_number', -1)
                ->whereNotNull('invoice_date')
                ->whereRaw('cast(invoice_date as date) <= \'' .$today_date. '\'')
                ->orderByRaw('CAST(order_date AS datetime) asc')
                ->select('id', 'invoice_date', 'cms_user_id', 'drm_customer_id', 'cms_client', 'total', 'currency', 'order_history', 'forecast_batch', 'order_date')
                ->get();

            //Check if performa order exist
            if($proferma_orders){
                foreach ($proferma_orders as $proferma_order) {

                    //User id
                    $cms_user_id = $proferma_order->cms_user_id;

                    // Invoice number generator start
                    $inv_setting = DB::table('drm_invoice_setting')->where('cms_user_id', $cms_user_id)->orderBy('id', 'desc')->select('start_from_1', 'start_invoice_number', 'suffix')->first();
                    $start_from_1 = (bool)$inv_setting->start_from_1;
                    $inv_suffix = $inv_setting->suffix;

                    $last_order_item = DB::table('new_orders')->where('cms_user_id', $cms_user_id)->where('invoice_number', '!=', -1)->where('credit_number', 0);

                    if ($start_from_1) {
                        $last_order_item->whereYear('created_at', date('Y'));
                    }

                    $inv1 = $last_order_item->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;
                    $inv2 = $inv_setting->start_invoice_number;
                    $invoice_number = ($start_from_1) ? $inv1 : (($inv1 > $inv2) ? $inv1 : $inv2);


                    //Extra layer of duplication check
                    $inv_used_count = DB::table('new_orders')->where(['cms_user_id' => $cms_user_id, 'invoice_number' => $invoice_number])->where('credit_number', 0);
                    if ($start_from_1) {
                        $inv_used_count->whereYear('created_at', date('Y'));
                    }
                    $inv_used_count = $inv_used_count->count();

                    $inv_pattern = drm_invoice_number_format($invoice_number, $inv_suffix);
                    $inv_string = inv_number_string($invoice_number, $inv_pattern);

                    if ($inv_used_count > 0) {
                        User::find(71)->notify(new DRMNotification('DUPLICATE INV NUBMER TRY: ' . $inv_string . ' USER ID: ' . $cms_user_id . ' SHOP_ID: ' . $shop_id . ' API_ID: ' . $order_id_api, 'DUPLICATE_INV_TRY'));
                        return false;
                    }

                    //Invoice number generator end

                    //Update invoice data
                    if($proferma_order->update(['invoice_number' => $invoice_number, 'order_date' => date('Y-m-d H:i:s'), 'inv_pattern' => $inv_pattern ])){
                        $url = url('admin/drm_all_orders/detail/'.$proferma_order->id);

                        $customer_name = ($proferma_order->customer)? $proferma_order->customer->full_name : ( ($proferma_order->client)? $proferma_order->client->name : '' );
                        $notification_message = 'New earmarked invoice now published. Invoice number '.$inv_string.' of customer '.$customer_name.' with amount '. number_format((float)$proferma_order->total, 2, ',', '.').' '.$proferma_order->currency;
                        User::find($proferma_order->cms_user_id)->notify(new DRMNotification($notification_message, 'ORDER_MODULE', $url));
                        updateOrderHistory($proferma_order, $proferma_order->status, 'Order restored successfully! Invoice: '.$inv_string );

                        $this->lastForecastBatchNotification($proferma_order);

                        //Auto email
                        $user_mail_setting = DB::table('drm_order_mail')
                        ->where( 'cms_user_id', $proferma_order->cms_user_id )
                        ->whereNull('channel')
                        ->select('auto_mail')
                        ->first();

                        if($user_mail_setting && $user_mail_setting->auto_mail)
                        {
                            SendProformaRestoreEmail::dispatch($proferma_order->id);
                        }
                    }
                }
            }
        }catch(\Exception $e){
            User::find(71)->notify(new DRMNotification('Performa invoice restore error '.$e->getMessage().' Line:'.$e->getLine(), 'ORDER_MODULE_ERROR', '#'));
        }
    }

    public function tags()
    {
        return ['Proforma Invoice restore'];
    }

    //Forecast batch notification
    private function lastForecastBatchNotification(NewOrder $order)
    {
        if(empty($order->forecast_batch)) return;

        $remains = DB::table('new_orders')
            ->whereNull('deleted_at')
            ->where('cms_user_id', $order->cms_user_id)
            ->where('invoice_number', '=', -1)
            ->where('credit_number', 0)
            ->where('forecast_batch', $order->forecast_batch)
            ->exists();

        if(!$remains)
        {
            $order_date = $order->order_date;
            $url = url('admin/drm_all_orders/detail/'.$order->id);
            $message = 'Your last forecast invoice ('.$order_date.') generated. You may create new forecast invoice';
            User::find($order->cms_user_id)->notify(new DRMNotification($message, 'FORCAST_BATCH_COMPLETED', $url));
        }
    }
}

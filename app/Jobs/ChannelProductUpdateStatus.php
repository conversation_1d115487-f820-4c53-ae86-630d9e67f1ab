<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\Services\ChannelProductService;

class ChannelProductUpdateStatus implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $channelProductId;
    protected $channelProductUpdateStatus;
    protected $drmProductId;
    protected $activeChannelId;
    protected $updateStatusFields;
    protected $lang;
    protected $channelProductStatusEvent;

    public function __construct($channelProductId, $channelProductUpdateStatus, $drmProductId, $activeChannelId, $updateStatusFields, $lang, $channelProductStatusEvent)
    {
        $this->channelProductId = $channelProductId;
        $this->channelProductUpdateStatus = $channelProductUpdateStatus;
        $this->drmProductId = $drmProductId;
        $this->activeChannelId = $activeChannelId;
        $this->updateStatusFields = $updateStatusFields;
        $this->lang = $lang;
        $this->channelProductStatusEvent = $channelProductStatusEvent;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        $product = \App\Models\ChannelProduct::where('id', $this->channelProductId)->first();
        $product->update_status = $this->channelProductUpdateStatus;
        $product->save();

        $channelProductService = new ChannelProductService;
        $channelProductService->transferProduct(
            $this->drmProductId,
            $this->activeChannelId,
            $this->updateStatusFields,
            $this->lang,
            $this->channelProductStatusEvent,
            true,
            0,
            ['shop_id' => $product->shop_id]
        );

        } catch(\Exception $e) {}
    }

    public function tags()
    {
        return ['Channel Product Status Updating ...'];
    }
}

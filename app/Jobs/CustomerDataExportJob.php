<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\Services\Customer\CustomerDataExportProcess;

class CustomerDataExportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $payload_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($payload_id)
    {
        $this->payload_id = $payload_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        app(CustomerDataExportProcess::class)->start($this->payload_id);

        } catch(\Exception $e) {}
    }
}

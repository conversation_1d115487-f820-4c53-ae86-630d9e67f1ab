<?php

namespace App\Jobs;

use App\Models\Product\AnalysisProduct;
use App\Services\ProductApi\Services\Countdown;
use App\Services\ProductApi\Services\GoogleShopping;
use App\Services\ProductApi\Services\ProductApiInterface;
use App\Services\ProductApi\Services\Rainforest;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class FindRequestId implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected $products;
    protected $user_id;
    protected $analysisService;

    public function __construct($products, $user_id)
    {
        $this->products = $products;
        $this->user_id = $user_id;
        $this->analysisService = [
            Countdown::class,
            Rainforest::class,
            GoogleShopping::class,
        ];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
        foreach($this->products as $product){
            $product = (object)$product;
            $analysisService = $this->analysisService;
            foreach($analysisService as $service){
                $analysisApi = new $service;
                $columnPrefix = $analysisApi->columnPrefix();
                $column_name = trim($columnPrefix)."id";
                $service_id = DB::table('analysis_products')->select($column_name)->where('ean', $product->ean)->where('user_id', $this->user_id)->get();
                $collection_id_column_name = trim($columnPrefix)."collection_id";
                $collection_id = DB::table('c_p_analysis_requests')->select($collection_id_column_name)->where('id', $service_id[0]->$column_name)->get();
                $custom_id = trim($product->ean)."-".$this->user_id;
                $request_id = $analysisApi->findRequest($collection_id[0]->$collection_id_column_name, $custom_id);
                $column_name = trim($columnPrefix)."request_id";
                AnalysisProduct::where('ean',$product->ean)
                ->where('user_id', $this->user_id)
                ->update([$column_name => $request_id]);
            }
        }

        } catch(\Exception $e) {}

    }
}

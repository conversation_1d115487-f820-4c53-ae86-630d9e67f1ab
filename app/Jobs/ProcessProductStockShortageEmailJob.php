<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\DrmProduct;
use App\Mail\DRMSEndMail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Exception;

class ProcessProductStockShortageEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $analysisService;

    public function __construct()
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            // $user_list = DrmProduct::select('user_id')->where('stock', '!=', null)->where('min_stock', '!=', null)->where('stock', '<', 'min_stock')->distinct()->get();
            // foreach($user_list as $user){

            //     $product_list = DrmProduct::where('user_id', $user->user_id)->where('stock', '!=', null)->where('min_stock', '!=', null)->where('stock', '<', 'min_stock')->get();

            //     $product_data['page_title'] = 'Test drm product stock shortage email template';
            //     $product_data['product_list'] = $product_list;
            //     $product_data['user'] = DB::table('cms_users')->where('id', $user->user_id)->first();
            //     $product_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

            //     $logo = $product_data['setting']->logo ?? '';

            //     $tags = [
            //         'user' => $product_data['user'],
            //         'user_name' => $product_data['user']->name,
            //         'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $producte_data['setting']->store_name . '" >',
            //         'product_name' => $product->title['de'],
            //         'product_table' => view('admin.drm_products.email_product_table', compact('product_list'))->render(),
            //     ];

            //     $slug = 'drm_product_stock_shortage_email_template';
            //     $mail_data = DRMParseMailTemplate($tags, $slug, 'de');
            //     // Mail::to($product_data['user']->email)->send(new DRMSEndMail($mail_data));
            //     app('drm.mailer')->getMailer()->to("<EMAIL>")->send(new DRMSEndMail($mail_data));

            // }

            DrmProduct::whereNotNull('stock')
                ->whereNotNull('min_stock')
                ->where('stock', '<', 'min_stock')
                ->distinct()
                ->pluck('user_id')->each(function($user_id){
                    $product_list = DrmProduct::where('user_id', $user_id)
                        ->whereNotNull('stock')
                        ->whereNotNull('min_stock')
                        ->where('stock', '<', 'min_stock')
                        ->get(['id','title','stock','min_stock']);

                    $product_data['page_title'] = 'Test drm product stock shortage email template';
                    $product_data['product_list'] = $product_list;
                    $product_data['user'] = DB::table('cms_users')->where('id', $user_id)->first();
                    $product_data['setting'] = DB::table('drm_invoice_setting')
                                                ->where('cms_user_id', $user_id)
                                                ->orderBy('id', 'desc')
                                                ->first();
                    $logo = $product_data['setting']->logo ?? '';

                    $tags = [
                        'user' => $product_data['user'],
                        'user_name' => $product_data['user']->name,
                        'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $product_data['setting']->store_name . '" >',
                        'product_name' => "",
                        'product_table' => view('admin.drm_products.email_product_table', compact('product_list'))->render(),
                    ];

                    $slug = 'drm_product_stock_shortage_email_template';
                    $mail_data = DRMParseMailTemplate($tags, $slug, 'de');
                    // Mail::to($product_data['user']->email)->send(new DRMSEndMail($mail_data));
                    app('drm.mailer')->getMailer()->to("<EMAIL>")->send(new DRMSEndMail($mail_data));
                });

        } catch (Exception $e) {

        }
    }
}

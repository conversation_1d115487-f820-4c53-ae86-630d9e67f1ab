<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class StripeOrderFetchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return[
          'timestamp' => Carbon::createFromTimestamp($this->timestamp)->diffForHumans(),
          'fetch' => $this->total_synced,
          'sort' => $this->timestamp,
        ];
    }
}

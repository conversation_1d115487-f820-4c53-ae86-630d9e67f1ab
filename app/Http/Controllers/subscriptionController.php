<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use CRUDBooster;
use DB;
use App\User;
use App\NewOrder;
use App\subscriptions;
use Carbon\Carbon;
use PDF;
use ServiceKey;


class subscriptionController extends \crocodicstudio\crudbooster\controllers\CBController
{

    public function purchase_invoice(){
        $data['page_title'] = "Customer Purchase Invoice";
        $data['user'] = User::find(CRUDBooster::myId());
        $data['orders'] = NewOrder::where('cms_client',CRUDBooster::myId())->where('insert_type',3)->orderBy('id','desc')->get();

        if(CRUDBooster::myId()){
            return view('subscription.purchase-invoice-view', compact('user'))->with($data);
        }
        else{
            CRUDBooster::redirect(CRUDBooster::adminPath('login'),"Login Please!","info");
        }
    }

    public function purchase_invoice_view($order_id){
        // dd($order_id);
        $return_invoice = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_invoice_pdf($order_id);
        
        return $return_invoice;

    }

    public function purchase_invoice_download($order_id){
    
        $return_invoice = app('App\Http\Controllers\AdminDrmAllOrdersController')->getDeliveryNotes($order_id);

    }


    public function viewPlan(){
        
        $data['import']=DB::table('purchase_import_plans')
        	 		->join('import_plans','import_plans.id','=','purchase_import_plans.import_plan_id')
        	 		->where('purchase_import_plans.cms_user_id',CRUDBooster::myId())
                    ->select('purchase_import_plans.*','import_plans.amount','import_plans.plan','import_plans.interval')
        	 		->first();
        $data['page_title'] = "Billing and Plans";
        
        $data['details']=DB::table('purchase_apps')
            ->join('app_stores','app_stores.id','=','purchase_apps.app_id')
            // ->where('purchase_apps.type','!=','Free Trail')
            ->where('purchase_apps.cms_user_id',CRUDBooster::myId())
            ->where(function($query) {
                    $query->where('purchase_apps.subscription_date_end','>', date('Y-m-d'))
                    ->orwhere('purchase_apps.subscription_life_time',1);
            })
            ->select('app_stores.menu_name','app_stores.download_file','app_stores.icon','purchase_apps.*')
            ->get();
        // dd($data['details']);
        if(CRUDBooster::myId()){
            $user = User::find(CRUDBooster::myId());
            $data['user'] = $user;
            return view('subscription.view-plan', compact('user'))->with($data);
        }
        else{
            CRUDBooster::redirect(CRUDBooster::adminPath('login'),"Login Please!","info");
        }
        
    }

    public function stripePurchaseView($order_id){

        $pdf_path = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_invoice_pdf($order_id);
        return $pdf_path;

    }

    public function stripePurchaseInvoiceDownload($order_id){
        $pdf_path = app('App\Http\Controllers\AdminDrmAllOrdersController')->getDeliveryNotes($order_id);
        return redirect($pdf_path);

    }

    public function update(Request $request){
        $planID = $request->plan;
        $subscription = DB::table('subscriptions')->where('user_id', CRUDBooster::myId())->where('stripe_id',$planID)->get();
        $updateRow = $subscription[0]->stripe_id;
        \Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));
        $planValue = \Stripe\Subscription::update(
            $updateRow,
            ['metadata' => ['DRM Plan' => '2']]
        );
        return CRUDBooster::redirectBack("Your Subscription Plan is Updated!", "success");
    }
    
    
    //super admin
    public function subscriptionOrder(){
        $subList = DB::table('subscriptions')
                    ->select('stripe_id')
                    ->orderBy('id','desc');
        $total_subscription = DB::table('subscriptions')->count();
        foreach($subList->get() as $stripeID){
            $stripeIDs = $stripeID->stripe_id;
            \Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));
            $subscription = \Stripe\Subscription::retrieve(
                $stripeIDs
            );
            $invoiceID = $subscription->latest_invoice;
            $invoice = \Stripe\Invoice::retrieve($invoiceID);

            $totalInvoice[] = $invoice;
            $totalTurnover[] = $invoice->amount_paid;
        }
        
        $turnOver = null;
        foreach($totalTurnover as $TotalTurnOver)
        {
            $turnOver = $turnOver + $TotalTurnOver / 100;
        }
        $data = [
            'invoice'  => $totalInvoice,
            'subList'   => $subList->paginate(10),
            'total_subscription'    => $total_subscription,
            'totalTurnover' => $turnOver,
        ];
        $data['page_title'] = "Subscription Orders Information";
        return view('subscription.order-list')->with($data);
    }

    public function getInvoice(Request $request){
        \Stripe\Stripe::setApiKey(ServiceKey::key('STRIPE_SECRET', 'sk_test_3fmKQpxSTTRNczuJM3KNav9i00sGRjMM91'));
        $invoice = \Stripe\Invoice::retrieve($request->id);
        $planDetails = \Stripe\Subscription::retrieve(
            $invoice->subscription
        );
        $customerID = $invoice->customer;
        $customerdetails = User::where('stripe_id',$customerID)->get();
        $planValue = $invoice->lines->data[0]->plan;
        $couponValue = $invoice->discount->coupon;
        $description = $invoice->lines->data[0];

        $data = [
            'totalInvoice'  => $invoice,
            'customerDetails'   => $customerdetails,
            'planValue' => $planValue,
            'couponValue'   => $couponValue,
            'description'   => $description,
            'planDetails'   => $planDetails,
        ];
        
        $data['page_title'] = "Customer Invoice";

        $pdf = PDF::loadView('subscription.invoice', $data);
        $fileName = "invoice-".$invoice->number.".pdf";
        return $pdf->download($fileName);

    }

    public function billingAddress(Request $request){
        $request->validate([
            'country_id' => 'required',
            'city' => 'required',
            'zip' => 'required',
            'address' => 'required',
            'company_name' => 'nullable',
            'email' => 'required',
            'phone' => 'nullable',
            'vat_id' => 'nullable',
        ]);
        $user = User::find(CRUDBooster::myId());

        if($user){
            $msg = 'update';
            $billing = null;
            if($user->billing_detail){
                $billing = $user->billing_detail()->update( $request->except(['_token']) );
            }else{
               $billing = $user->billing_detail()->create( $request->except(['_token']) );
               $msg = 'create';
            }

            if($billing){
                return response()->json([
                    'success' => true,
                    'message' => 'Billing address '.$msg.' success!',
                    'id'      => $user->billing_detail->id
                ]);
            }          
        } 

        return response()->json([
            'success' => false,
            'message' => 'Something went wrong!'
        ]);
    }

    public function userBillingAddress(){
        $user = User::find(CRUDBooster::myId());
        if($user){
            return response()->json([
                'success' => true,
                'html' => view('subscription.billing', compact('user'))->render()
            ]);            
        }else{
            return response()->json([
                'success' => false,
                'message' => 'Invalid User'
            ]);             
        }
    }

    public function insertCard(Request $request){
        $user = User::find(CRUDBooster::myId());
        if($user){
            if ($request->card_numbers) {
                $request->validate([
                    'card_names.*' => 'required|min:3',
                    'card_numbers.*' => 'required|min:16',
                    'card_cvcs.*' => 'required|min:3',
                    'card_months.*' => 'required|min:2',
                    'card_years.*' => 'required|min:4',
                ],
                [
                    'card_numbers.*.required' => 'Card number can not be empty!',
                    'card_numbers.*.min' => 'Invalid card number!',

                    'card_names.*' => 'Invalid name!',
                    'card_cvcs.*' => 'Invalid cvc number!',
                    'card_months.*' => 'Invalid month!',
                    'card_years.*' => 'Invalid year!',
                ]);
                foreach($request->card_numbers as $i => $number){
                    $user->cards()->create([
                        'number' => $number,
                        'name'  => $request->card_names[$i],
                        'cvc'   => $request->card_cvcs[$i],
                        'mm'    => $request->card_months[$i],
                        'yyyy'  => $request->card_years[$i]
                    ]);
                }
            }

            if ($request->card_numbers_old) {
                $request->validate([
                    'card_names_old.*' => 'required|min:3',
                    'card_numbers_old.*' => 'required|min:16',
                    'card_cvcs_old.*' => 'required|min:3',
                    'card_months_old.*' => 'required|min:2',
                    'card_years_old.*' => 'required|min:4',
                ],
                [
                    'card_numbers_old.*.required' => 'Card number can not be empty!',
                    'card_numbers_old.*.min' => 'Invalid card number!',

                    'card_names_old.*' => 'Invalid name!',
                    'card_cvcs_old.*' => 'Invalid cvc number!',
                    'card_months_old.*' => 'Invalid month!',
                    'card_years_old.*' => 'Invalid year!',
                ]);
                foreach($request->card_numbers_old as $k => $o_number){
                    $user->cards()->where('id', $k)->update([
                        'number' => $o_number ,
                        'name'  => $request->card_names_old[$k],
                        'cvc'   => $request->card_cvcs_old[$k],
                        'mm'    => $request->card_months_old[$k],
                        'yyyy'  => $request->card_years_old[$k]
                    ]);
                }
            }

            if($request->card_number_dels){
                $user->cards()->whereIn('id', $request->card_number_dels)->delete();
            }
            return response()->json([
                'success' => true,
                'message' => 'Cart save successfully!'
            ]);            
        }
        return response()->json([
            'success' => false,
            'message' => 'Something went wrong!'
        ]);
    }

    public function userCard(){
        $user = User::find(CRUDBooster::myId());
        if($user){
            return response()->json([
                'success' => true,
                'html' => view('subscription.card', compact('user'))->render()
            ]);            
        }else{
            return response()->json([
                'success' => false,
                'message' => 'Invalid User'
            ]);             
        }
    }

    public function billingAddressFill(User $user){
        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
        $term = ($privacy)? $privacy->page_content : '';

        if($user->billing_detail){
            $billing = $user->billing_detail;

            $user_data = $billing->company_name.'<br>'.$billing->address.'<br>'.$billing->zip.' '.$billing->city.'<br>'.$billing->country->name;


            if (strpos($term, '{customer}') !== false) {
                $term = str_replace('{customer}', $user_data, $term);
            }

            return response()->json([
                'success' => true,
                'html' => view('app_store.term', compact('user', 'billing', 'term'))->render()
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'You have no billing address. Please create! '
        ]);
    }

}

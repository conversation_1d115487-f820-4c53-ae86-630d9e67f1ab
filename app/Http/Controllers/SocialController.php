<?php
 namespace App\Http\Controllers;
 use Validator,Redirect,Response,File;
 use Socialite;
 use App\User;
 use Auth;
 use Illuminate\Support\Facades\Session;
 use CRUDBooster;
 use DB;
 use Request;
 use App\Mail\DRMSEndMail;
 // use App\Mail\NewCustomers;
 use Illuminate\Support\Facades\Mail;
 use Illuminate\Support\Str;
 use  App\Traits\ProjectShare;
 use ServiceKey;

 class SocialController extends Controller
 {

  use ProjectShare;


 public function redirect($provider)
 {

     return Socialite::driver($provider)->redirect();
 }
 public function callback($provider)
 {


 	try{
		 $getInfo=Socialite::driver($provider)->stateless()->user();

         $users=User::where('email',$getInfo->email)->first();

         if($users !=null){
            $this->userlogin($users);
            return redirect(CRUDBooster::adminPath());
         }

    	 if($getInfo->email==null){
    	 	return redirect()->action('AdminDrmProjectsController@sign_in_get')->with('error_msg', 'Your Email is Empty!');
    	 }
    	 $password = Str::random(6);
        $usersxds = $this->createUser($getInfo,$provider,$password);

        // $postdata=[

        //     'name'=>$getInfo->name,
        //     'email'=>$getInfo->email,
        //     'password_confirmation'=>$password,

        // ];
        // Mail::to($getInfo->email)->send(new NewCustomers($postdata));

        $tags = [
            'user_name' => $getInfo->name,
            'user_email' => $getInfo->email,
            'password_confirmation' => $password
        ];
        $slug = 'welcome_email';
        $lang = getUserSavedLang($getInfo->email);
        $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
        app('drm.mailer')->getMailer()->to($getInfo->email)->send(new DRMSEndMail($mail_data));


        $users=User::where('email',$getInfo->email)->first();
        $this->userlogin($users);
        return redirect(CRUDBooster::adminPath());

   }catch (Exception $e) {
  		return redirect()->action('AdminDrmProjectsController@sign_in_get')->with('error_msg', 'Something Went Wrong !');
   }

}
 function createUser($user,$provider,$password){

        $userValueInsert = DB::table('cms_users')->insertGetId([
            'name'     => $user->name,
            'email'    => $user->email,
            'remember_token'    => $user->token,
            'id_cms_privileges' => 3,
            'provider' => $provider,
            'password' => \Hash::make($password),
            'provider_id' => $user->id,
            'status' => 'Active',
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $insert_appoinment_data = DB::table('takeappointment')->insert(
            ['user_id' => $userValueInsert, 'payment_date_for' => 1, 'payment_date_remaining' => 1]
        );

        return $userValueInsert;
   }


    public function userlogin($user) {
                $users=DB::table('cms_users')->where('id',$user->id)->first();

                if($users->status != null){
                $priv = DB::table("cms_privileges")->where("id", $users->id_cms_privileges)->first();
                $roles = DB::table('cms_privileges_roles')->where('id_cms_privileges', $users->id_cms_privileges)->join('cms_moduls', 'cms_moduls.id', '=', 'id_cms_moduls')->select('cms_moduls.name', 'cms_moduls.path', 'is_visible', 'is_create', 'is_read', 'is_edit', 'is_delete')->get();
                $photo = ($users->photo) ? asset($users->photo) : asset('vendor/crudbooster/avatar.jpg');
                Session::put('admin_is_superadmin', $priv->is_superadmin);
                Session::put('admin_id', $users->id);
                Session::put('admin_is_superadmin', $priv->is_superadmin);
                Session::put('admin_is_developer', $priv->is_dev);
                Session::put('admin_name', $users->name);
                Session::put('admin_photo', $photo);
                Session::put('admin_privileges_roles', $roles);
                Session::put("admin_privileges", $users->id_cms_privileges);
                Session::put('admin_privileges_name', $priv->name);
                Session::put('admin_lock', 0);
                Session::put('theme_color', $priv->theme_color);
                Session::put("appname", CRUDBooster::getSetting('appname'));
                CRUDBooster::insertLog(trans("crudbooster.log_login", ['email' => $users->email, 'ip' => Request::server('REMOTE_ADDR')]));
                $cb_hook_session = new \App\Http\Controllers\CBHook;
                $cb_hook_session->afterLogin();
                return true;
                }else{
                    return redirect()->route('getLogin')->with('message', 'Your account has been suspended. Please contact our <NAME_EMAIL> to get a new activation.');
                }

    }

    public function getLoginFrame(){
      return view('iframe.login_full');
    }

    public function getLoginFrameLite(){
      return view('iframe.login');
    }

    public function getRegFrameLite(){
      return view('iframe.registration');
    }
    public function getRegFrame(){
      return view('iframe.registration_full');
    }
 }

<?php

namespace App\Http\Controllers\Marketplace;

use App\Models\Payload;
use App\Models\Marketplace\Product;
use App\Http\Controllers\Controller;
use App\Jobs\Marketplace\CategoryAccessProductDeleteJob;

class CategoryAccessProductDeleteController extends Controller
{

    public function categoryAccessRemoveProductDelete()
    {
        $jobInfo = Payload::where('uuid', request()->uuid)->first();

        if (!$jobInfo) {
            //case where job info is not found
            return;
        }

        $productIds = Product::join('marketplace_categories', 'marketplace_products.category_id', '=', 'marketplace_categories.id')
        ->where('marketplace_categories.parent_id', $jobInfo->payload['parent_category'])
            ->pluck('marketplace_products.id')
            ->toArray();

        if (!empty($productIds)) {
            $chunkSize = 400;
            $productChunks = array_chunk($productIds, $chunkSize);

            foreach ($productChunks as $ids) {
                 CategoryAccessProductDeleteJob::dispatch($ids, $jobInfo->payload['customer_id'])->onQueue('keepa');
            }
        }

        $jobInfo->update(['status' => 1]);
    }
}

<?php

namespace App\Http\Controllers\Marketplace;

use App\BillingDetail;
use App\DrmProduct;
use App\DeliveryCompany;
use App\Models\DrmCategory;
use App\Traits\ProjectShare;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use App\Jobs\Marketplace\SupplierMonthlyReportSendJob;
use App\Models\Marketplace\Category;
use App\Models\Marketplace\CreditNote;
use App\DropmatixProductBrand;
use App\NewOrder;
use App\Services\Marketplace\ProductService;
use App\User;
use PDF;
use App\Models\Marketplace\AutoTransferSubscription;
use App\Models\Marketplace\MpCoreDrmTransferProduct;
use App\Enums\Apps;
use App\Models\Marketplace\UserAccess;
use App\Jobs\Marketplace\BulkMarketplaceProductDelete;
use App\Jobs\Marketplace\CategoryAccessProductDeleteJob;
use App\Services\ProductApi\TransferProduct;
use App\Jobs\DestroyProduct;
use App\Jobs\ChannelManager\AutoTransfer;
use App\Jobs\Marketplace\CustomerBulkParentCategoryRemove;
use App\TransferMarketplaceToDrm;

class MarketPlaceController extends Controller
{
    use ProjectShare;
    private $productService;

    public function __construct(ProductService $productService)
    {
        $this->productService = $productService;
    }



    public function transferProductToDrm ($productId, $user_id, $attributes = [])
    {
        // //DB::beginTransaction();
        // try {

            $pId = $productId;
            $product = Product::where('id', $pId)->first();

            if ( Product::isInDrmProductList($product, $user_id) ) {
                return true;
            }

            $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
            $lang = app('App\Services\UserService')->getProductLanguage($country_id);

            if ( $product->category_id ) {
                $categoryName = Category::where('id', $product->category_id)->first()->name;

                if ( DrmCategory::where('category_name_'.$lang, $categoryName)->exists() ) {
                    $drmCategory = DrmCategory::where('category_name_'.$lang, $categoryName)->first();
                } else {
                    $drmCategory = DrmCategory::create([
                        'category_name_'.$lang => $categoryName,
                        'user_id' => $user_id,
                        'country_id' => $country_id,
                    ]);
                }
            }

            $deliveryCompanyRow = [
                'user_id'       => \App\Enums\Apps::DROPMATIX_ID,
                'name'          => 'Dropmatix Systema SL',
                'contact_name'  => 'Dropmatix Systema SL',
                'zip'           => '07200',
                'state'         => 'FELANITX',
                'country_id'    => 8,
                'email'         => '<EMAIL>',
                'address'       => 'C/ HORTS, 33',
                'note'          => 'Marketplace Supplier',
                'is_marketplace_supplier' => 1,
            ];

            $deliveryCompany = DeliveryCompany::where('user_id', $user_id)
                ->where('name', $deliveryCompanyRow['name'])
                ->where('email', '<EMAIL>')
                ->first();

            if ( !$deliveryCompany ) {
                $deliveryCompanyRow['user_id'] = $user_id;
                $deliveryCompany = DeliveryCompany::create($deliveryCompanyRow);
            }

            $stock = ($product->shipping_method == 1) ? $product->stock : $product->internel_stock;

            if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::FULFILLment) {
                $shippingCost = 5.20;
            } else if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::DROPSHIPPING) {
                $shippingCost = $product->shipping_cost;
            }

            $drmProductInfo             = [
                'user_id'               => $user_id,
                'country_id'            => $country_id,
                'language_id'           => null,
                'name'                  => $product->brand .' '. $product->name,
                'item_number'           => $product->item_number,
                'ean'                   => $product->ean,
                'image'                 => $product->image,
                'ek_price'              => $product->vk_price,
                'vk_price'              => 0.00,
                'vat'                   => NULL,
                'stock'                 => (empty($stock)) ? 0 : $stock,
                'category'              => $drmCategory->id ?? null,
                'ean_field'             => 1,
                'item_weight'           => $product->item_weight ?? null,
                'item_size'             => $product->item_size ?? null,
                'item_color'            => $product->item_color ?? null,
                'note'                  => $product->note ?? null,
                'production_year'       => $product->production_year ?? null,
                'brand'                 => $product->brand ?? null,
                'materials'             => $product->materials ?? null,
                'tags'                  => $product->tags ?? null,
                'update_enabled'        => $product->update_enabled ?? null,
                'status'                => $product->status ?? null,
                'gender'                => $product->gender ?? null,
                'uvp'                   => $product->uvp ?? 0.00,
                'title'                 => [
                                             $lang => $product->name ?? null,
                                           ],
                'update_status'         => makeUpdateStatusJson(),
                'description' => [
                    $lang => $product->description ?? null,
                ],
                'delivery_company_id'     => $deliveryCompany->id,
                'marketplace_supplier_id' => $product->supplier_id ?? '',
                'marketplace_product_id'  => $product->id,
                'marketplace_shipping_method' => $product->shipping_method,
                'shipping_cost'               => $shippingCost ?? 0,
                'delivery_days'               => $product->delivery_days,
            ];

            $drmProductInfo = array_merge($drmProductInfo, $attributes);

            $drmProduct = DrmProduct::create($drmProductInfo);

            DB::table('drm_product_categories')->insert([
                'product_id' => $drmProduct->id,
                'category_id' => $drmCategory->id,
            ]);

            // //DB::commit();
            return true;

        // } catch (\Exception $e) {
        //     //DB::rollBack();
        //     return false;
        // }
    }

    public function createSupplierMonthlyReport(){
        $users = User::with('billing_detail')->where('id_cms_privileges',4)->get();
        $data['setting'] = BillingDetail::with('country')->where('user_id', 2455)->first();
        $data['page_title'] = 'Supplier Credit Note';

        $month = strtotime('last month');

        foreach($users as $user){

            $orders = NewOrder::where('cms_user_id',$user->id)
                    ->whereYear('order_date', date('Y', strtotime('last month')))
                    ->whereMonth('order_date', date('m', $month))
                    ->where('new_orders.invoice_number', '>', 0)
                    ->where('insert_type', '!=', \App\Enums\InsertType::MP_INTERNEL_MANUAL)->where('test_order','!=',1)
                    ->get();

            $credit_note = CreditNote::updateOrCreate(['supplier_id'=>$user->id,'month'=>date('Y-m', $month)],['amount'=>$orders->sum('total')]);

            if(count($orders) > 0) {

                $user_emails = DB::table('options')->select('report_email')->where('user_id',$user->id)->whereNotNull('report_email')->first();

                if($user_emails){
                    $emails = json_decode($user_emails->report_email);

                    $data['user'] = $user;
                    $data['orders'] = $orders;
                    $data['invoice_month'] = $credit_note->month;
                    $data['referenznummer'] = $credit_note->id;

                    if(!isLocal()){
                        $pdf = $this->supplierMonthlyReportCloudUpload($data);

                        foreach($emails as $email){
                            dispatch(new SupplierMonthlyReportSendJob($email, $pdf));
                        }
                    }


                }
            }
        }
    }

    private function supplierMonthlyReportCloudUpload($data){

        $randomStr = substr(str_shuffle(str_repeat('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', mt_rand(1, 5))), 1, 40);
        $fileName = 'marketplace-supplier-monthly-report' . '/' . $randomStr . ".pdf";
        $pdf = PDF::loadView('admin.drm_invoice.supplier_invoice', $data);

        Storage::disk('spaces')->put($fileName, $pdf->output(), 'public');
        return Storage::disk('spaces')->url($fileName);
    }

    public function transferAllFilteredProductsToDrm ($productId = null, $attributes = [], $categoryId = null, $user_id = null){
        //DB::beginTransaction();
        try {
            $autoTranferIds = [];
            $totalTransfered = 0;
            $autoTransferSubscription = null;
            $productIds = (request()->product_id) ? request()->product_id : $productId;
            $user_id = $user_id;
            $id_exists = DrmProduct::whereIntegerInRaw('marketplace_product_id', $productIds)
                    ->where('user_id', $user_id)->pluck('marketplace_product_id')->toArray();

            $tranferable_ids = array_diff($productIds, $id_exists);

            if($categoryId){
                $autoTransferSubscription = AutoTransferSubscription::where(['user_id'=>$user_id,'category_id'=>$categoryId])->first();
                $autoTranferIds = $autoTransferSubscription->transfered_product_ids ?? [];
                $totalTransfered = $autoTransferSubscription->transfered_products_count ?? 0;
            }
            if(empty($tranferable_ids)){
                return [
                    'success' => false,
                    'message' => "Products already transfered to drm. Please select new products.",
                ];
            }

            $importProduct = $this->getImportPlan($user_id);
            $transferLimit = $importProduct ? $importProduct['product_amount'] : 0;
            $transferPlan = $importProduct['plan'] ? $importProduct['plan'] : null;
            $transferPlanLimit = $importProduct['limit'] ? $importProduct['limit'] : null;

            $i = 0;
            $trial_checked = 0;
            $mpCoreData = [];
            $drm_product_categories = [];
            if((($transferPlan && $transferPlan == 'Trial') && ($transferPlanLimit && $transferPlanLimit == 'Unlimited')) || ($transferPlanLimit && $transferPlanLimit == 'Unlimited')){
                foreach($tranferable_ids as $productId){
                    $product = Product::with('additionalInfo','productBrand')->where('id', $productId)->first();
                    $ean_exist = DrmProduct::where('ean', $product->ean)->where('user_id', $user_id)->first();
                    if(!$ean_exist){
                        // Additional columns check
                        $manufacturer = null;
                        $manufacturer_link = null;
                        $manufacturer_id = null;
                        $custom_tariff_number = null;
                        $shipping_company_id = null;
                        $region = null;
                        $country_of_origin = null;
                        $min_stock = null;
                        $min_order = null;
                        $gross_weight = null;
                        $net_weight = null;
                        $product_length = null;
                        $product_width = null;
                        $product_height = null;
                        $volume = null;
                        $packaging_length = null;
                        $packaging_width = null;
                        $packaging_height = null;
                        $item_unit = null;
                        $packing_unit = null;
                        $volume_gross = null;

                        if( $product->additionalInfo ){
                            $manufacturer = $product->additionalInfo->manufacturer;
                            $manufacturer_link = $product->additionalInfo->manufacturer_link;
                            $manufacturer_id = $product->additionalInfo->manufacturer_id;
                            $custom_tariff_number = $product->additionalInfo->custom_tariff_number;
                            $shipping_company_id = $product->additionalInfo->shipping_company_id;
                            $region = $product->additionalInfo->region;
                            $country_of_origin = $product->additionalInfo->country_of_origin;
                            $min_stock = $product->additionalInfo->min_stock;
                            $min_order = $product->additionalInfo->min_order;
                            $gross_weight = $product->additionalInfo->gross_weight;
                            $net_weight = $product->additionalInfo->net_weight;
                            $product_length = $product->additionalInfo->product_length;
                            $product_width = $product->additionalInfo->product_width;
                            $product_height = $product->additionalInfo->product_height;
                            $volume = $product->additionalInfo->volume;
                            $packaging_length = $product->additionalInfo->packaging_length;
                            $packaging_width = $product->additionalInfo->packaging_width;
                            $packaging_height = $product->additionalInfo->packaging_height;
                            $item_unit = $product->additionalInfo->item_unit;
                            $packing_unit = $product->additionalInfo->packing_unit;
                            $volume_gross = $product->additionalInfo->volume_gross;
                        }

                        $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
                        $lang = app('App\Services\UserService')->getProductLanguage($country_id);

                        $discount = 0.0;
                        if ( $product->category_id ) {
                            $category = Category::where('id',$product->category_id)->first();
                            $drmCategory = DrmCategory::where('category_name_'.$lang, $category->name)->where('user_id',$user_id)->first();
                            if( date($category->start_date) <= date("Y-m-d") && date($category->end_date) >= date("Y-m-d") ){
                                $discount = $category->is_offer_active ? $category->discount_percentage : 0.0;
                            }
                            if ( !$drmCategory ) {
                                $drmCategory = DrmCategory::create([
                                    'category_name_'.$lang => $category->name,
                                    'user_id' => $user_id,
                                    'country_id' => $country_id,
                                ]);
                            }
                        }

                        $deliveryCompanyRow = [
                            'user_id'       => \App\Enums\Apps::DROPMATIX_ID,
                            'name'          => 'Dropmatix Systema SL',
                            'contact_name'  => 'Dropmatix Systema SL',
                            'zip'           => '07200',
                            'state'         => 'FELANITX',
                            'country_id'    => 8,
                            'email'         => '<EMAIL>',
                            'address'       => 'C/ HORTS, 33',
                            'note'          => 'Marketplace Supplier',
                            'is_marketplace_supplier' => 1,
                        ];

                        $deliveryCompany = DeliveryCompany::where('user_id', $user_id)
                            ->where('name', $deliveryCompanyRow['name'])
                            ->where('email', '<EMAIL>')
                            ->first();

                        if ( !$deliveryCompany ) {
                            $deliveryCompanyRow['user_id'] = $user_id;
                            $deliveryCompany = DeliveryCompany::create($deliveryCompanyRow);
                        }

                        $stock = ($product->shipping_method == 1) ? $product->stock : $product->internel_stock;

                        if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::FULFILLment) {
                            $shippingCost = 5.20;
                        } else if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::DROPSHIPPING) {
                            $shippingCost = $product->shipping_cost;
                        }

                        if ( $product->brand ) {
                            $mpBrandName = (is_numeric($product->brand) ? $product->productBrand->brand_name : $product->brand) ?? '';
                            $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper( $mpBrandName) . '%')->where('user_id',$user_id)->first();
                            if ( $drmBrand ) {
                                $drmBrand = $drmBrand;
                            } else {
                                if(!empty($mpBrandName)){
                                    $drmBrand = DropmatixProductBrand::create([
                                        'brand_name' =>  $mpBrandName,
                                        'user_id' => $user_id,
                                        'brand_logo'=>($product->productBrand->brand_logo ?? [])
                                    ]);
                                }
                            }
                        }

                        if( $product->offer_start_date <= now() && $product->offer_end_date >= now() && $product->is_offer_active == 1){
                            $discount += $product->discount_percentage ? ($product->discount_percentage - $product->discount_percentage/4) : 0.0;
                        }

                        $drmProductInfo             = [
                            'user_id'               => $user_id,
                            'country_id'            => $country_id,
                            'language_id'           => null,
                            'name'                  => $product->brand .' '. $product->name,
                            'item_number'           => $product->item_number,
                            'ean'                   => $product->ean,
                            'additional_eans'       => json_encode($product->additional_eans),
                            'image'                 => $product->image,
                            'ek_price'              => userWiseVkPriceCalculate($product->vk_price ?? 0.0, $user_id, false, $discount, $product->api_id ?? null),
                            'vk_price'              => 0.00,
                            'vat'                   => $product->vat ?? null,
                            'tax_type'              => $product->tax_type ?? 1,
                            'stock'                 => (empty($stock)) ? 0 : $stock,
                            'category'              => $drmCategory->id ?? null,
                            'ean_field'             => 1,
                            'item_weight'           => $product->item_weight ?? null,
                            'item_size'             => $product->item_size ?? null,
                            'item_color'            => $product->item_color ?? null,
                            'note'                  => $product->note ?? null,
                            'production_year'       => $product->production_year ?? null,
                            'brand'                 => $drmBrand ? $drmBrand->id : null,
                            'materials'             => $product->materials ?? null,
                            'tags'                  => $product->tags ?? null,
                            'update_enabled'        => $product->update_enabled ?? null,
                            'status'                => $product->status ?? null,
                            'gender'                => $product->gender ?? null,
                            'uvp'                   => $product->uvp ?? 0.00,
                            'title'                 => [
                                                        $lang => $product->name ?? null,
                                                    ],
                            'update_status'         => makeUpdateStatusJson(),

                            // 'short_description' => json_encode($product->description),
                                'description' => [
                                    $lang => $product->description ?? null,
                                ],
                            'delivery_company_id'     => $deliveryCompany->id,
                            'marketplace_supplier_id' => $product->supplier_id ?? '',
                            'marketplace_product_id'  => $product->id,
                            'marketplace_shipping_method' => $product->shipping_method,
                            'shipping_cost'               => $shippingCost ?? 0,
                            'delivery_days'               => $product->delivery_days,
                            'industry_template_data'      => ($product->industry_template_data),
                            'product_type'                => \App\Enums\ProductType::MP_MANUAL_TRANSFER,
                            'mp_category_offer'           => $discount,
                            // Additional columns
                            'manufacturer'                => $manufacturer,
                            'manufacturer_link'           => $manufacturer_link,
                            'manufacturer_id'             => $manufacturer_id,
                            'custom_tariff_number'        => $custom_tariff_number,
                            'shipping_company_id'         => $shipping_company_id ?? 8,
                            'region'                      => $region,
                            'country_of_origin'           => $country_of_origin,
                            'min_stock'                   => $min_stock,
                            'min_order'                   => $min_order,
                            'gross_weight'                => $gross_weight,
                            'net_weight'                  => $net_weight,
                            'product_length'              => $product_length,
                            'product_width'               => $product_width,
                            'product_height'              => $product_height,
                            // 'volume'                      => $volume,
                            'packaging_length'            => $packaging_length,
                            'packaging_width'             => $packaging_width,
                            'packaging_height'            => $packaging_height,
                            'item_unit'                   => $item_unit,
                            'packaging_unit'                => $packing_unit,
                            // 'volume_gross'                => $volume_gross,
                        ];

                        $drmProductInfo = array_merge($drmProductInfo,$attributes);

                        $drmProduct = DrmProduct::create($drmProductInfo);
                        if ($trial_checked == 0) {
                            app(\App\Http\Controllers\AdminDrmImportsController::class)->setImportTrial($user_id);
                            $trial_checked = 1;
                        }

                        $drm_product_categories[] = [
                            'product_id' => $drmProduct->id,
                            'category_id' => $drmCategory->id,
                        ];

                        $mpCoreData[] = [
                            'drm_product_id' => $drmProduct->id,
                            'marketplace_product_id' => $drmProduct->marketplace_product_id,
                            'user_id' => $drmProduct->user_id,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                        $i++;
                        $autoTranferIds[$productId] = $drmProduct->id;
                    }
                }
            } else if($transferLimit && $transferLimit > 0) {
                foreach($tranferable_ids as $productId){
                    if($transferLimit > $i) {
                        $product = Product::with('additionalInfo','productBrand')->where('id', $productId)->first();
                        $ean_exist = DrmProduct::where('ean', $product->ean)->where('user_id', $user_id)->first();
                        if(!$ean_exist){
                            // Additional columns check
                            $manufacturer = null;
                            $manufacturer_link = null;
                            $manufacturer_id = null;
                            $custom_tariff_number = null;
                            $shipping_company_id = null;
                            $region = null;
                            $country_of_origin = null;
                            $min_stock = null;
                            $min_order = null;
                            $gross_weight = null;
                            $net_weight = null;
                            $product_length = null;
                            $product_width = null;
                            $product_height = null;
                            $volume = null;
                            $packaging_length = null;
                            $packaging_width = null;
                            $packaging_height = null;
                            $item_unit = null;
                            $packing_unit = null;
                            $volume_gross = null;

                            if( $product->additionalInfo ){
                                $manufacturer = $product->additionalInfo->manufacturer;
                                $manufacturer_link = $product->additionalInfo->manufacturer_link;
                                $manufacturer_id = $product->additionalInfo->manufacturer_id;
                                $custom_tariff_number = $product->additionalInfo->custom_tariff_number;
                                $shipping_company_id = $product->additionalInfo->shipping_company_id;
                                $region = $product->additionalInfo->region;
                                $country_of_origin = $product->additionalInfo->country_of_origin;
                                $min_stock = $product->additionalInfo->min_stock;
                                $min_order = $product->additionalInfo->min_order;
                                $gross_weight = $product->additionalInfo->gross_weight;
                                $net_weight = $product->additionalInfo->net_weight;
                                $product_length = $product->additionalInfo->product_length;
                                $product_width = $product->additionalInfo->product_width;
                                $product_height = $product->additionalInfo->product_height;
                                $volume = $product->additionalInfo->volume;
                                $packaging_length = $product->additionalInfo->packaging_length;
                                $packaging_width = $product->additionalInfo->packaging_width;
                                $packaging_height = $product->additionalInfo->packaging_height;
                                $item_unit = $product->additionalInfo->item_unit;
                                $packing_unit = $product->additionalInfo->packing_unit;
                                $volume_gross = $product->additionalInfo->volume_gross;
                            }

                            $i++;
                            $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
                            $lang = app('App\Services\UserService')->getProductLanguage($country_id);

                            $discount = 0.0;
                            if ( $product->category_id ) {
                                $category = Category::where('id',$product->category_id)->first();
                                $drmCategory = DrmCategory::where('category_name_'.$lang, $category->name)->where('user_id',$user_id)->first();
                                if( $category->start_date <= now() && $category->end_date >= now()){
                                    $discount = $category->is_offer_active ? $category->discount_percentage : 0.0;
                                }
                                if ( !$drmCategory ) {
                                    $drmCategory = DrmCategory::create([
                                        // 'category_name' => $categoryName,
                                        'category_name_'.$lang => $category->name,
                                        // 'category_name_en' => $categoryName,
                                        // 'category_name_es' => $categoryName,
                                        'user_id' => $user_id,
                                        'country_id' => $country_id,
                                    ]);
                                }
                            }
                            $deliveryCompanyRow = [
                                'user_id'       => \App\Enums\Apps::DROPMATIX_ID,
                                'name'          => 'Dropmatix Systema SL',
                                'contact_name'  => 'Dropmatix Systema SL',
                                'zip'           => '07200',
                                'state'         => 'FELANITX',
                                'country_id'    => 8,
                                'email'         => '<EMAIL>',
                                'address'       => 'C/ HORTS, 33',
                                'note'          => 'Marketplace Supplier',
                                'is_marketplace_supplier' => 1,
                            ];
                            $deliveryCompany = DeliveryCompany::where('user_id', $user_id)
                                ->where('name', $deliveryCompanyRow['name'])
                                ->where('email', '<EMAIL>')
                                ->first();
                            if ( !$deliveryCompany ) {
                                $deliveryCompanyRow['user_id'] = $user_id;
                                $deliveryCompany = DeliveryCompany::create($deliveryCompanyRow);
                            }

                            $stock = ($product->shipping_method == 1) ? $product->stock : $product->internel_stock;

                            if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::FULFILLment) {
                                $shippingCost = 5.20;
                            } else if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::DROPSHIPPING) {
                                $shippingCost = $product->shipping_cost;
                            }

                            if ( $product->brand ) {
                                $mpBrandName = (is_numeric($product->brand) ? $product->productBrand->brand_name : $product->brand) ?? '';
                                $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper( $mpBrandName) . '%')->where('user_id', $user_id)->first();
                                if ( $drmBrand ) {
                                    $drmBrand = $drmBrand;
                                } else {
                                    if(!empty($mpBrandName)){
                                        $drmBrand = DropmatixProductBrand::create([
                                            'brand_name' =>  $mpBrandName,
                                            'user_id' => $user_id,
                                            'brand_logo'=>($product->productBrand->brand_logo ?? [])
                                        ]);
                                    }
                                }
                            }

                            if( $product->offer_start_date <= now() && $product->offer_end_date >= now() && $product->is_offer_active == 1){
                                $discount += $product->discount_percentage ? ($product->discount_percentage - $product->discount_percentage/4) : 0.0;
                            }

                            $drmProductInfo             = [
                                'user_id'               => $user_id,
                                'country_id'            => $country_id,
                                'language_id'           => null,
                                'name'                  => $product->brand .' '. $product->name,
                                'item_number'           => $product->item_number,
                                'ean'                   => $product->ean,
                                'additional_eans'       => json_encode($product->additional_eans),
                                'image'                 => $product->image,
                                'ek_price'              => userWiseVkPriceCalculate($product->vk_price ?? 0.0, $user_id, false, $discount, $product->api_id ?? null),
                                'vk_price'              => 0.00,
                                'vat'                   => $product->vat ?? null,
                                'tax_type'              => $product->tax_type ?? 1,
                                'stock'                 => (empty($stock)) ? 0 : $stock,
                                'category'              => $drmCategory->id ?? null,
                                'ean_field'             => 1,
                                'item_weight'           => $product->item_weight ?? null,
                                'item_size'             => $product->item_size ?? null,
                                'item_color'            => $product->item_color ?? null,
                                'note'                  => $product->note ?? null,
                                'production_year'       => $product->production_year ?? null,
                                'brand'                 => $drmBrand ? $drmBrand->id : null,
                                'materials'             => $product->materials ?? null,
                                'tags'                  => $product->tags ?? null,
                                'update_enabled'        => $product->update_enabled ?? null,
                                'status'                => $product->status ?? null,
                                'gender'                => $product->gender ?? null,
                                'uvp'                   => $product->uvp ?? 0.00,
                                'title'                 => [
                                                            $lang => $product->name ?? null,
                                                        ],
                                'update_status'         => makeUpdateStatusJson(),
                                // 'short_description' => json_encode($product->description),
                                    'description' => [
                                        $lang => $product->description ?? null,
                                    ],
                                'delivery_company_id'     => $deliveryCompany->id,
                                'marketplace_supplier_id' => $product->supplier_id ?? '',
                                'marketplace_product_id'  => $product->id,
                                'marketplace_shipping_method' => $product->shipping_method,
                                'shipping_cost'               => $shippingCost ?? 0,
                                'delivery_days'               => $product->delivery_days,
                                'industry_template_data'      => ($product->industry_template_data),
                                'product_type'                => \App\Enums\ProductType::MP_MANUAL_TRANSFER,
                                'mp_category_offer'           => $discount,
                                // Additional columns
                                'manufacturer'                => $manufacturer,
                                'manufacturer_link'           => $manufacturer_link,
                                'manufacturer_id'             => $manufacturer_id,
                                'custom_tariff_number'        => $custom_tariff_number,
                                'shipping_company_id'         => $shipping_company_id ?? 8,
                                'region'                      => $region,
                                'country_of_origin'           => $country_of_origin,
                                'min_stock'                   => $min_stock,
                                'min_order'                   => $min_order,
                                'gross_weight'                => $gross_weight,
                                'net_weight'                  => $net_weight,
                                'product_length'              => $product_length,
                                'product_width'               => $product_width,
                                'product_height'              => $product_height,
                                // 'volume'                      => $volume,
                                'packaging_length'            => $packaging_length,
                                'packaging_width'             => $packaging_width,
                                'packaging_height'            => $packaging_height,
                                'item_unit'                   => $item_unit,
                                'packaging_unit'              => $packing_unit,
                                // 'volume_gross'                => $volume_gross,
                            ];

                            $drmProductInfo = array_merge($drmProductInfo,$attributes);

                            $drmProduct = DrmProduct::create($drmProductInfo);
                            if ($trial_checked == 0) {
                                app(\App\Http\Controllers\AdminDrmImportsController::class)->setImportTrial($user_id);
                                $trial_checked = 1;
                            }
                            $drm_product_categories[] = [
                                'product_id' => $drmProduct->id,
                                'category_id' => $drmCategory->id,
                            ];
                            $mpCoreData[] = [
                                'drm_product_id' => $drmProduct->id,
                                'marketplace_product_id' => $drmProduct->marketplace_product_id,
                                'user_id' => $drmProduct->user_id,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ];

                            $autoTranferIds[$productId] = $drmProduct->id;
                        }

                    } else {
                        break;
                    }
                }
            } else {
                return response()->json([
                    'status'      => true,
                    'message'     => 'Your DRM products transfer limit exceed! Please Upgrade your tarif plan.',
                ]);
            }

            DB::table('drm_product_categories')->insert($drm_product_categories);
            MpCoreDrmTransferProduct::insert($mpCoreData);

            if($categoryId){
                $autoTransferSubscription->update(['transfered_product_ids'=>$autoTranferIds, 'transfered_products_count'=>$totalTransfered + $i]);
            }

            //DB::commit();
            $one_one_sync = DB::table('cms_users')->select('one_one_sync')->where('id', $user_id)->get();
            if($one_one_sync[0]->one_one_sync == 1){
                app(TransferProduct::class)->transferSynchronizedProductsToAnalysis($user_id);
            }

            if(professionalOrHigher($user_id)){
                AutoTransfer::dispatch(array_values($autoTranferIds),$user_id,$lang ?? "de");
            }

            return response()->json([
                'status'      => true,
                'message'     => 'Successfully transferred '.$i.' Products',
            ]);

        } catch (\Exception $e) {
            //DB::rollBack();
            return response()->json([
                'status'        => 'error :: '.$e->getMessage(),
                'error_msg'     => 'Something went wrong',
            ]);
        }
    }

    public function getImportPlan($user_id)
    {
        try {
            $url = "https://drm.software/api/app_store/import_plan/";
            $data = [
                'user_id'   => $user_id
            ];

            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("accept: application/json","content-type: application/json"));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
            curl_setopt($ch, CURLOPT_POSTFIELDS,json_encode($data));
            $data = curl_exec($ch);
            curl_close($ch);
            $result = json_decode($data,true);
            return $result['result'];
        } catch (\Throwable $th) {
            return false;
        }
    }

    public function ProductBrandUpdate($productIds,$brandId,$selected_brand_name,$selected_brand_logo){

        try{

            $products = Product::with('core_products:marketplace_product_id,id,user_id')
                    ->select('id','brand')
                    ->whereIn('id', $productIds)
                    ->where('brand','!=',$brandId)
                    ->get();

            Product::whereIn('id', $productIds)->where('brand','!=',$brandId)->update(['brand'=> $brandId]);

            foreach($products as $product){

                $core_products = $product->core_products;

                if(count($core_products) > 0){
                    foreach($core_products as $core_product){
                        $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper( $selected_brand_name) . '%')
                                    ->where('user_id',$core_product->user_id)->first();
                        if ( $drmBrand ) {
                            $drmBrand = $drmBrand;
                        } else {
                            if(!empty($selected_brand_name)){
                                $drmBrand = DropmatixProductBrand::create([
                                    'brand_name' =>  $selected_brand_name,
                                    'user_id' =>$core_product->user_id,
                                    'brand_logo'=>([$selected_brand_logo] ?? [])
                                ]);
                            }
                        }

                        $updateableColumns['brand'] = $drmBrand->id;
                        app(\App\Services\DRMProductService::class)->update($core_product->id, $updateableColumns);
                    }
                }
            }

        }catch(\Exception $e){
            info($e->getMessage());
        }

    }

    public function assignMarketplaceProductCalculation($product_ids, $calculation){

        $channelProducts = Product::with('core_products')->whereIn('id', $product_ids)->get();

        foreach ($channelProducts as $channelProduct) {
            $old_vk_price = $channelProduct->vk_price;
            $new_vk_price = $this->calculatePrice($channelProduct->ek_price, $calculation, $channelProduct->uvp, $channelProduct->shipping_cost);

            if (round($new_vk_price, 2) != round($old_vk_price, 2) && $new_vk_price > 0) {
                $channelProduct->calculation_id = $calculation->id ?? null;
                $channelProduct->vk_price = $new_vk_price;
                $channelProduct->old_vk_price = $old_vk_price;
                $channelProduct->vk_price_updated_at  = \Carbon\Carbon::now();
                $channelProduct->update();
            }

            if(!blank($channelProduct->core_products)) {
                $this->calculatedPriceSyncToDrm($channelProduct->core_products, $channelProduct->vk_price);
            }
        }

    }

    public function calculatePrice($price, $calculation, $uvp = 0, $shipping_cost = 0)
    {
        if ($calculation->dynamic_shipping_cost && $shipping_cost > 0) {
            $calculation_shipping_cost = $shipping_cost;
        } else {
            $calculation_shipping_cost = $calculation->shipping_cost;
        }

        try {
            if ($calculation->uvp) {
                $price = $uvp;
            } else {
                $price = $price + $price
                    * ($calculation->profit_percent / 100)
                    + $calculation_shipping_cost
                    + $calculation->additional_charge;

                if ($calculation->round_scale != null) {
                    $prices = explode('.', $price);
                    if ($prices[1] != 0) {
                        $price = $prices[0] + $calculation->round_scale;
                    }
                }
            }
            return (float)str_replace(',', '', number_format($price, 2));
        } catch (\Throwable $th) {
            return $price;
        }
    }


    public function calculatedPriceSyncToDrm($products, $price)
    {
        $mp_vk_price = $price;
        foreach($products as $product){
            $mp_price_markup_discount = !blank($product->mp_price_markup) ? ($product->mp_price_markup * $mp_vk_price) / 100 : 0;
            $cat_offer_discount = !blank($product->mp_category_offer) ? ($product->mp_category_offer * $mp_vk_price) / 100 : 0;

            $mp_new_vk_price = round($mp_vk_price + $mp_price_markup_discount - $cat_offer_discount, 2);

            if (round($product->ek_price, 2)  != $mp_new_vk_price) {
                $updateableColumns['ek_price'] = $mp_new_vk_price;
                app(\App\Services\DRMProductService::class)->update($product->id, $updateableColumns);
            }
        }
    }

    public function transferUnlimitedProductsToDrm($tranferable_ids = [], $user_id, $category_id = null){
        $products = Product::with('additionalInfo','productBrand')->whereIn('id', $tranferable_ids)->where('status', 1)->get();
        // $ean_exist = DrmProduct::select('ean')->where('user_id', $user_id)->whereIn('ean', $products->pluck('ean')->toArray())->pluck('ean')->toArray();
        // $products = $products->filter(function ($value, $key) use ($ean_exist) {
        //     return !in_array($value['ean'], $ean_exist);
        // });

        if(!empty($products)){

            $mp_core_data = [];
            $auto_transfer_ids = [];
            $drm_product_categories = [];
            $autoTransferSubscription = null;
            $total_transfered = 0;
            $trial_checked = 0;

            if($category_id){
                $autoTransferSubscription = AutoTransferSubscription::where(['user_id'=>$user_id,'category_id'=>$category_id])->first();
                $auto_transfer_ids = $autoTransferSubscription->transfered_product_ids ?? [];
                $total_transfered = $autoTransferSubscription->transfered_products_count ?? 0;
            }

            $deliveryCompanyRow = [
                'user_id'       => \App\Enums\Apps::DROPMATIX_ID,
                'name'          => 'Dropmatix Systema SL',
                'contact_name'  => 'Dropmatix Systema SL',
                'zip'           => '07200',
                'state'         => 'FELANITX',
                'country_id'    => 8,
                'email'         => '<EMAIL>',
                'address'       => 'C/ HORTS, 33',
                'note'          => 'Marketplace Supplier',
                'is_marketplace_supplier' => 1,
            ];
            $deliveryCompany = DeliveryCompany::where('user_id', $user_id)
                ->where('name', $deliveryCompanyRow['name'])
                ->where('email', '<EMAIL>')
                ->first();
            if ( !$deliveryCompany ) {
                $deliveryCompanyRow['user_id'] = $user_id;
                $deliveryCompany = DeliveryCompany::create($deliveryCompanyRow);
            }

            $mp_agreement = DB::table('mp_payment_agreements')
                ->where('user_id', '=', $user_id)
                ->where('type', '=', 1)
                ->select('price_markup')
                ->first();
            $price_markup = $mp_agreement ? $mp_agreement->price_markup : 0.0;

            foreach($products as $key => $product){
                // Additional columns check
                $manufacturer = null;
                $manufacturer_link = null;
                $manufacturer_id = null;
                $custom_tariff_number = null;
                $shipping_company_id = null;
                $region = null;
                $country_of_origin = null;
                $min_stock = null;
                $min_order = null;
                $gross_weight = null;
                $net_weight = null;
                $product_length = null;
                $product_width = null;
                $product_height = null;
                // $volume = null;
                $packaging_length = null;
                $packaging_width = null;
                $packaging_height = null;
                $item_unit = null;
                $packing_unit = null;
                // $volume_gross = null;

                if( $product->additionalInfo ){
                    $manufacturer = $product->additionalInfo->manufacturer;
                    $manufacturer_link = $product->additionalInfo->manufacturer_link;
                    $manufacturer_id = $product->additionalInfo->manufacturer_id;
                    $custom_tariff_number = $product->additionalInfo->custom_tariff_number;
                    $shipping_company_id = $product->additionalInfo->shipping_company_id;
                    $region = $product->additionalInfo->region;
                    $country_of_origin = $product->additionalInfo->country_of_origin;
                    $min_stock = $product->additionalInfo->min_stock;
                    $min_order = $product->additionalInfo->min_order;
                    $gross_weight = $product->additionalInfo->gross_weight;
                    $net_weight = $product->additionalInfo->net_weight;
                    $product_length = $product->additionalInfo->product_length;
                    $product_width = $product->additionalInfo->product_width;
                    $product_height = $product->additionalInfo->product_height;
                    // $volume = $product->additionalInfo->volume;
                    $packaging_length = $product->additionalInfo->packaging_length;
                    $packaging_width = $product->additionalInfo->packaging_width;
                    $packaging_height = $product->additionalInfo->packaging_height;
                    $item_unit = $product->additionalInfo->item_unit;
                    $packing_unit = $product->additionalInfo->packing_unit;
                    // $volume_gross = $product->additionalInfo->volume_gross;
                }

                // $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
                $country_id = $product->country_id ?? 1;
                $lang = app('App\Services\UserService')->getProductLanguage($country_id);

                $discount = 0.0;
                if ($product->category_id && !in_array($product->supplier_id, [2817])) {
                    $category = Category::find($product->category_id);
                    if ($category) {
                        $drmCategory = DrmCategory::firstOrCreate(
                            ['category_name_'.$lang => $category->name, 'user_id' => $user_id],
                            ['country_id' => $country_id]
                        );

                        if ($category->start_date <= now() && $category->end_date >= now() && $category->is_offer_active) {
                            $discount = $category->discount_percentage;
                        }
                    }
                }

                if(in_array($product->supplier_id, [2817])) {
                    $category = DB::table('channel_user_categories')->where('id', $product->category_id)->first();
                    $drmCategory = DrmCategory::where('category_name_'.$lang, $category->category_name)->where('user_id',$user_id)->first();
                    if ( !$drmCategory ) {
                        $drmCategory = DrmCategory::create([
                            'category_name_'.$lang => $category->category_name,
                            'user_id' => $user_id,
                            'country_id' => $country_id,
                        ]);
                    }
                }

                $stock = ($product->shipping_method == 1) ? $product->stock : $product->internel_stock;

                if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::FULFILLment) {
                    $shippingCost = $product->shipping_cost > 0 ? $product->shipping_cost : 9.68;
                } else {
                    $shippingCost = $product->shipping_cost;
                }

                if ($product->real_shipping_cost == 0 && isEnterpriceOrTrialUser($user_id)) {
                    $shippingCost = 0.0;
                }

                if ( $product->brand ) {
                    $mpBrandName = (is_numeric($product->brand) ? $product->productBrand->brand_name : $product->brand) ?? '';
                    $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper( $mpBrandName) . '%')->where('user_id', $user_id)->first();
                    if ( !$drmBrand ) {
                        if(!empty($mpBrandName)){
                            $drmBrand = DropmatixProductBrand::create([
                                'brand_name' =>  $mpBrandName,
                                'user_id' => $user_id,
                                'brand_logo'=>($product->productBrand->brand_logo ?? [])
                            ]);
                        }
                    }
                }

                if( $product->offer_start_date <= now() && $product->offer_end_date >= now() && $product->is_offer_active == 1){
                    $discount += $product->discount_percentage ? ($product->discount_percentage - $product->discount_percentage/4) : 0.0;
                }

                $drm_product_info           = [
                    'user_id'               => $user_id,
                    'country_id'            => $country_id,
                    'language_id'           => null,
                    'name'                  => $product->brand .' '. $product->name,
                    'item_number'           => $product->item_number,
                    'ean'                   => $product->ean,
                    'additional_eans'       => json_encode($product->additional_eans),
                    'image'                 => $product->image,
                    'ek_price'              => userWiseVkPriceCalculate($product->vk_price ?? 0.0, $user_id, false, $discount, $product->api_id ?? null),
                    'vk_price'              => 0.00,
                    'vat'                   => $product->vat ?? null,
                    'tax_type'              => $product->tax_type ?? 1,
                    'stock'                 => (empty($stock)) ? 0 : $stock,
                    'category'              => $drmCategory->id ?? null,
                    'ean_field'             => 1,
                    'item_weight'           => $product->item_weight ?? null,
                    'item_size'             => $product->item_size ?? null,
                    'item_color'            => $product->item_color ?? null,
                    'note'                  => $product->note ?? null,
                    'production_year'       => $product->production_year ?? null,
                    'brand'                 => $drmBrand ? $drmBrand->id : null,
                    'materials'             => $product->materials ?? null,
                    'tags'                  => $product->tags ?? null,
                    'update_enabled'        => $product->update_enabled ?? null,
                    'status'                => $product->status ?? null,
                    'gender'                => $product->gender ?? null,
                    'uvp'                   => $product->uvp ? $product->uvp + (($product->uvp * $price_markup)/100) : 0.00,
                    'title'                 => [
                                                $lang => $product->name ?? null,
                                            ],
                    'update_status'         => makeUpdateStatusJson(),
                    // 'short_description' => json_encode($product->description),
                        'description' => [
                            $lang => $product->description ?? null,
                        ],
                    'delivery_company_id'     => $deliveryCompany->id,
                    'marketplace_supplier_id' => $product->supplier_id ?? '',
                    'marketplace_product_id'  => $product->id,
                    'marketplace_shipping_method' => $product->shipping_method,
                    'shipping_cost'               => $shippingCost ?? 0,
                    'delivery_days'               => $product->delivery_days,
                    'industry_template_data'      => ($product->industry_template_data),
                    'product_type'                => $category_id ? \App\Enums\ProductType::MP_AUTO_TRANSFER : \App\Enums\ProductType::MP_MANUAL_TRANSFER,
                    'mp_category_offer'           => $discount,
                    // Additional columns
                    'manufacturer'                => $manufacturer,
                    'manufacturer_link'           => $manufacturer_link,
                    'manufacturer_id'             => $manufacturer_id,
                    'custom_tariff_number'        => $custom_tariff_number,
                    'shipping_company_id'         => $shipping_company_id ?? 8,
                    'region'                      => $region,
                    'country_of_origin'           => $country_of_origin,
                    'min_stock'                   => $min_stock,
                    'min_order'                   => $min_order,
                    'gross_weight'                => $gross_weight,
                    'net_weight'                  => $net_weight,
                    'product_length'              => $product_length,
                    'product_width'               => $product_width,
                    'product_height'              => $product_height,
                    // 'volume'                      => $volume,
                    'packaging_length'            => $packaging_length,
                    'packaging_width'             => $packaging_width,
                    'packaging_height'            => $packaging_height,
                    'item_unit'                   => $item_unit,
                    'packaging_unit'              => $packing_unit,
                    // 'volume_gross'                => $volume_gross,
                    'mp_price_markup'             => $price_markup,
                    'marketplace_delivery_company_id' => $product->delivery_company_id,
                    'im_handel'                   => $product->im_handel,

                ];

                $drmProduct = DrmProduct::create($drm_product_info);

                if ($trial_checked == 0) {
                    app(\App\Http\Controllers\AdminDrmImportsController::class)->setImportTrial($user_id);
                    $trial_checked = 1;
                }

                $drm_product_categories[] = [
                    'product_id' => $drmProduct->id,
                    'category_id' => $drmProduct->category,
                    'country_id' => $drmProduct->country_id,
                ];
                $mp_core_data[] = [
                    'drm_product_id' => $drmProduct->id,
                    'mp_product_ean' => $drmProduct->ean,
                    'marketplace_product_id' => $drmProduct->marketplace_product_id,
                    'user_id' => $drmProduct->user_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                $auto_transfer_ids[$drmProduct->marketplace_product_id] = $drmProduct->id;
            }

            if(!empty($drm_product_categories) && !empty($mp_core_data)) {
                DB::table('drm_product_categories')->insert($drm_product_categories);
                MpCoreDrmTransferProduct::insert($mp_core_data);

                if($category_id){
                    $autoTransferSubscription->update(['transfered_product_ids'=>$auto_transfer_ids, 'transfered_products_count'=>$total_transfered + $key + 1]);
                }

                $one_one_sync = DB::table('cms_users')->select('one_one_sync')->where('id', $user_id)->get();
                if($one_one_sync[0]->one_one_sync == 1){
                    app(TransferProduct::class)->transferSynchronizedProductsToAnalysis($user_id);
                }

                if(professionalOrHigher($user_id)){
                    AutoTransfer::dispatch(array_values($auto_transfer_ids),$user_id, "de");
                }

                return response()->json([
                    'status'      => true,
                    'message'     => 'Successfully transferred '.($key+1).' Products',
                ]);
            }
        } else {
            return response()->json([
                'status'    => 422,
                'message'   => 'These ean products already exist in your account. You can not transfer these products.',
            ]);
        }

    }

    public function transferTarifLimitedProductsToDrm($tranferable_ids = [], $user_id, $category_id = null){
        $products = Product::with('additionalInfo','productBrand')->whereIn('id', $tranferable_ids)->where('status', 1)->get();
        // $ean_exist = DrmProduct::select('ean')->where('user_id', $user_id)->whereIn('ean', $products->pluck('ean')->toArray())->pluck('ean')->toArray();
        // $products = $products->filter(function ($value, $key) use ($ean_exist) {
        //     return !in_array($value['ean'], $ean_exist);
        // });

        if(!empty($products)){

            $mp_core_data = [];
            $auto_transfer_ids = [];
            $drm_product_categories = [];
            $autoTransferSubscription = null;
            $total_transfered = 0;
            $trial_checked = 0;

            if($category_id){
                $autoTransferSubscription = AutoTransferSubscription::where(['user_id'=>$user_id,'category_id'=>$category_id])->first();
                $auto_transfer_ids = $autoTransferSubscription->transfered_product_ids ?? [];
                $total_transfered = $autoTransferSubscription->transfered_products_count ?? 0;
            }

            $deliveryCompanyRow = [
                'user_id'       => \App\Enums\Apps::DROPMATIX_ID,
                'name'          => 'Dropmatix Systema SL',
                'contact_name'  => 'Dropmatix Systema SL',
                'zip'           => '07200',
                'state'         => 'FELANITX',
                'country_id'    => 8,
                'email'         => '<EMAIL>',
                'address'       => 'C/ HORTS, 33',
                'note'          => 'Marketplace Supplier',
                'is_marketplace_supplier' => 1,
            ];
            $deliveryCompany = DeliveryCompany::where('user_id', $user_id)
                ->where('name', $deliveryCompanyRow['name'])
                ->where('email', '<EMAIL>')
                ->first();
            if ( !$deliveryCompany ) {
                $deliveryCompanyRow['user_id'] = $user_id;
                $deliveryCompany = DeliveryCompany::create($deliveryCompanyRow);
            }

            $importProduct = $this->getImportPlan($user_id);
            $transferLimit = $importProduct ? $importProduct['product_amount'] : 0;

            $mp_agreement = DB::table('mp_payment_agreements')
                    ->where('user_id', '=', $user_id)
                    ->where('type', '=', 1)
                    ->select('price_markup')
                    ->first();
            $price_markup = $mp_agreement ? $mp_agreement->price_markup : 0.0;

            foreach($products as $key => $product){
                if($transferLimit > $key) {
                    // Additional columns check
                    $manufacturer = null;
                    $manufacturer_link = null;
                    $manufacturer_id = null;
                    $custom_tariff_number = null;
                    $shipping_company_id = null;
                    $region = null;
                    $country_of_origin = null;
                    $min_stock = null;
                    $min_order = null;
                    $gross_weight = null;
                    $net_weight = null;
                    $product_length = null;
                    $product_width = null;
                    $product_height = null;
                    // $volume = null;
                    $packaging_length = null;
                    $packaging_width = null;
                    $packaging_height = null;
                    $item_unit = null;
                    $packing_unit = null;
                    // $volume_gross = null;

                    if( $product->additionalInfo ){
                        $manufacturer = $product->additionalInfo->manufacturer;
                        $manufacturer_link = $product->additionalInfo->manufacturer_link;
                        $manufacturer_id = $product->additionalInfo->manufacturer_id;
                        $custom_tariff_number = $product->additionalInfo->custom_tariff_number;
                        $shipping_company_id = $product->additionalInfo->shipping_company_id;
                        $region = $product->additionalInfo->region;
                        $country_of_origin = $product->additionalInfo->country_of_origin;
                        $min_stock = $product->additionalInfo->min_stock;
                        $min_order = $product->additionalInfo->min_order;
                        $gross_weight = $product->additionalInfo->gross_weight;
                        $net_weight = $product->additionalInfo->net_weight;
                        $product_length = $product->additionalInfo->product_length;
                        $product_width = $product->additionalInfo->product_width;
                        $product_height = $product->additionalInfo->product_height;
                        // $volume = $product->additionalInfo->volume;
                        $packaging_length = $product->additionalInfo->packaging_length;
                        $packaging_width = $product->additionalInfo->packaging_width;
                        $packaging_height = $product->additionalInfo->packaging_height;
                        $item_unit = $product->additionalInfo->item_unit;
                        $packing_unit = $product->additionalInfo->packing_unit;
                        // $volume_gross = $product->additionalInfo->volume_gross;
                    }

                    // $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
                    $country_id = $product->country_id ?? 1;
                    $lang = app('App\Services\UserService')->getProductLanguage($country_id);

                    $discount = 0.0;
                    if ($product->category_id && !in_array($product->supplier_id, [2817])) {
                        $category = Category::find($product->category_id);
                        if ($category) {
                            $drmCategory = DrmCategory::firstOrCreate(
                                ['category_name_'.$lang => $category->name, 'user_id' => $user_id],
                                ['country_id' => $country_id]
                            );
    
                            if ($category->start_date <= now() && $category->end_date >= now() && $category->is_offer_active) {
                                $discount = $category->discount_percentage;
                            }
                        }
                    }

                    if(in_array($product->supplier_id, [2817])) {
                        $category = DB::table('channel_user_categories')->where('id', $product->category_id)->first();
                        $drmCategory = DrmCategory::where('category_name_'.$lang, $category->category_name)->where('user_id',$user_id)->first();
                        if ( !$drmCategory ) {
                            $drmCategory = DrmCategory::create([
                                'category_name_'.$lang => $category->category_name,
                                'user_id' => $user_id,
                                'country_id' => $country_id,
                            ]);
                        }
                    }

                    $stock = ($product->shipping_method == 1) ? $product->stock : $product->internel_stock;

                    if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::FULFILLment) {
                        $shippingCost = $product->shipping_cost > 0 ? $product->shipping_cost : 9.68;
                    } else {
                        $shippingCost = $product->shipping_cost;
                    }

                    if ($product->real_shipping_cost == 0 && isEnterpriceOrTrialUser($user_id)) {
                        $shippingCost = 0.0;
                    }

                    if ( $product->brand ) {
                        $mpBrandName = (is_numeric($product->brand) ? $product->productBrand->brand_name : $product->brand) ?? '';
                        $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper( $mpBrandName) . '%')->where('user_id', $user_id)->first();
                        if ( !$drmBrand ) {
                            if(!empty($mpBrandName)){
                                $drmBrand = DropmatixProductBrand::create([
                                    'brand_name' =>  $mpBrandName,
                                    'user_id' => $user_id,
                                    'brand_logo'=>($product->productBrand->brand_logo ?? [])
                                ]);
                            }
                        }
                    }

                    if( $product->offer_start_date <= now() && $product->offer_end_date >= now() && $product->is_offer_active == 1){
                        $discount += $product->discount_percentage ? ($product->discount_percentage - $product->discount_percentage/4) : 0.0;
                    }

                    $drm_product_info           = [
                        'user_id'               => $user_id,
                        'country_id'            => $country_id,
                        'language_id'           => null,
                        'name'                  => $product->brand .' '. $product->name,
                        'item_number'           => $product->item_number,
                        'ean'                   => $product->ean,
                        'additional_eans'       => json_encode($product->additional_eans),
                        'image'                 => $product->image,
                        'ek_price'              => userWiseVkPriceCalculate($product->vk_price ?? 0.0, $user_id, false, $discount, $product->api_id ?? null),
                        'vk_price'              => 0.00,
                        'vat'                   => $product->vat ?? null,
                        'tax_type'              => $product->tax_type ?? 1,
                        'stock'                 => (empty($stock)) ? 0 : $stock,
                        'category'              => $drmCategory->id ?? null,
                        'ean_field'             => 1,
                        'item_weight'           => $product->item_weight ?? null,
                        'item_size'             => $product->item_size ?? null,
                        'item_color'            => $product->item_color ?? null,
                        'note'                  => $product->note ?? null,
                        'production_year'       => $product->production_year ?? null,
                        'brand'                 => $drmBrand ? $drmBrand->id : null,
                        'materials'             => $product->materials ?? null,
                        'tags'                  => $product->tags ?? null,
                        'update_enabled'        => $product->update_enabled ?? null,
                        'status'                => $product->status ?? null,
                        'gender'                => $product->gender ?? null,
                        'uvp'                   => $product->uvp ? $product->uvp + (($product->uvp * $price_markup)/100) : 0.00,
                        'title'                 => [
                                                    $lang => $product->name ?? null,
                                                ],
                        'update_status'         => makeUpdateStatusJson(),
                        // 'short_description' => json_encode($product->description),
                            'description' => [
                                $lang => $product->description ?? null,
                            ],
                        'delivery_company_id'     => $deliveryCompany->id,
                        'marketplace_supplier_id' => $product->supplier_id ?? '',
                        'marketplace_product_id'  => $product->id,
                        'marketplace_shipping_method' => $product->shipping_method,
                        'shipping_cost'               => $shippingCost ?? 0,
                        'delivery_days'               => $product->delivery_days,
                        'industry_template_data'      => ($product->industry_template_data),
                        'product_type'                => $category_id ? \App\Enums\ProductType::MP_AUTO_TRANSFER : \App\Enums\ProductType::MP_MANUAL_TRANSFER,
                        'mp_category_offer'           => $discount,
                        // Additional columns
                        'manufacturer'                => $manufacturer,
                        'manufacturer_link'           => $manufacturer_link,
                        'manufacturer_id'             => $manufacturer_id,
                        'custom_tariff_number'        => $custom_tariff_number,
                        'shipping_company_id'         => $shipping_company_id ?? 8,
                        'region'                      => $region,
                        'country_of_origin'           => $country_of_origin,
                        'min_stock'                   => $min_stock,
                        'min_order'                   => $min_order,
                        'gross_weight'                => $gross_weight,
                        'net_weight'                  => $net_weight,
                        'product_length'              => $product_length,
                        'product_width'               => $product_width,
                        'product_height'              => $product_height,
                        // 'volume'                      => $volume,
                        'packaging_length'            => $packaging_length,
                        'packaging_width'             => $packaging_width,
                        'packaging_height'            => $packaging_height,
                        'item_unit'                   => $item_unit,
                        'packaging_unit'              => $packing_unit,
                        // 'volume_gross'                => $volume_gross,
                        'mp_price_markup'             => $price_markup,
                        'marketplace_delivery_company_id' => $product->delivery_company_id,
                        'im_handel'                   => $product->im_handel,
                    ];

                    $drmProduct = DrmProduct::create($drm_product_info);

                    if ($trial_checked == 0) {
                        app(\App\Http\Controllers\AdminDrmImportsController::class)->setImportTrial($user_id);
                        $trial_checked = 1;
                    }

                    $drm_product_categories[] = [
                        'product_id' => $drmProduct->id,
                        'category_id' => $drmProduct->category,
                        'country_id' => $drmProduct->country_id,
                    ];
                    $mp_core_data[] = [
                        'drm_product_id' => $drmProduct->id,
                        'mp_product_ean' => $drmProduct->ean,
                        'marketplace_product_id' => $drmProduct->marketplace_product_id,
                        'user_id' => $drmProduct->user_id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    $auto_transfer_ids[$drmProduct->marketplace_product_id] = $drmProduct->id;

                }
            }

            if(!empty($drm_product_categories) && !empty($mp_core_data)) {
                DB::table('drm_product_categories')->insert($drm_product_categories);
                MpCoreDrmTransferProduct::insert($mp_core_data);

                if($category_id){
                    $autoTransferSubscription->update(['transfered_product_ids'=>$auto_transfer_ids, 'transfered_products_count'=>$total_transfered + $key + 1]);
                }

                $one_one_sync = DB::table('cms_users')->select('one_one_sync')->where('id', $user_id)->get();
                if($one_one_sync[0]->one_one_sync == 1){
                    app(TransferProduct::class)->transferSynchronizedProductsToAnalysis($user_id);
                }

                if(professionalOrHigher($user_id)){
                    AutoTransfer::dispatch(array_values($auto_transfer_ids),$user_id,"de");
                }

                return response()->json([
                    'status'      => true,
                    'message'     => 'Successfully transferred '.($key+1).' Products',
                ]);
            }
        } else {
            return response()->json([
                'status'    => 422,
                'message'   => 'These ean products already exist in your account. You can not transfer these products.',
            ]);
        }

    }

    public function deleteSelectedParentCategory($parentCategoryId, $user_id)
    {
        try {
            $all_categories = Category::where('parent_id', $parentCategoryId)->pluck('id')->toArray();
            $user_access_status = UserAccess::where('user_id', $user_id)->first() ?? [];
            // update UserAccess process
            $update_accessable_parent_categories = array_diff($user_access_status->accessable_parent_categories ?? [], [$parentCategoryId] ?? []);
            $update_accessable_categories = array_diff($user_access_status->accessable_categories ?? [], $all_categories ?? []);

            $check_accessable_categories = $user_access_status['check_accessable_categories'] ?? [];
            // update set_by_admin
            foreach ($check_accessable_categories as &$item) {
                if ($item["accessable_categories"] === $parentCategoryId) {
                    $item["set_by_admin"] = 1;
                }
            }

            AutoTransferSubscription::where('user_id', $user_id)->whereIn('category_id', $all_categories ?? [])->update(
                ['end_date' => now(),'status' => 0,]
            );
            $languageId = app('App\Services\UserService')->getProductCountry($user_id);
            $lang = app('App\Services\UserService')->getProductLanguage($languageId);
            $mp_product_ids = Product::whereIn('category_id', $all_categories ?? [])->pluck('id')->toArray() ?? [];

            foreach (array_chunk($mp_product_ids, 500) as $chunkIds) {
                $drm_product_id = DrmProduct::whereIn('marketplace_product_id', $chunkIds)
                                ->where('user_id', $user_id)->pluck('id')->toArray();

                if (!empty($drm_product_id)) {
                    DestroyProduct::dispatch($drm_product_id, $user_id, $lang, $languageId);
                }
            }

            UserAccess::where('user_id', $user_id)->update([
                'accessable_parent_categories' => array_values($update_accessable_parent_categories),
                'accessable_categories'        => array_values($update_accessable_categories),
                'check_accessable_categories'  => array_values($check_accessable_categories)
            ]);

        } catch (\Exception $e) {
            info(["Delete Selected ParentCategory failed", $e->getMessage()]);
        }
    }

    public function marketplaceProductRemoveFromDrm($mp_product_id = [], $user_id){
        $drm_product_id = MpCoreDrmTransferProduct::whereIn('marketplace_product_id', $mp_product_id)
                          ->where('user_id', $user_id)->pluck('drm_product_id')->toArray();
        $languageId = app('App\Services\UserService')->getProductCountry($user_id);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);
        if (!empty($drm_product_id)) {
            foreach (array_chunk($drm_product_id, 200) as $chunkIds) {
                DestroyProduct::dispatch($chunkIds, $user_id, $lang, $languageId);
            }
        }
    }

    public function customerBulkParentCategoryRemove($user_id)
    {
        $user_access_status = UserAccess::where('user_id', $user_id)->select('id', 'accessable_categories')->first();
        if ($user_access_status) {
            foreach (array_chunk($user_access_status->accessable_categories, 50) as $categoryIds) {
                dispatch(new CustomerBulkParentCategoryRemove($categoryIds, $user_id));
            }
            $user_access_status->update([
                'accessable_parent_categories' => [],
                'accessable_categories'        => [],
                'check_accessable_categories'  => []
            ]);
            cache()->forget('UserCategoryAccess_' . $user_id);
        }
    }

    public function duplicateTransferableProductIdDelete($user_id, $porduct_ids)
    {
        $mp_id_exists = DrmProduct::where('user_id', $user_id)->whereIn('marketplace_product_id', $porduct_ids)->pluck('marketplace_product_id')->toArray();
        TransferMarketplaceToDrm::where('user_id', $user_id)->whereIn('mp_product_id', $mp_id_exists)->where('status', 0)->delete();
    }

    public function mpCacheClear($key)
    {
        Cache::forget($key);
    }

}

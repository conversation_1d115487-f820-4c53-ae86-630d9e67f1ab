<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUD<PERSON>ooster;
	use Route;
	use Illuminate\Support\Facades\Validator;
	use App\Services\DateTime\DateTime;
	class AdminManualImportTarrifController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "id";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = true;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "manual_import_tarrif";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"User Name","name"=>"user_id","join"=>"cms_users,name"];
			$this->col[] = ["label"=>"Import Amount","name"=>"import_amount"];
			$this->col[] = ['label'=>'Unlimited','name'=>'unlimited'];
			$this->col[] = ['label'=>'End Date','name'=>'end_date'];
			# END COLUMNS DO NOT REMOVE THIS LINE

			$product_table = "drm_products";
			// $purchased_users = DB::table('purchase_import_plans')
			// 									 ->where("product_amount_import",">","SELECT COUNT(*) FROM ".$product_table." WHERE user_id = cms_user_id")
			// 									 ->pluck('cms_user_id')->toArray();
			// $assigned_users = DB::table($this->table)->pluck('user_id')->toArray();
			// $users = array_merge($purchased_users,$assigned_users);
			// dd($purchased_users);
			// ,"datatable_where"=>"cms_users.id NOT IN (".implode(',', $purchased_users).")"
			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			$this->form[] = ['label'=>'User Name','name'=>'user_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-8','datatable'=>"cms_users,name,email"];
			$this->form[] = ['label'=>'Import Amount','name'=>'import_amount','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-8','datatable'=>'import_plans,product_amount'];
			$this->form[] = ['label'=>'Unlimited Days','name'=>'unlimited','type'=>'radio','validation'=>'required','width'=>'col-sm-9','dataenum'=>'0|No;1|Yes','value'=>'0'];
		  $this->form[] = ['label'=>'End Date','name'=>'end_date','type'=>'date','width'=>'col-sm-8','validation'=>'required'];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ['label'=>'User Name','name'=>'user_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-8','datatable'=>'cms_users,name'];
			//$this->form[] = ['label'=>'Import Amount','name'=>'import_amount','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-8','datatable'=>'import_plans,product_amount','value' => ''];
			//$this->form[] = ['label'=>'End Date','name'=>'end_date','type'=>'date','validation'=>'','width'=>'col-sm-8'];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = '
						$(document).ready(function(){
							checkUnlimited();
						});
						$("input[type=radio][name=unlimited]").click(function(){
							checkUnlimited();
						});

						function checkUnlimited(){
							var value = $("input[type=radio][name=unlimited]:checked").val();
							if(value == 0){
								$("#form-group-end_date").css("display","block");
								$("#end_date").attr("required",true);
							}
							else {
								$("#form-group-end_date").css("display","none");
								$("#end_date").attr("required",false);
							}
						}
					';


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	if($column_index == 3){
					if($column_value == 0){
						$column_value = "No";
					}
					else {
						$column_value = "Yes";
					}
				}
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        $amount = self::getPackageAmount($postdata['import_amount']);
					$postdata['import_amount'] = $amount;

					$user_id = $postdata['user_id'];
					DB::table('app_trials')->where('user_id',$user_id)->where('app_id',0)->update(['trial_days'=>0]);
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
				$amount = self::getPackageAmount($postdata['import_amount']);
				$postdata['import_amount'] = $amount;
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }

			public function getEdit($id){
					$this->cbLoader();
					$row = DB::table($this->table)->where($this->primary_key, $id)->first();
					$amount = (float)$row->import_amount;
					$pack_id = DB::table('import_plans')->where('product_amount',$amount)->first()->id;
					$row->import_amount = $pack_id;
					if (! CRUDBooster::isRead() && $this->global_privilege == false || $this->button_edit == false) {
							CRUDBooster::insertLog(trans("crudbooster.log_try_edit", [
									'name' => $row->{$this->title_field},
									'module' => CRUDBooster::getCurrentModule()->name,
							]));
							CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
					}

					$page_menu = Route::getCurrentRoute()->getActionName();
					$page_title = trans("crudbooster.edit_data_page_title", ['module' => CRUDBooster::getCurrentModule()->name, 'name' => $row->{$this->title_field}]);
					$command = 'edit';
					Session::put('current_row_id', $id);

					return view('crudbooster::default.form', compact('id', 'row', 'page_menu', 'page_title', 'command'));
			}

	    //By the way, you can still create your own method in here... :)

			public function getUserAmount($user_id){
				$tarrif = DB::table('manual_import_tarrif')->where('user_id',$user_id)->first();
				if($tarrif){
					return $tarrif;
				}
				else {
					return 0;
				}
			}

			public function getPackageAmount($id){
				$result = DB::table('import_plans')->where('id',$id)->first();
				$amount = $result->product_amount;
				return $amount;
			}

			public function checkManualTarrif($user_id){
				$data = [];

				$tariff = self::getUserAmount($user_id);
				if($tariff){
					$end_date = $tariff->end_date;
					$amount = $tariff->import_amount;
					$unlimited = $tariff->unlimited;

					$data['end_date'] = date('Y-m-d', strtotime($end_date));

					if(!$unlimited){
						$end_date = $tariff->end_date;
						$date_diff = (int) DateTime::getRemainDays(date('Y-m-d'),$end_date);
						
						if($date_diff === 0) {
							$date_diff = 1;
						}

						if($date_diff>0){
							$data['amount'] = $amount;
							$data['days'] = $date_diff;
							return $data;
						}
						else {
							return false;
						}
					}
					else {
						$data['amount'] = $amount;
						$data['days'] = "Unlimited";
						return $data;
					}
				}
				else {
					return false;
				}
			}

			// DT Manual Tariff Assign Check
			public function checkDtManualTarrif($user_id){

				$data = [];

				$tariff = DB::table('dt_tariff_purchases')
				->where('user_id', $user_id)
				->where(function($q){
					$q->whereNull('subscription_id');
					$q->orWhere('subscription_id', '=', '');
				})
				->first();

				if($tariff){
					$end_date = $tariff->end_date;
					$amount = \App\Enums\DroptiendaPlan::DtPlanProductAmount($tariff->plan_id);

					if(in_array($user_id, \App\Enums\DroptiendaPlan::SPECIAL_USER_IDS)){
                        $amount = \App\Enums\DroptiendaPlan::SPECIAL_USER_PRODUCT_AMOUNT[$user_id]; //Special priveledge user, custom amount of product
                    }

					$plan_id = $tariff->plan_id;

					$data['end_date'] = date('Y-m-d', strtotime($end_date));
					
					$date_diff = (int) DateTime::getRemainDays(date('Y-m-d'),$end_date);
					
					if($date_diff === 0) {
						$date_diff = 1;
					}
					
					if($date_diff < 1000){

						if($date_diff > 0){
							$data['amount'] = $amount;
							$data['days'] = $date_diff;
							$data['plan_id'] = $plan_id;
							return $data;
						}else {
							return false;
						}

					}else {
						$data['amount'] = $amount;
						$data['days'] = "Unlimited";
						$data['plan_id'] = $plan_id;
						return $data;
					}
				}
				else {
					return false;
				}
			}

			public function postAssignUser(){
				$user_id = $_REQUEST['user_id'];
				$validator = \Validator::make(Request::all(), [
            'user_id' => 'required',
            'product_amount' => 'required',
						'unlimited' => 'required',
        ]);
        if ($validator->fails()) {
            $message = $validator->errors()->all();
            return redirect()->back()->with(['message' => implode(', ', $message), 'message_type' => 'danger']);
        }

				DB::table('manual_import_tarrif')->updateOrInsert(
						['user_id' => $user_id],
						['import_amount' => $_REQUEST['product_amount'], 'unlimited' => $_REQUEST['unlimited'],'end_date' => $_REQUEST['end_date']]
				);
				$this->cancelTrial($user_id);
				return redirect()->back();
			}

			public function cancelTrial($user_id){
				DB::table('app_trials')->updateOrInsert(
						['user_id' => $user_id,'app_id'=>0],
						['trial_days' => 0]
				);
			}
	}

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Stripe\ManualSubscription;
use DB;
use App\Jobs\DRMManualSubscriptionNotifyJob;
use Illuminate\Support\Facades\Mail;
use App\Mail\DRMSEndMail;
use App\User;
use App\NewOrder;
use App\Notifications\DRMNotification;
use App\StripePayment;

class DrmSubscriptionController extends Controller
{
	//Subscription action
    public function subscriptionAction(){
		ManualSubscription::where('status', 1)
		->whereDate('period_end', '<=', now())
		->orderBy('id')
		->select('id')
		->chunk(10, function($items) {
			$items->each(function($item) {
				DRMManualSubscriptionNotifyJob::dispatch($item->id);
			});
		});
    }

    //Payment notify
    public function paymentNotify($subscriptionId){
    	try {
		    $curl = curl_init();
		    curl_setopt_array($curl, array(
		      	CURLOPT_URL => config('app.drm_url').'/api/manual-subscription-renew/'.$subscriptionId.'?token=tyMcR63U78vg1Nm',
		      	CURLOPT_RETURNTRANSFER => true,
		      	CURLOPT_ENCODING => '',
		      	CURLOPT_MAXREDIRS => 10,
		      	CURLOPT_TIMEOUT => 0,
		      	CURLOPT_FOLLOWLOCATION => true,
		      	CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		      	CURLOPT_CUSTOMREQUEST => 'GET',
		    ));

		    $response = curl_exec($curl);

		    curl_close($curl);
		    return $response;
		} catch (\Exception $e) {
		    return $e->getMessage();
		}
    }
}

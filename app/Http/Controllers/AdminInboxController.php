<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;
    use App\User;
    use Illuminate\Support\Str;
	use GuzzleHttp\Client;
	use AppStore;
	

	class AdminInboxController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {
			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "id";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = true;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = false;
			$this->button_edit = false;
			$this->button_delete = false;
			$this->button_detail = false;
			$this->button_show = false;
			$this->button_filter = false;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "inbox";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"User Id","name"=>"user_id"];
			$this->col[] = ["label"=>"Shop Id","name"=>"shop_id"];
			$this->col[] = ["label"=>"Product Id","name"=>"product_id"];
			$this->col[] = ["label"=>"Ean","name"=>"ean"];
			$this->col[] = ["label"=>"Name","name"=>"name"];
			$this->col[] = ["label"=>"Product Model","name"=>"product_model"];
			//$this->col[] = ["label"=>"Image","name"=>"image","image"=>true,"width"=>"25"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];

			$this->form[] = ['label'=>'Ean','name'=>'ean','type'=>'text','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Name','name'=>'name','type'=>'text','width'=>'col-sm-10','placeholder'=>'You can only enter the letter only'];
			$this->form[] = ['label'=>'Price','name'=>'price','type'=>'text','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Product Id','name'=>'product_id','type'=>'select','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Product Model','name'=>'product_model','type'=>'text','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Quantity','name'=>'quantity','type'=>'text','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Shop Id','name'=>'shop_id','type'=>'select','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'User Id','name'=>'user_id','type'=>'select','width'=>'col-sm-10'];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ["label"=>"Ean","name"=>"ean","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Image","name"=>"image","type"=>"upload","required"=>TRUE,"validation"=>"required|image|max:3000","help"=>"File types support : JPG, JPEG, PNG, GIF, BMP"];
			//$this->form[] = ["label"=>"Name","name"=>"name","type"=>"text","required"=>TRUE,"validation"=>"required|string|min:3|max:70","placeholder"=>"You can only enter the letter only"];
			//$this->form[] = ["label"=>"Price","name"=>"price","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Product Id","name"=>"product_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"product,id"];
			//$this->form[] = ["label"=>"Product Model","name"=>"product_model","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Quantity","name"=>"quantity","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Shop Id","name"=>"shop_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"shop,id"];
			//$this->form[] = ["label"=>"User Id","name"=>"user_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"user,id"];

			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

	        //$collection=DB::table('gambio_products')->select('ean')->groupBy('ean')->havingRaw('count(*) > 1')->get();

	        //dd($collection);
	        if(! CRUDBooster::isSuperadmin()){
	            $query->where('user_id',CRUDBooster::myId());
	        }
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	//Your code here
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

		}
		
		public function installCheck()
		{
			try {
				$cmsuserInfo = DB::table('cms_users')->where('id',CRUDBooster::myId())->first();
	
				$database_name = str_replace(' ', '_', $cmsuserInfo->name);
				$db_name_lower = strtolower($database_name);
				$db_name = preg_replace("/[^A-Za-z0-9\-\']/", '', $db_name_lower);
				$apiRequest['id'] = $cmsuserInfo->id;
				$apiRequest['db_host'] = '************';
				$apiRequest['db_name'] = $db_name;
				$apiRequest['db_user'] = 'forge';
				$apiRequest['db_password'] = 'EVlesfB2hRbs5VE3657S';
				$apiRequest['db_port'] = '3306';
				$apiRequest['name'] = $cmsuserInfo->name;
				$apiRequest['email'] = $cmsuserInfo->email;
				$apiRequest['password'] = $cmsuserInfo->password;
	
				$url = "https://drm.network/api/check-installation";
				$client = new Client();
	
				$response  = $client->post($url,  ['form_params'=>$apiRequest]);
				return json_decode($response->getBody()->getContents(), TRUE);
				
			} catch (\Exception $e) {
				CRUDBooster::redirect(CRUDBooster::adminPath(),	'Something went wrong!. Error: '.$e->getMessage());
			}
		}

		public function getIndex()
		{
			// $data['app_details']  = AppStore::ActiveApp('Email-Ticketsystem');
			
				$basic_check  = AppStore::CheckAppPurchaseBoolean('34');
				
				if(!$basic_check[0]){
					CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access").' No Permission!');
				}else{
					$data['app_details'] = $basic_check[1];
				}
			

			$userInfo = DB::table('mail_setting')->where('user_id',CRUDBooster::myId())->first();
			$cmsuserInfo = DB::table('cms_users')->where('id',CRUDBooster::myId())->first();

			$cmsuserhost = Str::after($cmsuserInfo->email,'@');
		
			($this->installCheck()) ? $data['url'] = 'https://drm.network' : $data['url'] = 'https://drm.network';
					
           	$this->cbView('admin.mail_index_page',$data);
        }

	   public function getMentoring()
	   {

			$user = User::find(CRUDBooster::myId());
			$status = $user->plan_status;

			$check_appoinment = DB::table('takeappointment')->where('user_id',CRUDBooster::myId())->first();
			$check_user_id = $check_appoinment->user_id;
			$payment_date_remaining = $check_appoinment->payment_date_remaining;
			$payment_date_remainings = $payment_date_remaining-1;


			$data['page_title'] = 'Mentoring';
			$data['page_icon'] = "fa fa-desktop text-normal";

			//Create a view. Please use `cbView` method instead of view method from laravel.
			if(CRUDBooster::myPrivilegeId() == 3 && CRUDBooster::myId() == $check_user_id)
			{
				if($payment_date_remaining > 0)
				{

					if($check_user_id == CRUDBooster::myId())
					{
						if($status == 'Free')
						{
							$take_appointment = DB::table('takeappointment')->where('user_id',CRUDBooster::myId())->update(
								['payment_date_remaining' => $payment_date_remainings]
							);
						}
						else
						{
							$take_appointment = DB::table('takeappointment')->where('user_id',CRUDBooster::myId())->update(
								['payment_date_remaining' => $payment_date_remainings]
							);
						}
					}
					$this->cbView('admin.mentoring_page',$data);
				}
				else
				{
					return redirect('/admin');
				}
			}
			else{
				$this->cbView('admin.mentoring_page',$data);
			}
		}
	}
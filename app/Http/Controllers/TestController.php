<?php

namespace App\Http\Controllers;
use App\Shop;
use App\NewOrder;
use Carbon\Carbon;
use App\Models\UserDetails;
use App\MarketplaceProducts;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;

use App\Jobs\ProcessMPRatingUpdateJob;
use App\Models\Product\AnalysisProduct;
use App\Services\UniversalExportService;
use App\Jobs\ProcessDecathlonColumnUpdateJob;
use App\Models\Product\ComparisonAnalysisProduct;

class TestController extends Controller {

	public function debugOrderSyncInterval()
	{
		$normal = $this->debugNormalOrderSyncInterval();
		$bronze = $this->debugBronzeSyncInterval();
		$gold = $this->debugGoldSyncInterval();
		$platinum = $this->debugPlatinumSyncInterval();
		$silver = $this->debugSilverSyncInterval();

		$merge = array_merge($normal['all'], $bronze['all'], $gold['all'], $platinum['all'], $silver['all']);
		$mergeShops = array_merge($normal['shops'], $bronze['shops'], $gold['shops'], $platinum['shops'], $silver['shops']);

		return [
			'normal_24h' => $normal,
			'bronze_12h' => $bronze,
			'silver_6h' => $silver,
			'gold_3h' => $gold,
			'platinum_30m' => $platinum,
			'unique' => array_unique($merge),
			'unique_shops' => $mergeShops,
		];
	}

    public function handle()
    {
        // Define the thresholds and corresponding tags
        $thresholds = [
            500000 => ['Total sell is 500.000€',33],
            250000 => ['Total sell is 250.000€',32],
            100000 => ['Total sell is 100.000€',31],
            10000  =>  ['Total sell is 10.000€',30]
        ];// Define the thresholds and corresponding tags

        // Process UserDetails records in chunks
        foreach ($this->processUserDetails() as $userDetail) {
            foreach ($thresholds as $threshold => $tag) {
                // Check if the total sell amount meets the threshold
                if ($userDetail->total_amount >= $threshold) {
                    // Check if the user already has the tag
                    $tag[0] = 'lop';
                    $existingTag = $userDetail->dropfunnelTags->firstWhere('tag', $tag[0]);
                    dd($existingTag, $userDetail);
                    // If the tag doesn't exist, create it
                    if (!$existingTag) {
                        DropfunnelCustomerTag::insertTag($tag[0], $userDetail->user_id, $userDetail->customer_id, $tag[1]);
                    }

                    // Break the loop after processing the user for the current threshold
                    break;
                }
            }
        }
    }
    // Define the generator function to process UserDetails records in chunks
    private function processUserDetails() {
        try {
            foreach (UserDetails::where('total_amount', '>=', 10000)->cursor() as $userDetail) {
                yield $userDetail;
            }
        } catch (\Exception $e) {
            dd('Error occurred while processing UserDetails records: ' . $e->getMessage());
        }
    }

    public function processTest(){
        // $users = UserDetails::with('dropfunnelTags')->count();
        // dd($users);
        dd($this->handle());
        $user_id = request()->user_id;
        $feed_id = request()->feed_id;
        app(UniversalExportService::class)->syncFeed($user_id,$feed_id);
    }
    public function processhandle()
    {
        foreach ($this->getComparisonAnalysisProductChunks() as $chunk) {
            $this->updateComparisonAnalysisProducts($chunk);
        }
    }

    protected function getComparisonAnalysisProductChunks()
    {
        $offset = 0;
        $limit = 200;

        do {
            $products = ComparisonAnalysisProduct::whereNull('deleted_at')
                        ->select('ean', 'id')
                        ->offset($offset)
                        ->limit($limit)
                        ->get();
            if(request()->test == 1) dd($products);

            if ($products->isNotEmpty()) {
                yield $products;
                $offset += $limit;
            } else {
                break; // No more products found, exit the loop
            }
        } while (true);
    }

    protected function updateComparisonAnalysisProducts($products)
    {
        $eans = $products->pluck('ean')->toArray();

        $marketplaceProducts = MarketplaceProducts::whereIn('ean', $eans)
                                ->orderBy('stock', 'asc')
                                ->select('stock', 'ean', 'ek_price')
                                ->get();

        $marketplaceProducts->each(function($item) use ($eans) {
            ComparisonAnalysisProduct::whereIn('ean', $eans)->update([
                'mp_stock' => $item->stock,
                'mp_ek_price' => $item->ek_price,
                'updated_at' => now()
            ]);
        });
    }
	private function debugNormalOrderSyncInterval()
	{
		$app_id = config('global.interval_app_id');
		$sync_shop_types = config('global.sync_shop_types');

	    $user_has_plan =  \DB::table('purchase_apps')->where('purchase_apps.app_id', $app_id)->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('cms_user_id')->pluck('cms_user_id')->toArray();
	    $user_has_assign = \DB::table('app_assigns')->where(['app_id'=>$app_id])
	    ->where(function($amnu){
	        $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
	    })
	    ->select('user_id')->pluck('user_id')->toArray();

        $users = array_unique(array_merge($user_has_plan, $user_has_assign));
        $shop_arr = \DB::table('shops')
            ->join('cms_users', function ($join) use ($users) {
                $join->on('shops.user_id', '=', 'cms_users.id')
                ->whereIntegerNotInRaw('cms_users.id', $users)
                ->where('cms_users.status', 'active');
            })
            ->where('shops.status', 1)
            ->whereIn('shops.channel', $sync_shop_types)
            ->select('shops.*')
            ->get();

	    return [
	    	// 'assign' => $user_has_assign,
	    	// 'purchase' => $user_has_plan,
	    	'all' => $users,
	    	'shops' => array_map('intval', $shop_arr->pluck('user_id')->unique()->values()->toArray()),
	    ];
	}



	public function debugBronzeSyncInterval()
	{
	    $app_id = config('global.interval_app_id');
	    $plan_id = config('global.interval_bronze_plan');
	    $sync_shop_types = config('global.sync_shop_types');

	    $user_has_plan =  \DB::table('purchase_apps')->where(['app_id' => $app_id, 'plan_id' => $plan_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('cms_user_id')->pluck('cms_user_id')->toArray();
	    $user_has_assign = \DB::table('app_assigns')->where(['app_id'=>$app_id, 'plan_id'=>$plan_id])
	    ->where(function($amnu){
	        $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
	    })
	    ->select('user_id')->pluck('user_id')->toArray();

        $users = array_unique(array_merge($user_has_plan, $user_has_assign));
        $shop_arr = \DB::table('shops')
            ->join('cms_users', function ($join) use ($users) {
                $join->on('shops.user_id', '=', 'cms_users.id')
                ->whereIntegerInRaw('cms_users.id', $users)
                ->where('cms_users.status', 'active');
            })
            ->where('shops.status', 1)
            ->whereIn('shops.channel', $sync_shop_types)
            ->select('shops.*')
            ->get();

	    return [
	    	// 'assign' => $user_has_assign,
	    	// 'purchase' => $user_has_plan,
	    	'all' => $users,
	    	'shops' => array_map('intval', $shop_arr->pluck('user_id')->unique()->values()->toArray()),
	    ];

	}

	public function debugGoldSyncInterval()
	{
	    $app_id = config('global.interval_app_id');
	    $plan_id = config('global.interval_gold_plan');
	    $sync_shop_types = config('global.sync_shop_types');

	    $user_has_plan =  \DB::table('purchase_apps')->where(['app_id' => $app_id, 'plan_id' => $plan_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('cms_user_id')->pluck('cms_user_id')->toArray();
	    $user_has_assign = \DB::table('app_assigns')->where(['app_id'=>$app_id, 'plan_id'=>$plan_id])
	    ->where(function($amnu){
	        $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
	    })
	    ->select('user_id')->pluck('user_id')->toArray();

        $users = array_unique(array_merge($user_has_plan, $user_has_assign));
        $shop_arr = \DB::table('shops')
            ->join('cms_users', function ($join) use ($users) {
                $join->on('shops.user_id', '=', 'cms_users.id')
                ->whereIntegerInRaw('cms_users.id', $users)
                ->where('cms_users.status', 'active');
            })
            ->where('shops.status', 1)
            ->whereIn('shops.channel', $sync_shop_types)
            ->select('shops.*')
            ->get();

	    return [
	    	// 'assign' => $user_has_assign,
	    	// 'purchase' => $user_has_plan,
	    	'all' => $users,
	    	'shops' => array_map('intval', $shop_arr->pluck('user_id')->unique()->values()->toArray()),
	    ];
	}


	public function debugPlatinumSyncInterval()
	{
	    $app_id = config('global.interval_app_id');
	    $plan_id = config('global.interval_platinum_plan');
	    $sync_shop_types = config('global.sync_shop_types');

	    $user_has_plan =  \DB::table('purchase_apps')
	    ->where('app_id', $app_id)
	    ->where(function($q) use ($plan_id) {
	    	$q->where('plan_id', $plan_id)
	    	->orWhereNull('is_free_trail');
	    })->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('cms_user_id')->pluck('cms_user_id')->toArray();
	    $user_has_assign = \DB::table('app_assigns')->where(['app_id'=>$app_id, 'plan_id'=>$plan_id])
	    ->where(function($amnu){
	        $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
	    })
	    ->select('user_id')->pluck('user_id')->toArray();

        $users = array_unique(array_merge($user_has_plan, $user_has_assign));
        $shop_arr = \DB::table('shops')
            ->join('cms_users', function ($join) use ($users) {
                $join->on('shops.user_id', '=', 'cms_users.id')
                ->whereIntegerInRaw('cms_users.id', $users)
                ->where('cms_users.status', 'active');
            })
            ->where('shops.status', 1)
            ->whereIn('shops.channel', $sync_shop_types)
            ->select('shops.*')
            ->get();

	    return [
	    	// 'assign' => $user_has_assign,
	    	// 'purchase' => $user_has_plan,
	    	'all' => $users,
	    	'shops' => array_map('intval', $shop_arr->pluck('user_id')->unique()->values()->toArray()),
	    ];
	}


	public function debugSilverSyncInterval()
	{
	    $app_id = config('global.interval_app_id');
	    $plan_id = config('global.interval_silver_plan');
	    $sync_shop_types = config('global.sync_shop_types');

	    $user_has_plan =  \DB::table('purchase_apps')->where(['app_id' => $app_id, 'plan_id' => $plan_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('cms_user_id')->pluck('cms_user_id')->toArray();
	    $user_has_assign = \DB::table('app_assigns')->where(['app_id'=>$app_id, 'plan_id'=>$plan_id])
	    ->where(function($amnu){
	        $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
	    })
	    ->select('user_id')->pluck('user_id')->toArray();

        $users = array_unique(array_merge($user_has_plan, $user_has_assign));
        $shop_arr = \DB::table('shops')
            ->join('cms_users', function ($join) use ($users) {
                $join->on('shops.user_id', '=', 'cms_users.id')
                ->whereIntegerInRaw('cms_users.id', $users)
                ->where('cms_users.status', 'active');
            })
            ->where('shops.status', 1)
            ->whereIn('shops.channel', $sync_shop_types)
            ->select('shops.*')
            ->get();

	    return [
	    	// 'assign' => $user_has_assign,
	    	// 'purchase' => $user_has_plan,
	    	'all' => $users,
	    	'shops' => array_map('intval', $shop_arr->pluck('user_id')->unique()->values()->toArray()),
	    ];
	}

	public function appointmentReminderMailTest(){
		try{
			AnalysisProduct::where('source', 2)->chunk(1000, function($rows){
				$ids = $rows->pluck('id')->toArray();
				ProcessMPRatingUpdateJob::dispatch($ids);
			});
			dd("done!");
			ProcessDecathlonColumnUpdateJob::dispatchNow();
			$currentTime = Carbon::now()->format('Y-m-d H:i');
			DB::table('appointments')->where('status', '0')->where('customer_id', '!=', '0')->get()
				->each(function ($item) use ($currentTime) {
					$appointmentDateTimes = explode('T', json_decode($item->appointment)['0']->start);
					$appointment_date = $appointmentDateTimes['0'];
					$appointment_time = $appointmentDateTimes['1'];

					$remainder_time = json_decode(DB::table('appointment_slot')->where('user_id', $item->user_id)->value('input_array'));
					$time = $remainder_time->reminder_time;
					$type = $remainder_time->type;
					if($type == 'minute')
                        $type = 'Minuten';
                    if($type == 'hour')
                        $type = 'Stunden';
					$isNotifyMe = $remainder_time->remainder_to_me;

					$appointmentTime = Carbon::parse($appointment_date . ' ' . $appointment_time);
					if (!empty($time) && $appointmentTime) {
						if ($type == 'hour') {
							$compareTime = $appointmentTime->subHours($time)->format('Y-m-d H:i');
						} else {
							$compareTime = $appointmentTime->subMinutes($time)->format('Y-m-d H:i');
						}
						$to = json_decode($item->appointment)['0']->email;
						$from = DB::table('cms_users')->where('id', $item->user_id)->value('email');

						if ($currentTime == $compareTime && !empty($to) && !empty($from)) {
							$this->AppointmentRemainder($to, $from, $time, $type, $item->user_id, $item->id);
							if(($isNotifyMe == 'on')){
								$this->AppointmentRemainder($from, $from, $time, $type, $item->user_id, $item->id);
							}
							sleep(2);
						}
					}
				}
			);
		} catch(Exception $e){
			dd($e);
		}
	}

	protected function AppointmentRemainder($to, $from, $time, $type, $user_id, $appointment_id)
    {
      try {
        $template = config('system_email_settings.appointment_reminder_mail_body');
        $subject = config('system_email_settings.appointment_reminder_mail_subject');
        $emailSetting = DB::table('appointment_email_settings')->where('status', 2)->where('cms_user_id', $user_id)->first();
        $userEmail = DB::table('cms_users')->where('id', $user_id)->value('email');
		$appointment = DB::table('appointments')->where('id', $appointment_id)->first();
        $data = [];
        $data['email_to'] = $to;
		$data['email_from'] = $from;
		$data['subject'] = $subject;
		$day = Carbon::parse(json_decode($appointment->appointment)['0']->date);
        if (!empty($emailSetting)) {
            $data['subject'] = empty($emailSetting->mail_subject) ? $subject : $emailSetting->mail_subject;
            $data['email_from'] = empty($emailSetting->sender_email) ? $userEmail : $emailSetting->sender_email;
            $template = empty($emailSetting->email_template) ? $template : $emailSetting->email_template;
        }

        $invoiceSetting = DB::table('drm_invoice_setting')->select('logo', 'store_name')->where('cms_user_id', $user_id)->orderBy('id', 'desc')->first();

        $img = $invoiceSetting->logo ?? '';
        $logo = '<img id="display_logo" width="150" src="' . $img . '" alt="' . $invoiceSetting->store_name . '">';

        // $logo = '<img id="display_logo" width="150" src="" alt="">';
        $template = preg_replace('/\[logo]/', $logo, $template);
        $template = preg_replace('/\[customer_name]/', json_decode($appointment->appointment)['0']->title, $template);
        $template = preg_replace('/\[company_name]/', '', $template);
        $template = preg_replace('/\[appointment_date]/', json_decode($appointment->appointment)['0']->date, $template);
        $template = preg_replace('/\[appointment_time]/', json_decode($appointment->appointment)['0']->start_time, $template);
        $template = preg_replace('/\[time]/', $time, $template);
        $template = preg_replace('/\[type]/', $type, $template);

        $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', $user_id)->pluck('signature','id')->toArray();
		$signature_tags = [];

		if($email_signatures){
			foreach($email_signatures as $key => $signature){
				$template = preg_replace('/\[drm-sign-'.$key.']/', $signature, $template);
			}
		}

        $data['subject'] = preg_replace('/\[appointment_date]/', json_decode($appointment->appointment)['0']->date, $data['subject']);
        $data['subject'] = preg_replace('/\[appointment_time]/', json_decode($appointment->appointment)['0']->start_time, $data['subject']);

        app('drm.mailer')->getMailer($user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', ['body' => $template], function ($messages) use ($data) {
            // $messages->from($data['email_from']);
            $messages->to($data['email_to']);
            $messages->subject($data['subject']);
        });
      } catch (Exception $exception) {
		dd($exception);

            Log::channel('command')->info($exception);
      }
    }

	public function bestCalTest($id)
	{
		$time_start = microtime(true);
		$getAllSoldProducts = $this->getMpSoldProducts();
		$time_middle = microtime(true);

		if($id == 1){
			dd(($time_middle - $time_start)/60);
		}

        $takeHundredBestProducts = $this->mpSoldProductsFormat($getAllSoldProducts, 100);
		$time_end = microtime(true);

		if($id == 2){
			dd(($time_end - $time_middle)/60);
		}

		//dividing with 60 will give the execution time in minutes otherwise seconds
		$execution_time_1 = ($time_middle - $time_start)/60;
		$execution_time_2 = ($time_end - $time_middle)/60;
		$execution_time_3 = ($time_end - $time_start)/60;

		dd($execution_time_1, $execution_time_2, $execution_time_3);
	}

    public function getMpSoldProducts()
    {
        $mp_order_data = [];
        $all_ordered_products = [];
        $ordered_mp_product_id = [];

        NewOrder::where('new_orders.insert_type', 8)
        ->where('new_orders.cms_user_id', 2455)
        ->where('new_orders.test_order', '<>', 1)
        ->where('new_orders.invoice_number', '>', 0)
        ->whereDate('order_date', '>=', Carbon::now()->subYear()) // Last 1 year mp sold product get
        ->select('new_orders.id', 'new_orders.cart')
        ->chunk(1000, function($mp_orders) use(&$ordered_mp_product_id, &$all_ordered_products){
            foreach ($mp_orders as $order) {
            if (!is_null($order->products)) {
                foreach ($order->products as $product) {
                    unset($product->id);
                    unset($product->description);
                    unset($product->rate);
                    unset($product->tax);
                    unset($product->product_discount);
                    unset($product->item_number);
                    unset($product->delivery_days);
                    unset($product->ean);
                    unset($product->shipping_cost);
                    unset($product->delivery_days);

                    $product->order_id = $order->id;
                    $all_ordered_products[] = (array) $product;

                    if(!in_array($product->marketplace_product_id, $ordered_mp_product_id)){
                        $ordered_mp_product_id[] = $product->marketplace_product_id;
                    }
                }
            }
            }
        });

        $mp_order_data['sold_product_mp'] = $all_ordered_products;
        $mp_order_data['sold_product_mp_ids'] = $ordered_mp_product_id;

        return $mp_order_data;
    }

    public function mpSoldProductsFormat($allsoldOrders, $limit)
    {
        $array_product = $allsoldOrders['sold_product_mp'];
        $ordered_mp_product_id = $allsoldOrders['sold_product_mp_ids'];

        $mp_existing_products = $this->checkOrderedMpProductStoreExists($ordered_mp_product_id); //Filtering if mp sold product still at mp and return mp_product model collection

        $mp_existing_product_ids = array_column($mp_existing_products->toArray(), 'id');

    //   $drm_transferred_ids = $this->checkOrderedMpProductDrmExists($ordered_mp_product_id, $user_id); //Check for if product already transferred to main level
    //   $mp_existing_product_ids = array_diff($mp_existing_product_ids, $drm_transferred_ids);

        $products_collection = collect($array_product);
        $products_collection = $products_collection->whereIn('marketplace_product_id', $mp_existing_product_ids)->groupBy('marketplace_product_id');

        $top_product_collection = [];

        foreach ($products_collection as $product) {
            $count = count($product);
            $qty = $product->sum('qty');

            $item = reset($product);

            if (isset($item[0])) {

                // $mp_pro_id = $item[0]['marketplace_product_id'];
                // $mp_pro_vk_price = $mp_existing_products->where('id', $mp_pro_id)->pluck('vk_price')->first();

                $item[0]['sell_count'] = $count; // Total how many times orders the products
                $item[0]['sell_qty'] = $qty; // Total how much unit sold of the products
                // $item[0]['vk_price'] = $mp_pro_vk_price;

                unset($item[0]['product_name']);
                unset($item[0]['product_id']);
                unset($item[0]['supplier_id']);
                unset($item[0]['mp_supplier_id']);
                unset($item[0]['order_id']);
                unset($item[0]['shipping_method']);
                unset($item[0]['qty']);
                unset($item[0]['amount']);
                unset($item[0]['image']);

                $top_product_collection[] = $item[0];
            }
        }

        $best_mp_selling_products = collect($top_product_collection ?? [])
        ->sortByDesc('sell_qty')
        ->take($limit)
        ->values();

        return $best_mp_selling_products;
    }

    public function checkOrderedMpProductStoreExists($ordered_product_id)
    {
        $existing_products = collect();

        Product::select('id', 'category_id', 'vk_price')
        ->chunk(1000, function($products) use($ordered_product_id, &$existing_products){

            foreach ($products as $product){
                if(in_array($product->id, $ordered_product_id)) $existing_products->push($product);
            }

        });

        return $existing_products;
    }

}

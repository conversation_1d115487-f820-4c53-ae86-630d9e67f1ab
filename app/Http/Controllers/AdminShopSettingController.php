<?php

namespace App\Http\Controllers;

use Exception;

use League\Csv\Writer;
use League\Csv\Reader;

use Session;
use Request;
use DB;
use CRUDBooster;
use Auth;
use App\Helper\LengowApi;
use DateTime;
use App\Helper\GambioApi;
use App\Helper\ShopifyApi;
use App\Helper\EbayApi;
use App\Services\Modules\Export\WooCommerce;
use \Hkonnet\LaravelEbay\EbayServices;
use \DTS\eBaySDK\Trading\Services;
use \DTS\eBaySDK\Trading\Types;
use \DTS\eBaySDK\Trading\Enums;
use \DTS\eBaySDK\Constants;
use Ebay;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use App\Notifications\DRMNotification;
use App\User;
use Carbon\Carbon;
use App\Http\Controllers\AdminCategoriesController;
use Schema;

class AdminShopSettingController extends \crocodicstudio\crudbooster\controllers\CBController
{

    public function cbInit()
    {
        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "id";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = true;
        $this->button_bulk_action = true;
        $this->button_addmore = false;
        $this->button_action_style = "button_icon";

        if ($_REQUEST['id']) {
            $this->button_add = true;
        } else {
            $this->button_add = false;
        }

        $this->button_edit = true;
        $this->button_delete = true;
        $this->button_detail = true;
        $this->button_show = false;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "shops";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        $this->col = [];
        $this->col[] = ["label" => "id", "name" => "id"];
        if (CRUDBooster::isSuperadmin()) {
            $this->col[] = ["label" => "Userid", "name" => "user_id"];
        }
        $this->col[] = ["label" => "Shop Name", "name" => "shop_name"];
        $this->col[] = ["label" => "Shop Type", "name" => "channel"];
        $this->col[] = ["label" => "Shop URL", "name" => "url"];
        $this->col[] = ["label" => "Products", "name" => "channel"];

        //$this->col[] = ["label"=>"User","name"=>"user"];
        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];
        $this->form[] = ['label' => 'Shop Type', 'name' => 'channel', 'id' => 'channel', 'type' => 'select', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10', 'dataenum' => '1|Gambio;2|Lengow;3|Yatego;4|eBay;6|Shopify;7|Woocommerce;8|ClouSale;9|Chrono24', 'help' => 'Gambio Version 3.15.2', 'value' => $_GET['id']];
        $this->form[] = ['label' => 'Shop Name', 'name' => 'shop_name', 'type' => 'text', 'validation' => 'required', 'width' => 'col-sm-10', 'placeholder' => 'Please Enter Shop Name'];
        $this->form[] = ['label' => 'Shop URL', 'name' => 'url', 'type' => 'text', 'validation' => 'required', 'width' => 'col-sm-10', 'placeholder' => 'Please enter valid shop URL : https://example.com/'];
        $this->form[] = ['label' => 'Username / Access Token', 'name' => 'user', 'type' => 'text', 'width' => 'col-sm-10', 'placeholder' => 'Please enter a username or access token'];
        $this->form[] = ['label' => 'Password / Secret', 'name' => 'password', 'type' => 'text', 'width' => 'col-sm-10', 'placeholder' => 'Please enter a user password or secret'];
        $this->form[] = ['label' => 'User Id', 'name' => 'user_id', 'type' => 'hidden'];
        $this->form[] = ['label' => 'Language', 'name' => 'lang', 'type' => 'hidden', 'value' => $_GET['lang']];
        // $this->form[] = ['label'=>'Access Token','name'=>'token','type'=>'text','width'=>'col-sm-10','placeholder'=>'Please enter a valid access token'];
        // $this->form[] = ['label'=>'Secret','name'=>'secret','type'=>'text','width'=>'col-sm-10','placeholder'=>'Please enter a valid secret'];
        /*
                        | ----------------------------------------------------------------------
                        | Add javascript at body
                        | ----------------------------------------------------------------------
                        | javascript code in the variable
                        | $this->script_js = "function() { ... }";
                        |
                        */
        $this->script_js = "";


        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ['label'=>'Shop URL','name'=>'url','type'=>'text','validation'=>'required|url','width'=>'col-sm-10','placeholder'=>'Please enter a valid shop URL'];
        //$this->form[] = ['label'=>'Username','name'=>'user','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        //$this->form[] = ['label'=>'Password','name'=>'pass','type'=>'text','validation'=>'required|min:6|max:32','width'=>'col-sm-10'];
        # OLD END FORM

        /*
                        | ----------------------------------------------------------------------
                        | Sub Module
                        | ----------------------------------------------------------------------
                | @label          = Label of action
                | @path           = Path of sub module
                | @foreign_key    = foreign key of sub table/module
                | @button_color   = Bootstrap Class (primary,success,warning,danger)
                | @button_icon    = Font Awesome Class
                | @parent_columns = Sparate with comma, e.g : name,created_at
                |
                */
        $this->sub_module = array();


        /*
                | ----------------------------------------------------------------------
                | Add More Action Button / Menu
                | ----------------------------------------------------------------------
                | @label       = Label of action
                | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
                | @icon        = Font awesome class icon. e.g : fa fa-bars
                | @color       = Default is primary. (primary, warning, succecss, info)
                | @showIf      = If condition when action show. Use field alias. e.g : [id] == 1
                |
                */
        $this->addaction = array();
        $this->addaction[] = ['label' => 'Image Re-Sync', 'url' => CRUDBooster::mainpath('re-sync-image-gambio-product/[id]'), 'icon' => 'fa fa-refresh', 'color' => 'danger', 'showIf' => '[channel] == 1'];
        $this->addaction[] = ['label' => 'Sync Category', 'url' => CRUDBooster::mainpath('sync-category/[id]'), 'icon' => 'fa fa-refresh', 'color' => 'info', 'showIf' => '[channel] == 1'];
        // if ($_GET['id'] == 1 || $_GET['id'] == 4 || $_GET['id'] == 6 ) {
        //  $this->addaction[] = ['label' => 'Sync Products', 'url' => CRUDBooster::mainpath('sync-shop-product/[id]'), 'icon' => 'fa fa-refresh', 'color' => 'warning'];
        // }
        // $this->addaction[] = ['label' => 'Sync Order', 'url' => CRUDBooster::mainpath('sync/[id]'), 'icon' => 'fa fa-refresh', 'color' => 'primary'];
        $this->addaction[] = ['label' => 'Export', 'url' => CRUDBooster::mainpath('export/[id]'), 'icon' => 'fa fa-upload', 'color' => 'primary', 'showIf' => '[channel] == 1' ];
        $this->addaction[] = ['label' => 'Export', 'url' => CRUDBooster::mainpath('export/[id]'), 'icon' => 'fa fa-upload', 'color' => 'primary', 'showIf' => '[channel] == 2' ];
        $this->addaction[] = ['label' => 'Export', 'url' => CRUDBooster::mainpath('export/[id]'), 'icon' => 'fa fa-upload', 'color' => 'primary', 'showIf' => '[channel] == 3' ];
        $this->addaction[] = ['label' => 'Export', 'url' => CRUDBooster::mainpath('export/[id]'), 'icon' => 'fa fa-upload', 'color' => 'primary', 'showIf' => '[channel] == 4' ];
        $this->addaction[] = ['label' => 'Export', 'url' => CRUDBooster::mainpath('export/[id]'), 'icon' => 'fa fa-upload', 'color' => 'primary', 'showIf' => '[channel] == 6' ];
        $this->addaction[] = ['label' => 'Export', 'url' => CRUDBooster::mainpath('export/[id]'), 'icon' => 'fa fa-upload', 'color' => 'primary', 'showIf' => '[channel] == 7' ];

        $this->addaction[] = ['label' => 'Export', 'url' => url('api/get-export-clou-sale-csv/?id=[id]'), 'icon' => 'fa fa-upload', 'color' => 'primary', 'showIf' => '[channel] == 8' ];
        $this->addaction[] = ['label' => 'Export', 'url' => url('api/export-chrono24-xml/[id]'), 'icon' => 'fa fa-upload', 'color' => 'primary', 'showIf' => '[channel] == 9' ];


        /*
                | ----------------------------------------------------------------------
                | Add More Button Selected
                | ----------------------------------------------------------------------
                | @label       = Label of action
                | @icon        = Icon from fontawesome
                | @name        = Name of button
                | Then about the action, you should code at actionButtonSelected method
                |
                */
        $this->button_selected = array();


        /*
                | ----------------------------------------------------------------------
                | Add alert message to this module at overheader
                | ----------------------------------------------------------------------
                | @message = Text of message
                | @type    = warning,success,danger,info
                |
                */
        $this->alert        = array();



        /*
                | ----------------------------------------------------------------------
                | Add more button to header button
                | ----------------------------------------------------------------------
                | @label = Name of button
                | @url   = URL Target
                | @icon  = Icon from Awesome.
                |
                */
        $this->index_button = array();



        /*
                | ----------------------------------------------------------------------
                | Customize Table Row Color
                | ----------------------------------------------------------------------
                | @condition = If condition. You may use field alias. E.g : [id] == 1
                | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
                |
                */
        $this->table_row_color = array();


        /*
                | ----------------------------------------------------------------------
                | You may use this bellow array to add statistic at dashboard
                | ----------------------------------------------------------------------
                | @label, @count, @icon, @color
                |
                */
        $this->index_statistic = array();




        /*
                | ----------------------------------------------------------------------
                | Include HTML Code before index table
                | ----------------------------------------------------------------------
                | html code to display it before index table
                | $this->pre_index_html = "<p>test</p>";
                |
                */
        $this->pre_index_html = null;



        /*
                | ----------------------------------------------------------------------
                | Include HTML Code after index table
                | ----------------------------------------------------------------------
                | html code to display it after index table
                | $this->post_index_html = "<p>test</p>";
                |
                */
        $this->post_index_html = null;



        /*
                | ----------------------------------------------------------------------
                | Include Javascript File
                | ----------------------------------------------------------------------
                | URL of your javascript each array
                | $this->load_js[] = asset("myfile.js");
                |
                */
        $this->load_js = array();



        /*
                | ----------------------------------------------------------------------
                | Add css style at body
                | ----------------------------------------------------------------------
                | css code in the variable
                | $this->style_css = ".style{....}";
                |
                */
        $this->style_css = "";



        /*
                | ----------------------------------------------------------------------
                | Include css File
                | ----------------------------------------------------------------------
                | URL of your css each array
                | $this->load_css[] = asset("myfile.css");
                |
                */
        $this->load_css = array();
    }


    /*
        | ----------------------------------------------------------------------
        | Hook for button selected
        | ----------------------------------------------------------------------
        | @id_selected = the id selected
        | @button_name = the name of button
        |
        */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here

        if ($button_name == 'url_approve'); {
            self::ApproveURL($id_selected);
        }
    }


    /*
        | ----------------------------------------------------------------------
        | Hook for manipulate query of index result
        | ----------------------------------------------------------------------
        | @query = current sql query
        |
        */
    public function hook_query_index(&$query)
    {

    }

    /*
        | ----------------------------------------------------------------------
        | Hook for manipulate row of index table html
        | ----------------------------------------------------------------------
        |
        */
    public function hook_row_index($column_index, &$column_value)
    {

    }

    /*
        | ----------------------------------------------------------------------
        | Hook for manipulate data input before add data is execute
        | ----------------------------------------------------------------------
        | @arr
        |
        */
    public function hook_before_add(&$postdata)
    {

        $postdata['url'] = Str::start(str_replace('http://', 'https://', Str::finish($postdata['url'], '/')), 'https://');
        self::shopValidation((object) $postdata);

        $id = CRUDBooster::myId();
        $postdata["user_id"] = $id;
    }

    /*
        | ----------------------------------------------------------------------
        | Hook for execute command after add public static function called
        | ----------------------------------------------------------------------
        | @id = last insert id
        |
        */
    public function hook_after_add($id)
    {
        //Your code here
    }

    /*
        | ----------------------------------------------------------------------
        | Hook for manipulate data input before update data is execute
        | ----------------------------------------------------------------------
        | @postdata = input post data
        | @id       = current id
        |
        */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here

        $postdata['url'] = Str::start(str_replace('http://', 'https://', Str::finish($postdata['url'], '/')), 'https://');
        self::shopValidation((object) $postdata);
        $postdata['status'] = 1;
    }

    /*
        | ----------------------------------------------------------------------
        | Hook for execute command after edit public static function called
        | ----------------------------------------------------------------------
        | @id       = current id
        |
        */
    public function hook_after_edit($id)
    {
        //Your code here

        return redirect('admin/shop_setting?id=' . $_COOKIE['ShopSettingShopType'].'&lang=' . $_COOKIE['ShopSettingShopLang'])->send();
    }

    /*
        | ----------------------------------------------------------------------
        | Hook for execute command before delete public static function called
        | ----------------------------------------------------------------------
        | @id       = current id
        |
        */
    public function hook_before_delete($id)
    {
        //Your code here
    }

    /*
        | ----------------------------------------------------------------------
        | Hook for execute command after delete public static function called
        | ----------------------------------------------------------------------
        | @id       = current id
        |
        */
    public function hook_after_delete($id)
    {
        //Your code here
    }


    public function ApproveURL($id)
    {
        //dd($id);
        $urls = \App\Shop::get();
        foreach ($urls as $key => $value) {

            \App\Shop::update(['status' => "0"]);
        }
        foreach ($id as $ids) {

            \App\Shop::where('id', $ids)->update(['status' => "1"]);
            # code...
        }
    }

    public function getsync($id)
    {

        $this->stat['order'] = 0;
        $this->stat['details'];

        $shop = \App\Shop::where('id', $id)->first();

        if ($shop) {
            $shoptype = $shop->channel;
            $shopname   = $shop->shop_name;
            //dd($shopname);

        } else {
            dd("You have not configured any shop yet");
        }

        if ($shop->url == "") {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Api url is not given. Please add that.'), 'danger');
        }

        if ($shoptype == 1) {
            self::syncGambioorder($shop);
        } else if ($shoptype == 2) {

            self::syncLengowOrder($shop);
        } else if ($shoptype == 3) {

            self::syncYategoOrder($shop);
        } else if ($shoptype == 4) {

            self::syncEbayOrder($shop);
        }else if ($shoptype == 6) {

            self::syncShopifyOrder($shop);
        }

        // dd($this->stat['order'],$this->stat['details']);

        $data = [];
        $data['page_title'] = 'DRM Orders';
        //  $data['result'] = DB::table('drmordercontroller')->where('shopname',$shopname)->get();

        if (!CRUDBooster::isView()) CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));

        //  $this->cbView("admin.order.drm_order", $data);

        CRUDBooster::redirect(CRUDBooster::adminPath('drm_orders'), "All order has been synced !", "success");
    }



    public function getSyncCategory($id)
    {
        $shop = \App\Shop::where(['id' => $id,'channel' => 1])->first();
        $Categoriescontroller = new AdminCategoriesController();
        $Categoriescontroller->getGambioCategory($shop);
    }


    public function syncGambioorder($shop, $page = 1, &$count = 0)
    {
        try{
            if ($shop) {
                $user = $shop->username;
                $pass = $shop->password;
                $shopId = $shop->id;
                $base_url = $shop->url;
                $api_path = "api.php/v2/";
            } else {
                echo "You have not configured any shop yet!";
                throw new \Exception("You have not configured any shop yet!");
            }

            $auth = base64_encode("$user:$pass");

            $per_page = 50;
            $url  = $base_url . $api_path . "orders/?page=$page&per_page=$per_page";

            $headers = array("Authorization: Basic " . $auth);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");

            $response = curl_exec($ch);
            $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $errNo = curl_errno($ch);
            $errStr = curl_error($ch);
            curl_close($ch);
            // dd($responseCode,$errNo,$errStr);
            echo "Shop id : $shop->id code: $responseCode No: $errNo $errStr \n";

            if ($responseCode != 200) {
                throw new \Exception("Shop connection problem!");
            }

            $allOrder = json_decode($response);
            $cartsInfo = [];
            // dd($allOrder);

            foreach ((array) $allOrder as $indexv => $value) {
                $url  = $base_url . $api_path . "orders/$value->id/items?page=$page&per_page=$per_page";

                $headers = array("Authorization: Basic " . $auth);

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");


                $response = curl_exec($ch);
                $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $errNo = curl_errno($ch);
                $errStr = curl_error($ch);
                curl_close($ch);

                $cartsInfo[] = json_decode($response);
            }

            $this->bulkOrderInsertGambio($allOrder, $shop, $cartsInfo);

            if (count((array) $allOrder) != 0) {
                $page++;
                $count += count((array) $allOrder);
                $this->syncGambioorder($shop, $page, $count);
            }

            DB::table('api_order_sync_reports')->updateOrInsert([
                'shop_id' => $shop->id,
            ],
            [
                'status' => '1',
                'end_time' => Carbon::now()->unix(),
                'item' => $count
            ]);

        }  catch (\Exception $e) {
            if (CRUDBooster::myId()) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage()), 'error');
            }
            if($shop){
                DB::table('api_order_sync_reports')->updateOrInsert([
                    'shop_id' => $shop->id,
                ],
                [
                    'status' => '2',
                    'end_time' => Carbon::now()->unix(),
                    'item' => $count,
                    'report' => 'Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage(),
                ]);
                echo 'Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage()."\n";
                $shop_user = User::find($shop->user_id);
                if($shop_user) $shop_user->notify(new DRMNotification('Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage(), 'SHOP_VALIDATION_ERROR', '#'));
            }else{
                echo $e->getMessage()."\n";
            }
        }
    }

    public function bulkOrderInsertGambio($allOrder, $shop, $cartsInfo)
    {
        echo "here :" . date('H:m:s') . "\n";
        // dd($allOrder,$cartsInfo);

        $shopId = $shop->id;
        $shop_name = $shop->shop_name;
        $userId = CRUDBooster::myId();

        $sync_date = (DB::table('drm_order_sync')->where([
            'shop_id' => $shop->id,
            'drm_user_id' => $shop->user_id
        ])->first())->last_date;

        $l_date = $sync_date;
        if (count((array) $allOrder) > 0) {
            // dd($allOrder);

            foreach ($allOrder as $key => $value) {
                $customer_id = null;
                $order_info = [];

                $new = new DateTime($value->purchaseDate);
                $old = new DateTime($sync_date);

                if ($sync_date != null) {
                    if ($old >= $new) {
                        continue;
                    }
                }
                $l_date = $value->purchaseDate;

                $exist_shop_id = DB::table('drm_orders_new')->where('cms_user_id', $shop->user_id)->where('order_id_api', $value->id)->first();
                if($exist_shop_id) continue;

                // ---------- customer insert -------------

                list($total_sum, $currency) = explode(" ", $value->totalSum);

                $country = $value->deliveryAddress->country ?? $value->billingAddress->country ??  $value->deliveryAddress->countryIsoCode ?? $value->billingAddress->countryIsoCode;

                $customer_info = $order_info = null;

                $customer_info = [
                    "customer_full_name" => $value->customerName ?? $value->deliveryAddress->firstName . ' ' . $value->deliveryAddress->lastName,
                    "company_name" =>  $value->deliveryAddress->company ?? $value->billingAddress->company,
                    "currency" => $currency,
                    'email' => $value->customerEmail,
                    'address' =>  $value->deliveryAddress->additionalAddressInfo ?? $value->billingAddress->additionalAddressInfo,
                    'country' => $country,
                    'default_language' => $value->deliveryAddress->countryIsoCode ?? $value->billingAddress->countryIsoCode,
                    'zip_code' => $value->deliveryAddress->postcode ?? $value->billingAddress->postcode,
                    'state' => $value->deliveryAddress->state ??  $value->billingAddress->state,
                    'insert_type' => 'API',

                    //shipping
                    'street_shipping' => $value->deliveryAddress->street . ' ' . $value->deliveryAddress->houseNumber,
                    'city_shipping' => $value->deliveryAddress->city,
                    'state_shipping' => $value->deliveryAddress->state,
                    'zipcode_shipping' => $value->deliveryAddress->postcode,
                    'country_shipping' => $value->deliveryAddress->country ?? $value->billingAddress->countryIsoCode,

                    //billing
                    'street_billing' => $value->billingAddress->street . ' ' . $value->billingAddress->houseNumber,
                    'city_billing' => $value->billingAddress->city,
                    'state_billing' => $value->billingAddress->state,
                    'zipcode_billing' => $value->billingAddress->postcode,
                    'country_billing' => $value->billingAddress->country ?? $value->billingAddress->countryIsoCode,

                    'user_id' => $shop->user_id,
                ];

                $exist_customer_id = DB::table('drm_customers')->where('user_id', $shop->user_id)->where('email', $customer_info['email'])->first();
                if($exist_customer_id) continue;

                $customer_id = app('App\Http\Controllers\AdminDrmCustomersController')->add_customer($customer_info);



                $order_info['drm_customer_id'] = $customer_id;
                $order_info['user_id'] = $shop->user_id;

                // $date=date_create("2013-03-15");
                $order_info['order_date'] =  $value->purchaseDate;
                $order_info['insert_type'] = "API";
                $order_info['total'] = $total_sum;
                $order_info['shop_id'] = $shop->id;
                $order_info['order_id_api'] = $value->id;
                // $order_info['shipping'] =

                $order_info['sub_total'] = $total_sum;
                $order_info['discount'] = 0;
                $order_info['discount_type'] = "fixed";
                $order_info['adjustment'] = 0;
                $order_info['payment_type'] = $value->shippingType->module;
                $order_info['currency'] = $currency;

                $order_info['customer_info'] = $customer_info['customer_full_name'] . '<br>' . $customer_info['company_name'] . '<br>' . $customer_info['address'] . '<br>' . $customer_info['zip_code'] . ' ' . $customer_info['city'] . '<br>' . $customer_info['state'] . '<br>' . $customer_info['country'];


                $bill['b_street']       = $customer_info['street_billing']  . '<br>';
                $bill['b_postcode']       = $customer_info['city_billing'];
                $bill['b_state']       = $customer_info['state_billing']  . '<br>';
                $bill['b_city']        = $customer_info['zipcode_billing']  . '<br>';
                $bill['b_country'] = $customer_info['country_billing'];

                $order_info['billing'] = $bill['b_street'] .  $bill['b_state'] . $bill['b_postcode'] . ' ' . $bill['b_city'] . $bill['b_country'];

                $ship['d_street']       = $customer_info['street_shipping']  . '<br>';
                $ship['d_state']       = $customer_info['city_shipping'] . '<br>';
                $ship['d_postcode']       = $customer_info['zipcode_shipping'];
                $ship['d_city']        = $customer_info['city_shipping'] . '<br>';
                $ship['d_country'] = $customer_info['country_shipping'];

                $order_info['shipping'] = $ship['d_street'] .  $ship['d_state'] . $ship['d_postcode'] . ' ' . $ship['d_city'] . $ship['d_country'];

                $order_info['status'] = $value->statusName;
                $order_info['cart'] = '[{"cart":' . json_encode($cartsInfo[$key]) . "}]";

                foreach ((array) $cartsInfo[$key] as $item) {

                    $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item->name);
                    $order_info['description'][] =  iconv('UTF-8', 'ASCII//TRANSLIT', $item->model);
                    $order_info['qty'][] = $item->quantity;
                    $order_info['rate'][] = $item->price;
                    $order_info['unit'][] = $item->quantityUnitName;
                    $order_info['tax'][] = $item->tax;
                    $order_info['product_discount'][] = $item->discount ?? 0;
                    $order_info['amount'][] = $item->finalPrice;
                }

                // ----------------------- Order Insert ---------------------
                app('App\Http\Controllers\AdminDrmOrdersController')->add_order($order_info);
            }/* foreach end */

            DB::table('drm_order_sync')->updateOrInsert([
                'shop_id' => $shop->id,
                'drm_user_id' => $shop->user_id
            ], [
                'last_date' => $l_date
            ]);

            // dd($od);
        }
    }

    public function syncLengowOrder($shop, $page = 1, &$count = 0)
    {
        try{
            if (!$shop) {
                echo "You have not configured any Lengow shop yet!";
                throw new \Exception("You have not configured any Lengow shop yet!");
            }

            print_r("1. Shop id: $shop->id " . "\n");

            $access_token = $shop->username;
            $secret   = $shop->password;
            $lengow = new LengowApi($access_token, $secret);

            if ($lengow->token == "") {
                throw new \Exception("$shop->shop_name shop token problem!");
            }

            $all = $lengow->getOrder($page);

            if ($all) {
                $allorder = $all->results;

                print_r("Shop id: $shop->id " . "\n");
                $this->bulkOrderInsertLengow($allorder, $shop);

                if ($all->next) {
                    $page++;
                    $count += count($allorder);
                    $this->syncLengowOrder($shop, $page, $count);
                }
            }

            DB::table('api_order_sync_reports')->updateOrInsert([
                'shop_id' => $shop->id,
            ],
            [
                'status' => '1',
                'end_time' => Carbon::now()->unix(),
                'item' => $count
            ]);

        }  catch (\Exception $e) {
            if (CRUDBooster::myId()) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage()), 'error');
            }
            if($shop){
                DB::table('api_order_sync_reports')->updateOrInsert([
                    'shop_id' => $shop->id,
                ],
                [
                    'status' => '2',
                    'end_time' => Carbon::now()->unix(),
                    'item' => $count,
                    'report' => 'Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage(),
                ]);
                echo 'Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage()."\n";
                $shop_user = User::find($shop->user_id);
                if($shop_user) $shop_user->notify(new DRMNotification('Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage(), 'SHOP_VALIDATION_ERROR', '#'));
            }else{
                echo $e->getMessage()."\n";
            }
        }
    }

    public function bulkOrderInsertLengow($allorder, $shop)
    {

        print_r("Shop id: $shop->id Count: " . count($allorder) . "\n");
        // dd($allorder[0],$allorder[1]);
        // dd($shop);

        $shopId = $shop->id;
        $shop_name = $shop->shop_name;
        $userId   = $shop->user_id;
        $base_url = $shop->url;

        $sync_date = (DB::table('drm_order_sync')->where([
            'shop_id' => $shop->id,
            'drm_user_id' => $shop->user_id
        ])->first())->last_date;

        // $sync_count = 0;


        $l_date = $sync_date;
        foreach ((object) $allorder as $key => $value) {

            // $this->stat['order'] ++;

            $name = $value->billing_address->full_name . $value->billing_address->first_name . $value->billing_address->last_name . $value->packages[0]->delivery->city;

            if (strtolower(substr($name, 0, 3)) == 'xxx') {
                // $this->stat['details'][] =$name;

                continue;
            }

            $new = new DateTime($value->marketplace_order_date);
            $old = new DateTime($sync_date);

            if ($sync_date != null) {
                if ($old >= $new) {
                    continue;
                }
            }

            $exist_shop_id = DB::table('drm_orders_new')->where('cms_user_id', $shop->user_id)->where('order_id_api', $value->marketplace_order_id)->first();
            if($exist_shop_id) continue;


            $l_date = $value->marketplace_order_date;

            $customer_info = $order_info = null;

            $customer_info = [
                'customer_full_name' => $value->billing_address->full_name ?? $value->billing_address->first_name . " " . $value->billing_address->last_name,
                'company_name' => $value->packages[0]->delivery->company ?? $value->billing_address->company,
                'email' =>  $value->billing_address->email,
                'city' => $value->packages[0]->delivery->city ?? $value->billing_address->city,
                'zip_code' => $value->packages[0]->delivery->zipcode ?? $value->billing_address->zipcode,
                'state' =>  $value->packages[0]->delivery->state_region ?? $value->billing_address->state_region,
                'country' => $value->packages[0]->delivery->common_country_iso_a2 ?? $value->billing_address->common_country_iso_a2,
                'phone' =>  $value->packages[0]->delivery->phone_mobile ?? $value->billing_address->phone_mobile,
                // 'website' => ,
                'currency' => $value->original_currency->iso_a3,
                //  'default_language' => ,
                'address' => $value->contact_address ?? $value->billing_address->full_address ?? $value->packages[0]->delivery->full_address,
                'insert_type' => "API",
                'user_id' => $shop->user_id,
                //  'vat_number' => ,

                // shipping
                'street_shipping' => $value->packages[0]->delivery->first_line,
                'city_shipping' => $value->packages[0]->delivery->city,
                'state_shipping' => $value->packages[0]->delivery->state_region,
                'zipcode_shipping' => $value->packages[0]->delivery->zipcode,
                'country_shipping' => $value->packages[0]->delivery->common_country_iso_a2,

                //billing
                'street_billing' => $value->billing_address->first_line,
                'city_billing' => $value->billing_address->city,
                'state_billing' => $value->billing_address->state_region,
                'zipcode_billing' => $value->billing_address->zipcode,
                'country_billing' =>  $value->billing_address->common_country_iso_a2,

            ];

            if (isset($value->billing_address)) {
                $exist_customer_id = DB::table('drm_customers')->where('user_id', $shop->user_id)->where('email', $customer_info['email'])->first();
                if($exist_customer_id) continue;
                // insert customer
                $customer_id = app('App\Http\Controllers\AdminDrmCustomersController')->add_customer($customer_info);
            }
            // dd($customer_id);


            // ----------------------- order ----------------------------

            $order_info['user_id'] = $shop->user_id;
            $order_info['drm_customer_id'] = $customer_id;
            // $date=date_create("2013-03-15");
            $order_info['order_date'] =  $value->marketplace_order_date;
            $order_info['insert_type'] = "API";
            $order_info['total'] = $value->total_order;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $value->marketplace_order_id;

            $order_info['sub_total'] = $value->original_total_order;
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $value->payments[0]->type;
            $order_info['currency'] = $value->original_currency->iso_a3;
            $order_info['shipping_cost'] = $value->shipping;

            $order_info['customer_info'] = $customer_info['customer_full_name'] . '<br>' . $customer_info['company_name'] . '<br>' . $customer_info['address'] . '<br>' . $customer_info['zip_code'] . ' ' . $customer_info['city'] . '<br>' . $customer_info['state'] . '<br>' . $customer_info['country'];


            $bill['b_street']       = $value->billing_address->first_line . '<br>';
            // $bill['b_house_number'] = "N/A";
            $bill['b_postcode']       = $value->billing_address->zipcode;
            $bill['b_state']       = $value->billing_address->state_region . '<br>';
            $bill['b_city']        = $value->billing_address->city . '<br>';
            $bill['b_country'] = $value->billing_address->common_country_iso_a2;

            $order_info['billing'] = $bill['b_street'] .  $bill['b_state'] . $bill['b_postcode'] . ' ' . $bill['b_city'] . $bill['b_country'];

            $ship['d_street']       = $value->packages[0]->delivery->first_line  . '<br>';
            $ship['d_state']       = $value->packages[0]->delivery->state_region . '<br>';
            $ship['d_postcode']       = $value->packages[0]->delivery->zipcode;
            $ship['d_city']        = $value->packages[0]->delivery->city . '<br>';
            $ship['d_country'] = $value->packages[0]->delivery->common_country_iso_a2;

            $order_info['shipping'] = $ship['d_street'] .  $ship['d_state'] . $ship['d_postcode'] . ' ' . $ship['d_city'] . $ship['d_country'];

            // $order_info['client_note'];
            $order_info['status'] = $value->lengow_status;
            $order_info['cart'] = json_encode($value->packages);

            foreach ((array) $value->packages[0]->cart as $j => $item) {
                $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item->title);
                $order_info['description'][] =  iconv('UTF-8', 'ASCII//TRANSLIT', $item->category);
                $order_info['qty'][] = $item->quantity;
                $order_info['rate'][] = $item->amount;
                $order_info['tax'][] = $item->tax;
                $order_info['image'][] = $item->url_image;
                $order_info['product_discount'][] = $item->discount ?? 0;
                $order_info['amount'][] = $item->amount;
            }


            // ----------------------- Order Insert ---------------------
            app('App\Http\Controllers\AdminDrmOrdersController')->add_order($order_info);
        }/* end foreach */

        DB::table('drm_order_sync')->updateOrInsert([
            'shop_id' => $shop->id,
            'drm_user_id' => $shop->user_id
        ], [
            'last_date' => $l_date
        ]);

        // dd($od);

    }


    function syncYategoOrder($shop)
    {
        $count = 0;
        try{
            if (!$shop) {
                throw new \Exception("You have not configured any Yatego shop yet!");
            }
            echo "at yatego shop id $shop->id\n";


            $order_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $shop->username . "&passwd=" . $shop->password . "&action=csv_order";
            // $csv = array_map('str_getcsv', file($url));
            // dd(file($url));

            $order_content = @file_get_contents($order_url);
            if (!$order_content) {
                throw new \Exception("Can not access url or no order found!");
            }
            // dd($content);

            $putted_orders = @file_put_contents(storage_path() . "/tempOrder.csv", $order_content);
            if (!$putted_orders) {
                echo "Content can not be putted to file " . storage_path() . "/tempOrder.csv";
                throw new \Exception("Content can not be putted to file " . storage_path() . "/tempOrder.csv");
            }
            // dd(realpath('storage/tempOrder.csv'));
            // dd($csv);
            $l_date = null;

            $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
            $reader->setInputEncoding('UTF-8');
            $reader->setDelimiter(';');
            $spreadsheet = $reader->load(realpath(storage_path() . "/tempOrder.csv"));

            // dd($spreadsheet->getActiveSheet()->toArray());
            $order_arr = $spreadsheet->getActiveSheet()->toArray();
            $order_columns = $order_arr[0];
            unset($order_arr[0]);
            // dd($order_arr);

            if (count($order_arr)) {

                $order_id_from = $order_arr[1][1];
                $order_id_to = $order_arr[count($order_arr)][1];
                // dd($order_id_from,$order_id_to);

                $order_product_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $shop->username . "&passwd=" . $shop->password . "&action=csv_products&von=" . $order_id_from . "&bis=" . $order_id_to . "&varids=1";

                $product_content = @file_get_contents($order_product_url);
                // dd($content);

                if (!$product_content) {
                    throw new \Exception('Can not access Product url. Please contact admin. ');
                }

                $putted = @file_put_contents(storage_path() . "/tempOrderProduct.csv", $product_content);
                if (!$putted) {
                    echo "Content can not be putted to file " . storage_path() . "/tempOrderProduct.csv";
                    throw new \Exception("Content can not be putted to file " . storage_path() . "/tempOrderProduct.csv");
                }

                // dd(realpath('storage/tempOrder.csv'));
                // dd($csv);

                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
                $reader->setInputEncoding('UTF-8');
                $reader->setDelimiter(';');
                $spreadsheet = $reader->load(realpath(storage_path() . "/tempOrderProduct.csv"));

                // dd($spreadsheet->getActiveSheet()->toArray());
                $product_arr = $spreadsheet->getActiveSheet()->toArray();
                // dd($product_arr);
                $product_columns = $product_arr[0];
                unset($product_arr[0]);
                // dd($product_columns, $product_arr);
                $product_arr_new = [];
                foreach ($product_arr as $item) {
                    $product = @array_combine($product_columns, $item);

                    if (!$product) {
                        throw new \Exception('Error in product details. Please contact admin.');
                    }

                    $product_arr_new[] = $product;
                }

                // $product_arr_new[] = $product;

                $product_collection = collect($product_arr_new);

                // dd($product_collection);

                foreach ($order_arr as $item) {

                    $order = @array_combine($order_columns, $item);
                    // $order_arr = @array_combine($columns,$product_arr[2]);

                    if ($order) {
                        // dd();
                        $products = $product_collection->where('Bestellnummer', $order['Bestellnummer'])->toArray();

                        if(count($products)){
                            $l_date = $this->orderInsertYatego($shop, $order, $products);
                            $count += count($products);
                        }

                    } else {
                        throw new \Exception('Shop Setting Changed. Please contact admin.');
                    }
                }

            }else{
                if (CRUDBooster::myId()) {
                    CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('No order found. '), 'error');
                }
            }

            DB::table('api_order_sync_reports')->updateOrInsert([
                'shop_id' => $shop->id,
            ],
            [
                'status' => '1',
                'end_time' => Carbon::now()->unix(),
                'item' => $count
            ]);

        }  catch (\Exception $e) {
            if (CRUDBooster::myId()) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage()), 'error');
            }
            if($shop){
                DB::table('api_order_sync_reports')->updateOrInsert([
                    'shop_id' => $shop->id,
                ],
                [
                    'status' => '2',
                    'end_time' => Carbon::now()->unix(),
                    'item' => $count,
                    'report' => 'Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage(),
                ]);
                echo 'Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage()."\n";
                $shop_user = User::find($shop->user_id);
                if($shop_user) $shop_user->notify(new DRMNotification('Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage(), 'SHOP_VALIDATION_ERROR', '#'));
            }else{
                echo $e->getMessage()."\n";
            }
        }
        //  dd($shop);

    }

    public function orderInsertYatego($shop, $order, $products)
    {
        echo "inserting at yatego\n";

        $sync_date = (DB::table('drm_order_sync')->where([
            'shop_id' => $shop->id,
            'drm_user_id' => CRUDBooster::myId() ?? $shop->user_id
        ])->first())->last_date;

        // customer
        $new = new DateTime($order['Bestelldatum']);
        $old = new DateTime($sync_date);

        if ($sync_date != null && $old >= $new) {
            return;
        }

        $exist_shop_id = DB::table('drm_orders_new')->where('cms_user_id', $shop->user_id)->where('order_id_api', $order['Order_ID'])->first();
        if($exist_shop_id) return;

        $l_date = $order['Bestelldatum'];

        $customer_info = $order_info = null;

        $customer_info = [
            'customer_full_name' => $order["R_Vorname"] . " " . $order["R_Nachname"],
            'company_name' => $order["R_Firma"],
            'email' =>  $order["E-Mail-Adresse"],
            'city' => $order["R_Stadt"],
            'zip_code' => $order["R_PLZ"],
            // 'state' =>  $order["R_Stadt"] ,
            'country' => $order["R_Land"],
            'phone' =>  $order["R_Telefon"],
            // 'website' => ,
            // 'currency' => ,
            //  'default_language' => ,
            'insert_type' => "API",
            'user_id' => $shop->user_id,
            //  'vat_number' => ,

            // shipping
            'street_shipping' => $order["L_Strasse"],
            'city_shipping' => $order["L_Stadt"],
            // 'state_shipping' => ,
            'zipcode_shipping' => $order["L_PLZ"],
            'country_shipping' => $order["L_Land"],

            //billing
            'street_billing' =>  $order["R_Strasse"],
            'city_billing' => $order["R_Stadt"],
            // 'state_billing' =>  ,
            'zipcode_billing' =>  $order["R_PLZ"],
            'country_billing' =>   $order["R_Land"],

        ];

        $exist_customer_id = DB::table('drm_customers')->where('user_id', $shop->user_id)->where('email', $customer_info['email'])->first();
        if($exist_customer_id) return;
        $customer_id = app('App\Http\Controllers\AdminDrmCustomersController')->add_customer($customer_info);
        // -----------------------

        // order
        $order_info['drm_customer_id'] = $customer_id;
        $order_info['user_id'] = $shop->user_id;
        // $date=date_create("2013-03-15");
        $order_info['order_date'] = $order["Bestelldatum"];
        $order_info['insert_type'] = "API";
        $order_info['total'] = $order['Gesamtumsatz'];
        $order_info['shop_id'] = $shop->id;
        $order_info['order_id_api'] = $order['Order_ID'];

        $order_info['sub_total'] = $order['Gesamtumsatz'];
        $order_info['discount'] = $order['Bestellwertrabatt'];
        $order_info['discount_type'] = "fixed";
        $order_info['adjustment'] = 0;
        $order_info['payment_type'] = $order['Zahlart'];
        // $order_info['currency'] = $order['zzzz'];

        $order_info['customer_info'] = $customer_info['customer_full_name'] . '<br>'
            . $customer_info['company_name'] . '<br>'
            . $customer_info['address'] . '<br>'
            . $customer_info['zip_code'] . ' ' . $customer_info['city'] . '<br>'
            . $customer_info['state'] . '<br>'
            . $customer_info['country'];

        $order_info['billing'] = $customer_info['street_billing'] . '<br>'
            . $customer_info['zipcode_billing'] . ' ' . $customer_info['city_billing'] . '<br>'
            . $customer_info['country_billing'];

        $order_info['shipping'] = $customer_info['street_shipping'] . '<br>'
            . $customer_info['zipcode_shipping'] . ' ' . $customer_info['city_billing'] . '<br>'
            . $customer_info['country_shipping'];

        // $order_info['client_note'];
        // $order_info['status'] =$order['zzzz'];

        // dd($customer_info,$order_info);

        foreach ((object) $products as $item) {

            // dd($item);
            $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item['Produktname']);
            $order_info['description'][] =  iconv('UTF-8', 'ASCII//TRANSLIT', $item['Artikelnummer']);
            $order_info['qty'][] = $item['Anzahl'];
            $order_info['rate'][] = $item['Einzelpreis'];
            $order_info['tax'][] = $item['Steuer'];
            $order_info['product_discount'][] = $item['Mengenrabatt'] ?? 0;
            $order_info['amount'][] = $item['Gesamtpreis'];
        }

        // order add
        app('App\Http\Controllers\AdminDrmOrdersController')->add_order($order_info);

        if ($l_date) {
            DB::table('drm_order_sync')->updateOrInsert([
                'shop_id' => $shop->id,
                'drm_user_id' => CRUDBooster::myId() ?? $shop->user_id
            ], [
                'last_date' => $l_date
            ]);
        }
    }


    /* Ebay Orders  */

    public function syncEbayOrder($shop)
    {
        $count = 0;
        try{
            if($shop){
                $ebay_service = new EbayServices();
                $service = $ebay_service->createTrading();
                $request = new Types\GetOrdersRequestType();
                $request->RequesterCredentials = new Types\CustomSecurityHeaderType();
                $request->RequesterCredentials->eBayAuthToken = $shop->password;
                $request->CreateTimeFrom = \DateTime::createFromFormat('Y-m-d H:i:s', '2019-02-01 18:33:00');
                $request->CreateTimeTo = \DateTime::createFromFormat('Y-m-d H:i:s', now());
                $request->OrderStatus = 'All';
                $response = $service->getOrders($request);

                if(json_decode($response,true)['Ack'] == 'Success'){
                    $response = (object)(json_decode($response,true)['OrderArray']);
                    $all_orders = $response->Order;

                    if($all_orders){
                        $count += count($all_orders);
                        $this->bulkOrderInsertEbay($shop,$all_orders);
                    }else{
                        if (CRUDBooster::myId()) {
                            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Order Empty!'), 'error');
                        }
                    }
                }else{
                    throw new \Exception('Authentication error!');
                }
            }else{
                throw new \Exception('You have not configured any Ebay shop!');
            }

            DB::table('api_order_sync_reports')->updateOrInsert([
                'shop_id' => $shop->id,
            ],
            [
                'status' => '1',
                'end_time' => Carbon::now()->unix(),
                'item' => $count
            ]);

        }  catch (\Exception $e) {
            if (CRUDBooster::myId()) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage()), 'error');
            }
            if($shop){
                DB::table('api_order_sync_reports')->updateOrInsert([
                    'shop_id' => $shop->id,
                ],
                [
                    'status' => '2',
                    'end_time' => Carbon::now()->unix(),
                    'item' => $count,
                    'report' => 'Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage()
                ]);
                echo 'Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage()."\n";
                $shop_user = User::find($shop->user_id);
                if($shop_user) $shop_user->notify(new DRMNotification('Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage(), 'SHOP_VALIDATION_ERROR', '#'));
            }else{
                echo $e->getMessage()."\n";
            }
        }

    }

    public function bulkOrderInsertEbay($shop,$all_orders){

        $last_sync_date = (DB::table('drm_order_sync')->where(['shop_id' => $shop->id,'drm_user_id' => $shop->user_id])->first())->last_date;

        $last_date = $last_sync_date;

        foreach ($all_orders as $order) {
            $new = new DateTime($order['CreatedTime']);
            $old = new DateTime($last_sync_date);

            if ($last_sync_date != null && $old >= $new) {
                continue;
            }

            $exist_shop_id = DB::table('drm_orders_new')->where('cms_user_id', $shop->user_id)->where('order_id_api', $order['OrderID'])->first();
            if($exist_shop_id) continue;

            $customer_info = $order_info = null;

            $customer_info = [
                'customer_full_name' => $order['TransactionArray']['Transaction'][0]['Buyer']['UserFirstName'] . ' ' . $order['TransactionArray']['Transaction'][0]['Buyer']['UserLastName'] ,
                'company_name' => '',
                'email' =>  $order['TransactionArray']['Transaction'][0]['Buyer']['Email'],
                'city' => $order['ShippingAddress']['CityName'],
                'zip_code' => $order['ShippingAddress']['PostalCode'],
                'state' => $order['ShippingAddress']['StateOrProvince'],
                'country' => $order['ShippingAddress']['CountryName'],
                'phone' =>  $order['ShippingAddress']['Phone'],

                'currency' => $order['AmountPaid']['currencyID'],
                'address' => '',
                'insert_type' => "API",
                'user_id' => $shop->user_id,

                // shipping
                'street_shipping' => $order['ShippingAddress']['Street1'] ?? $order['ShippingAddress']['Street2'],
                'city_shipping' => $order['ShippingAddress']['CityName'],
                'state_shipping' => $order['ShippingAddress']['StateOrProvince'],
                'zipcode_shipping' => $order['ShippingAddress']['PostalCode'],
                'country_shipping' => $order['ShippingAddress']['CountryName'],

                //billing
                'street_billing' => $order['ShippingAddress']['Street1'] ?? $order['ShippingAddress']['Street2'],
                'city_billing' => $order['ShippingAddress']['CityName'],
                'state_billing' => $order['ShippingAddress']['StateOrProvince'],
                'zipcode_billing' => $order['ShippingAddress']['PostalCode'],
                'country_billing' =>  $order['ShippingAddress']['CountryName'],
            ];

            if (isset($order['TransactionArray']['Transaction'][0]['Buyer'])) {
                $exist_customer_id = DB::table('drm_customers')->where('user_id', $shop->user_id)->where('email', $customer_info['email'])->first();
                if($exist_customer_id) continue;
                $customer_id = app('App\Http\Controllers\AdminDrmCustomersController')->add_customer($customer_info);
            }

            // ----------------------- order ----------------------------


            $order_info['user_id'] = $shop->user_id;

            $order_info['drm_customer_id'] = $customer_id;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] =  $order['OrderID'];
            $order_info['order_date'] =  $order['CreatedTime'];
            $last_date = $order_info['order_date'];
            $order_info['insert_type'] = "API";
            $order_info['total'] =  $order['Total']['value'];
            $order_info['sub_total'] = $order['Subtotal']['value'];
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $order['CheckoutStatus']['PaymentMethod'];
            $order_info['currency'] = $order['Total']['currencyID'];

            $order_info['customer_info'] = $customer_info['customer_full_name'] . '<br>'
                . $customer_info['company_name'] . '<br>'
                . $customer_info['address'] . '<br>'
                . $customer_info['zip_code'] . ' ' . $customer_info['city'] . '<br>'
                . $customer_info['state'] . '<br>'
                . $customer_info['country'];

            $order_info['billing'] = $customer_info['street_billing'] . '<br>'
                . $customer_info['zipcode_billing'] . ' ' . $customer_info['city_billing'] . '<br>'
                . $customer_info['country_billing'];

            $order_info['shipping'] = $customer_info['street_shipping'] . '<br>'
                . $customer_info['zipcode_shipping'] . ' ' . $customer_info['city_billing'] . '<br>'
                . $customer_info['country_shipping'];

            $order_info['status'] = $order['OrderStatus'];

            foreach ($order['TransactionArray']['Transaction'] as $item) {
                $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item['Item']['Title']);
                $order_info['description'][] =  iconv('UTF-8', 'ASCII//TRANSLIT', $item['Item']['Title']);
                $order_info['qty'][] = $item['QuantityPurchased'];
                $order_info['rate'][] = $item['TransactionPrice']['value'];
                $order_info['tax'][] = $item['Taxes']['TotalTaxAmount']['value'];
                $order_info['product_discount'][] = 0;
                $order_info['amount'][] = $item['QuantityPurchased'] * $item['TransactionPrice']['value'];
            }

            app('App\Http\Controllers\AdminDrmOrdersController')->add_order($order_info);

        }

        if ($last_date){
            DB::table('drm_order_sync')->updateOrInsert(['shop_id' => $shop->id,'drm_user_id' => $shop->user_id], [
                'last_date' => $last_date
            ]);
        }

    }


    // Shopify Orders
    public function syncShopifyOrder($shop)
    {
        $count = 0;
        try{
            if($shop){
                echo "at Shopify shop id $shop->id \n";
                $url = $shop->url . 'admin/api/2020-01/orders.json?status=any&fulfillment_status=any';
                // $url = $shop_details->url . 'admin/api/2020-01/draft_orders.json';
                $client = new \GuzzleHttp\Client();
                $response = $client->request('GET', $url, [
                    'auth' => [$shop->username, $shop->password]
                ]);

                if($response->getStatusCode() !== 200){
                    throw new \Exception('Connection problem!');
                }

                $data = $response->getBody()->getContents();
                $all_orders = (json_decode($data))->orders;

                if($all_orders){
                    $count += count((array) $all_orders);
                    $this->bulkorderInsertShopify($shop, $all_orders);
                }
            }else{
                throw new \Exception('You have not configured shop!');
            }

            DB::table('api_order_sync_reports')->updateOrInsert([
                'shop_id' => $shop->id,
            ],
            [
                'status' => '1',
                'end_time' => Carbon::now()->unix(),
                'item' => $count
            ]);

        }  catch (\Exception $e) {

            if (CRUDBooster::myId()) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage()), 'error');
            }
            if($shop){
                DB::table('api_order_sync_reports')->updateOrInsert([
                    'shop_id' => $shop->id,
                ],
                [
                    'status' => '2',
                    'end_time' => Carbon::now()->unix(),
                    'item' => $count,
                    'report' => 'Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage()
                ]);
                echo 'Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage()."\n";
                $shop_user = User::find($shop->user_id);
                if($shop_user) $shop_user->notify(new DRMNotification('Shop: '.$shop->shop_name. ' - Error: '.$e->getMessage(), 'SHOP_VALIDATION_ERROR', '#'));
            }else{
                echo $e->getMessage()."\n";
            }
        }
    }

    public function bulkorderInsertShopify($shop, $all_orders)
    {
        echo "inserting at Shopify\n";
        $sync_date = (DB::table('drm_order_sync')->where([
            'shop_id' => $shop->id,
            'drm_user_id' => $shop->user_id
        ])->first())->last_date;

        $l_date = $sync_date;
        // dd($all_orders);
        foreach ((object) $all_orders as $value) {
            // dd($value);
            // dd($value->billing_address->city);

            $exist_shop_id = DB::table('drm_orders_new')->where('cms_user_id', $shop->user_id)->where('order_id_api', $value->id)->first();
            if($exist_shop_id) continue;

            $new = new DateTime($value->created_at);
            $old = new DateTime($sync_date);

            if ($sync_date != null) {
                if ($old >= $new) {
                    continue;
                }
            }

            $customer_info = $order_info = null;
            $customer_info = [
                'customer_full_name' => $value->customer->default_address->name,
                'company_name' => $value->customer->default_address->company,
                'email' =>  $value->customer->email,
                'city' => $value->customer->default_address->city,
                'zip_code' => $value->customer->default_address->zip,
                'state' =>  $value->customer->default_address->province,
                'country' => $value->customer->default_address->country_name,
                'phone' =>  $value->customer->default_address->phone,
                // 'website' => ,
                'currency' => $value->customer->currency,
                //  'default_language' => ,
                'address' => $value->customer->default_address->address1,
                'insert_type' => "API",
                'user_id' => $shop->user_id,
                //  'vat_number' => ,

                // shipping
                'street_shipping' => $value->shipping_address->address1,
                'city_shipping' => $value->shipping_address->city,
                'state_shipping' => $value->shipping_address->province,
                'zipcode_shipping' => $value->shipping_address->zip,
                'country_shipping' => $value->shipping_address->country,

                //billing
                'street_billing' => $value->billing_address->address1,
                'city_billing' => $value->billing_address->city,
                'state_billing' => $value->billing_address->province,
                'zipcode_billing' => $value->billing_address->zip,
                'country_billing' =>  $value->billing_address->country,

            ];



            if (isset($value->customer)) {
                $exist_customer_id = DB::table('drm_customers')->where('user_id', $shop->user_id)->where('email', $customer_info['email'])->first();
                if($exist_customer_id) continue;
                // insert customer
                $customer_id = app('App\Http\Controllers\AdminDrmCustomersController')->add_customer($customer_info);
            }
            // dd($customer_id);


            // ----------------------- order ----------------------------


            $order_info['user_id'] = $shop->user_id;

            $order_info['drm_customer_id'] = $customer_id;
            // $date=date_create("2013-03-15");
            $order_info['order_date'] =  $value->created_at;

            $l_date = $order_info['order_date'];

            $order_info['insert_type'] = "API";
            $order_info['total'] = $value->total_price;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $value->id;

            $order_info['sub_total'] = $value->subtotal_price;
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $value->gateway;
            $order_info['currency'] = $value->currency;

            $order_info['customer_info'] = $customer_info['customer_full_name'] . '<br>'
                . $customer_info['company_name'] . '<br>'
                . $customer_info['address'] . '<br>'
                . $customer_info['zip_code'] . ' ' . $customer_info['city'] . '<br>'
                . $customer_info['state'] . '<br>'
                . $customer_info['country'];

            $order_info['billing'] = $customer_info['street_billing'] . '<br>'
                . $customer_info['zipcode_billing'] . ' ' . $customer_info['city_billing'] . '<br>'
                . $customer_info['country_billing'];

            $order_info['shipping'] = $customer_info['street_shipping'] . '<br>'
                . $customer_info['zipcode_shipping'] . ' ' . $customer_info['city_billing'] . '<br>'
                . $customer_info['country_shipping'];

            // $order_info['client_note'];
            $order_info['status'] = $value->financial_status;
            $order_info['cart'] = json_encode($value->line_items);

            foreach ((object) $value->line_items as $item) {

                $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item->name);
                $order_info['description'][] =  iconv('UTF-8', 'ASCII//TRANSLIT', $item->title);
                $order_info['qty'][] = $item->quantity;
                $order_info['rate'][] = $item->price;
                $order_info['tax'][] = $item->tax;
                $order_info['product_discount'][] = $item->total_discount ?? 0;
                $order_info['amount'][] = $item->quantity * $item->price;
            }


            // ----------------------- Order Insert ---------------------
            app('App\Http\Controllers\AdminDrmOrdersController')->add_order($order_info);
        }

        if ($l_date){
            DB::table('drm_order_sync')->updateOrInsert([
                'shop_id' => $shop->id,
                'drm_user_id' => $shop->user_id
            ], [
                'last_date' => $l_date
            ]);
        }

    }


}

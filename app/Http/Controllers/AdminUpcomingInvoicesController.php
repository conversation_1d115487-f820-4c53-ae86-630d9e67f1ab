<?php

namespace App\Http\Controllers;

use Session;
use DB;
use CRUDBooster;
use Illuminate\Support\Facades\Storage;
use File;
use ZipArchive;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Controllers\AdminDrmImportsController;
use Illuminate\Support\Facades\Cache;
use App\UpcomingInvoice;
use App\NewOrder;
use App\UpcommingInvoiceCategory;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Str;
use SoapBox\Formatter\Formatter;
use Illuminate\Support\Facades\Validator;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\LazyCollection;
use Toastr;
use AppStore;
use App\ShareableLink;
use App\Mail\DRMSEndMail;
use Illuminate\Support\Facades\Mail;
use App\Jobs\AccountingArchiveJob;
use App\Notifications\DRMNotification;
use App\Exports\InvoicesExport;

class AdminUpcomingInvoicesController extends \crocodicstudio\crudbooster\controllers\CBController
{

	public function cbInit()
	{

		# START CONFIGURATION DO NOT REMOVE THIS LINE
		$this->title_field = "invoice_number";
		$this->limit = "20";
		$this->orderby = "id,desc";
		$this->global_privilege = false;
		$this->button_table_action = true;
		$this->button_bulk_action = true;
		$this->button_action_style = "button_icon";
		$this->button_add = true;
		$this->button_edit = true;
		$this->button_delete = true;
		$this->button_detail = true;
		$this->button_show = true;
		$this->button_filter = true;
		$this->button_import = true;
		$this->button_export = false;
		$this->table = "upcoming_invoices";
		$this->table_statement = "statements";

		# END CONFIGURATION DO NOT REMOVE THIS LINE

		# START COLUMNS DO NOT REMOVE THIS LINE
		$this->col = [];
		$this->col[] = ["label" => "Supplier", "name" => "delivery_company_id", "join" => "delivery_companies,name"];
		$this->col[] = ["label" => "Invoice Number", "name" => "invoice_number"];
		$this->col[] = ["label" => "Date", "name" => "date"];
		// $this->col[] = ["label"=>"Due Date","name"=>"due_date"];
		$this->col[] = ["label" => "Category", "name" => "category"];
		$this->col[] = ["label" => "Status", "name" => "status"];
		# END COLUMNS DO NOT REMOVE THIS LINE

		# START FORM DO NOT REMOVE THIS LINE
		$this->form = [];
		$this->form[] = ['label' => 'Supplier', 'name' => 'delivery_company_id', 'type' => 'select2', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10', 'datatable' => 'delivery_companies,name'];
		$this->form[] = ['label' => 'Invoice Number', 'name' => 'invoice_number', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10'];
		$this->form[] = ['label' => 'Date', 'name' => 'date', 'type' => 'datetime', 'validation' => 'required|date_format:Y-m-d', 'width' => 'col-sm-10'];
		$this->form[] = ['label' => 'Due Date', 'name' => 'due_date', 'type' => 'datetime', 'validation' => 'date_format:Y-m-d', 'width' => 'col-sm-10'];
		// $this->form[] = ['label'=>'Type Of Issue','name'=>'type_of_issue','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
		$this->form[] = ['label' => 'Category', 'name' => 'category', 'type' => 'text', 'validation' => 'min:1|max:255', 'width' => 'col-sm-10'];
		$this->form[] = ['label' => 'Status', 'name' => 'status', 'type' => 'text', 'validation' => 'min:1|max:255', 'width' => 'col-sm-10'];
		$this->form[] = ['label' => 'Amount', 'name' => 'amount', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10'];
		$this->form[] = ['label' => 'Total Amount', 'name' => 'total_amount', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10'];
		$this->form[] = ['label' => 'Tax', 'name' => 'tax', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10'];
		$this->form[] = ['label' => 'Description', 'name' => 'description', 'type' => 'textarea', 'validation' => 'required|string|min:5|max:5000', 'width' => 'col-sm-10'];
		# END FORM DO NOT REMOVE THIS LINE

		/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
		$this->sub_module = array();


		/*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
		$this->addaction = array();


		/*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
		$this->button_selected = array();


		/*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
		$this->alert        = array();



		/*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
		$this->index_button = array();



		/*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
		$this->table_row_color = array();


		/*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
		$this->index_statistic = array();



		/*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
		$this->script_js = NULL;


		/*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
		$this->pre_index_html = null;



		/*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
		$this->post_index_html = null;



		/*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
		$this->load_js = array();



		/*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
		$this->style_css = NULL;



		/*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
		$this->load_css = array();
	}


	/*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	public function actionButtonSelected($id_selected, $button_name)
	{
		//Your code here

	}


	/*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	public function hook_query_index(&$query)
	{
		//Your code here

	}

	/*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	public function hook_row_index($column_index, &$column_value)
	{
		//Your code here
	}

	/*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	public function hook_before_add(&$postdata)
	{
		$postdata['user_id'] = CRUDBooster::myId();
		Validator::make(
			$_REQUEST,
			[
				'status' => 'required',
				'delivery_company_id' => 'required',
				'invoice_number' => 'required',
				'date' => 'required',
				'due_date' => 'nullable|required_if:status,unpaid',
				'tax' => 'required',
				'amount' => 'required',
				'file.*' => 'required|mimes:jpeg,bmp,png,gif,svg,pdf',
				'description' => 'nullable|string|min:5|max:5000',
			],
			[
				'delivery_company_id.required' => 'Supplier required',
			]
		)->validate();
		if ($postdata['tax']) {
			$postdata['tax'] = removeCommaPrice($postdata['tax']);
		}
		if ($postdata['amount']) {
			$postdata['amount'] = removeCommaPrice($postdata['amount']);
		}
	}

	/*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	public function hook_after_add($id)
	{


		$allowedfileExtension = ['pdf', 'jpg', 'png', 'jpeg', 'PDF', 'JPG', 'PNG', 'JPEG'];
		if (is_array(request()->file)) {
			foreach (request()->file as $file) {
				if (!in_array($file->getClientOriginalExtension(), $allowedfileExtension)) {
					continue;
				}
				$rand = Str::random(40);
				$extension = $file->extension();
				$path = $file->storeAs('storage/invoice_files', $rand . '.' . $extension, ['visibility' => 'public', 'disk' => 'spaces']);
				$file_path = Storage::disk('spaces')->url($path);
				DB::table('upcoming_invoice_documents')->insert([
					"upcoming_invoice_id" => $id,
					"file" => $file_path
				]);
			}
		}
	}

	/*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	public function hook_before_edit(&$postdata, $id)
	{
		$postdata['user_id'] = CRUDBooster::myId();
		Validator::make(
			$_REQUEST,
			[
				'status' => 'required',
				'delivery_company_id' => 'required',
				'invoice_number' => 'required',
				'date' => 'required',
				'due_date' => 'nullable|required_if:status,unpaid',
				'tax' => 'required',
				'amount' => 'required',
				'file.*' => 'nullable|mimes:jpeg,bmp,png,gif,svg,pdf',
				'description' => 'nullable|string|min:5|max:5000',
			],
			[
				'delivery_company_id.required' => 'Supplier required',
			]
		)->validate();
		if ($postdata['tax']) {
			$postdata['tax'] = removeCommaPrice($postdata['tax']);
		}
		if ($postdata['amount']) {
			$postdata['amount'] = removeCommaPrice($postdata['amount']);
		}
	}
	// public function getDeleteFile($id){
	// 	$data= DB::table('upcoming_invoice_documents')->where('id',$id)->delete();
	// }

	/*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	public function hook_after_edit($id)
	{
		if (request()->delete_f_lists && is_array(request()->delete_f_lists)) {
			foreach (request()->delete_f_lists as $file_id) {
				DB::table('upcoming_invoice_documents')->where('id', $file_id)->where('upcoming_invoice_id', $id)->delete();
			}
		}
		$allowedfileExtension = ['pdf', 'jpg', 'png', 'jpeg', 'PDF', 'JPG', 'PNG', 'JPEG'];
		if (is_array(request()->file)) {
			foreach (request()->file as $file) {
				if (!in_array($file->getClientOriginalExtension(), $allowedfileExtension)) {
					continue;
				}
				$rand = Str::random(40);
				$extension = $file->extension();
				$path = $file->storeAs('storage/invoice_files', $rand . '.' . $extension, ['visibility' => 'public', 'disk' => 'spaces']);
				$file_path = Storage::disk('spaces')->url($path);
				DB::table('upcoming_invoice_documents')->insert([
					"upcoming_invoice_id" => $id,
					"file" => $file_path
				]);
			}
		}
	}

	/*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	public function hook_before_delete($id)
	{
		//Your code here

	}

	/*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	public function hook_after_delete($id)
	{

		DB::table("upcoming_invoice_documents")->where('upcoming_invoice_id', $id)->delete();
	}

	public static function getChainedInvoice($id, $type = 0)
	{
		if ($type == 0) {
			$onlyMatched_invoice = UpcomingInvoice::where('id', $id)
				->where('user_id', CRUDBooster::myId())
				->first();
		} else {
			$onlyMatched_invoice = NewOrder::where('cms_user_id', CRUDBooster::myId())
				->where('id', $id)
				->orderBy("order_date", "desc")
				->first();
		}
		return $onlyMatched_invoice;
	}


	public function lockInvoice()
	{
		$request = Request::all();
		($request['type'] == 'order') ? $type = 1 : $type = 0;

		$affectedRows = DB::table('statements')->where('id', $request['id'])->update(array('ui_id' => $request['ui_id'], 'type' => $type));

		if ($affectedRows > 0) {
			if ($type == 0) {
				$updateStatus = UpcomingInvoice::where('id', $request['ui_id'])->update(array('status' => 'paid'));
			} else {
				$updateOrderInvoiceStatus = NewOrder::where('id', $request['ui_id'])->update(array('payment_status' => 'paid'));
			}
			return response()->json(['success' => true]);
		} else {
			return response()->json(['success' => false]);
		}
	}


	public function compareStatement()
	{
		$request = Request::all();

		$statement = DB::table('statements')
			->where('id', $request['id'])
			->first();

		if ($request['orderInvoice'] == 'true') {
			$invoice = NewOrder::where('id', $request['ui_id'])->first();
			$amount = $invoice->total;
			$type = 'order';
		} else {
			$invoice = UpcomingInvoice::where('id', $request['ui_id'])
				->first();
			$amount = $invoice->amount;
			$type = 'invoice';
		}

		$html = view('admin.accounting.components.compareFooter')->with(compact('invoice', 'statement', 'amount', 'type'))->render();

		return response()->json(['success' => true, 'html' => $html]);
	}


	public function getMacthedInvoices()
	{
		$request = Request::all();
		$statement = DB::table('statements')
			->where('id', $request['id'])
			->first();

		$amount  = str_replace(',', '', $statement->amount);

		if ($statement->ui_id) {

			if ($statement->type == 0) {

				$onlyMatched_invoice = UpcomingInvoice::where('id', $statement->ui_id)
					->where('user_id', CRUDBooster::myId())
					->get();

				$unmatched_invoice = UpcomingInvoice::where('id', '!=', $statement->ui_id)
					->where('user_id', CRUDBooster::myId())
					->where('status', '!=', 'paid')
					->get();

				$macthedOrderInvoice = NewOrder::where('cms_user_id', CRUDBooster::myId())
					->where('payment_status', '!=', 'paid')
					->orderBy("order_date", "desc")
					->get();

				$merged = $onlyMatched_invoice->merge($unmatched_invoice);

				$matched_invoice = $merged->all();

				$locked = true;
			} else {
				$onlyMacthedOrderInvoice = NewOrder::where('cms_user_id', CRUDBooster::myId())
					->where('id', $statement->ui_id)
					->orderBy("order_date", "desc")
					->get();

				$unmacthedOrderInvoice = NewOrder::where('cms_user_id', CRUDBooster::myId())
					->where('id', '!=', $statement->ui_id)
					->where('payment_status', '!=', 'paid')
					->orderBy("order_date", "desc")
					->get();

				$unmatched_invoice = UpcomingInvoice::where('user_id', CRUDBooster::myId())
					->where('status', '!=', 'paid')
					->get();

				$merged = $onlyMacthedOrderInvoice->merge($unmacthedOrderInvoice);
				$macthedOrderInvoice = $merged->all();

				$locked = true;
			}
		} else {

			$checker = UpcomingInvoice::where('user_id', CRUDBooster::myId())
				->where('status', '!=', 'paid')
				->get();

			$orderChecker = NewOrder::where('cms_user_id', CRUDBooster::myId())
				->where('payment_status', '!=', 'paid')
				->orderBy("order_date", "desc")
				->get();

			if (!count($checker)) {
				$matched_invoice = UpcomingInvoice::where('user_id', CRUDBooster::myId())
					->where('status', '!=', 'paid')
					->get();
			} else {

				$matched_invoice = $checker;
			}

			if (!count($orderChecker)) {
				$macthedOrderInvoice = NewOrder::where('cms_user_id', CRUDBooster::myId())
					->where('payment_status', '!=', 'paid')
					->orderBy("order_date", "desc")
					->get();
			} else {

				$macthedOrderInvoice = $orderChecker;
			}
		}

		$html = view('admin.accounting.components.matchedInvoices')->with(compact('matched_invoice', 'statement', 'macthedOrderInvoice', 'locked'))->render();
		return response()->json(['success' => true, 'html' => $html]);
	}

	public function getIndex()
	{
		// checking for app is purchased or not
		$pro_check  = AppStore::CheckAppPurchaseBoolean('36');

		if (!$pro_check[0]) {
			CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
		} else {
			$app_details = $pro_check[1];
		}

		// search inputs
		$q = request()->q; //invoice number
		$date = request()->from; //from date
		$from = $to = null;
		if (isset($date)) {
			$explodeDate = explode(" – ", $date);
			if (isset($explodeDate[0])) {
				$from = date("Y-m-d", strtotime($explodeDate[0]));
			}
			if (isset($explodeDate[0])) {
				$to = date("Y-m-d", strtotime($explodeDate[1]));
			} //to date
		}
		$amount = 0;
		$category = request()->category; //category
		$supplier = request()->supplier; //supplier
		// $quarter=request()->quarter; //quarter

		$country = request()->country; //country
		// if($quarter!=null ){
		// 	if($quarter == 1) {$lastDayOfMonth = cal_days_in_month(CAL_GREGORIAN, 4, date('Y')); $firstMonth = '01'; $lastMonth = '03';}
		// 	if($quarter == 2) {$lastDayOfMonth = cal_days_in_month(CAL_GREGORIAN, 8, date('Y')); $firstMonth = '04'; $lastMonth = '06';}
		// 	if($quarter == 3) {$lastDayOfMonth = cal_days_in_month(CAL_GREGORIAN, 12, date('Y')); $firstMonth = '07'; $lastMonth = '09';}
		// 	if($quarter == 4) {$lastDayOfMonth = cal_days_in_month(CAL_GREGORIAN, 12, date('Y')); $firstMonth = 10; $lastMonth = 12;}
		// }

		if (request()->p != null) {
			$page = request()->p;
		}

		$page_id = 'UP-I-001';
		switch ($page) {

			case "ongoing":
				$orders = NewOrder::where('cms_user_id', CRUDBooster::myId())->orderBy("order_date", "desc");

				if ($q != null) {
					$orders->where('invoice_number', 'like', "%" . $q . "%");
				}
				if ($country != null) {
					$orders->whereRaw('JSON_CONTAINS(billing, \'{"country": "' . $country . '"}\')');
				}
				// dd( $orders->get() );
				// if($quarter!=null){
				// 	$orders->whereBetween('order_date',[date('Y').'-'.$firstMonth.'-01',date('Y').'-'.$lastMonth.'-'.$lastDayOfMonth]);

				// }else{
				if ($from != null) {
					if ($to == null) {
						$orders->whereBetween('order_date', [$from, now()]);
					} else {
						$orders->whereBetween('order_date', [$from, $to]);
					}
				}
				// }

				if ($supplier || $category) {
					$orders->where('id', '<', 0);
				}
				$amount += $orders->sum('total');
				$orders = $orders->get();

				break;

			case "due":

				// intial query of due invoices
				$due_invoices = UpcomingInvoice::where('status', 'unpaid')->where('user_id', CRUDBooster::myId())->whereDate('due_date', '<', date('Y-m-d'))->orderBy("date", "desc");

				if ($q != null) {
					$due_invoices->where('invoice_number', 'like', "%" . $q . "%");
				}

				if ($from != null) {
					if ($to == null) {
						$due_invoices->whereBetween('date', [$from, now()]);
					} else {
						$due_invoices->whereBetween('date', [$from, $to]);
					}
				}

				// if($quarter!=null ){
				// 	$due_invoices->whereBetween('date',[date('Y').'-'.$firstMonth.'-01',date('Y').'-'.$lastMonth.'-'.$lastDayOfMonth]);
				// }

				if ($country != null) {
					$due_invoices->where('id', '<', 0);
				}

				if ($category != null) {
					$due_invoices->where("category", $category);
				}

				if ($supplier != null) {
					$due_invoices->where("delivery_company_id", $supplier);
				}
				$amount += $due_invoices->sum('amount');
				$due_invoices = $due_invoices->get();

				$invoices = $due_invoices;

				break;

			case "all_incoming":

				$invoices = UpcomingInvoice::where('user_id', CRUDBooster::myId())->orderBy("date", "desc");
				if ($q != null) {
					$invoices->where('invoice_number', 'like', "%" . $q . "%");
				}

				if ($from != null) {
					if ($to == null) {
						$invoices->whereBetween('date', [$from, now()]);
					} else {
						$invoices->whereBetween('date', [$from, $to]);
					}
				}

				// if($quarter!=null ){
				// 	$invoices->whereBetween('date',[date('Y').'-'.$firstMonth.'-01',date('Y').'-'.$lastMonth.'-'.$lastDayOfMonth]);
				// }
				if ($country != null) {
					$invoices->where('id', '<', 0);
				}
				if ($category != null) {
					$invoices->where("category", $category);
				}

				if ($supplier != null) {
					$invoices->where("delivery_company_id", $supplier);
				}
				$amount += $invoices->sum('amount');
				$invoices = $invoices->get();

				break;

			case "statement":
			case "unassigned-statement":

				// counting all bank statements
				$statementAllCount = DB::table('statements')
					->where('user_id', CRUDBooster::myId());

				// counting assigned bank statements
				$statementAssignedCount = DB::table('statements')
					->where('user_id', CRUDBooster::myId())
					->where('ui_id', '!=', NULL)
					->get()->count();

				// counting unassifned bank statements
				$statementUnAssignedCOunt = DB::table('statements')
					->where('user_id', CRUDBooster::myId())
					->where('ui_id', NULL)
					->get()->count();

				$statements = DB::table("statements")
					->where('user_id', CRUDBooster::myId())
					->orderBy('date', 'desc');

				if ($page == "unassigned-statement") {
					$statements->where('ui_id', NULL);
				}

				$fromModified = Carbon::parse($from)->format('Y-m-d H:i:s');
				$toModified = Carbon::parse($to)->endOfDay()->format('Y-m-d H:i:s');

				$today = Carbon::today()->endOfDay()->format('Y-m-d H:i:s');

				// if($quarter!=null ){
				// 	$statements->whereBetween('date',[date('Y').'-'.$firstMonth.'-01',date('Y').'-'.$lastMonth.'-'.$lastDayOfMonth]);
				// 	$statementAllCount->whereBetween('date',[date('Y').'-'.$firstMonth.'-01',date('Y').'-'.$lastMonth.'-'.$lastDayOfMonth]);
				// }else{
				if ($from != null) {
					if ($to == '') {
						$statements->whereBetween('date', [$fromModified, $today]);
						$statementAllCount->whereBetween('date', [$fromModified, $today])->get()->count();
					} else {
						$statements->whereBetween('date', [$fromModified, $toModified]);
						$statementAllCount->whereBetween('date', [$fromModified, $toModified]);
					}
				}
				// }

				$statementAllCount =  $statementAllCount->get()->count();

				$bankStatements = $statements->simplePaginate(10);
				$amount = 0;

				break;

			case "assigned-statement":

				// counting all bank statements
				$statementAllCount = DB::table('statements')
					->where('user_id', CRUDBooster::myId())
					->get()->count();

				// counting assigned bank statements
				$statementAssignedCount = DB::table('statements')
					->where('user_id', CRUDBooster::myId())
					->where('ui_id', '!=', NULL)
					->get()->count();

				// counting unassifned bank statements
				$statementUnAssignedCOunt = DB::table('statements')
					->where('user_id', CRUDBooster::myId())
					->where('ui_id', NULL)
					->get()->count();

				$statements = DB::table("statements")
					->where('user_id', CRUDBooster::myId())
					->where('ui_id', '!=', NULL)
					->orderBy('date', 'desc');

				$fromModified = Carbon::parse($from)->format('Y-m-d H:i:s');
				$toModified = Carbon::parse($to)->endOfDay()->format('Y-m-d H:i:s');

				$today = Carbon::today()->endOfDay()->format('Y-m-d H:i:s');

				// if($quarter!=null ){
				// 	$statements->whereBetween('date',[date('Y').'-'.$firstMonth.'-01',date('Y').'-'.$lastMonth.'-'.$lastDayOfMonth]);
				// }else{
				if ($from != null) {
					if ($to == '') {
						$statements->whereBetween('date', [$fromModified, $today]);
					} else {
						$statements->whereBetween('date', [$fromModified, $toModified]);
					}
				}
				// }

				$bankStatements = $statements->simplePaginate(10);
				$amount = 0;

				break;

			default:

				$invoices = UpcomingInvoice::where('user_id', CRUDBooster::myId())->orderBy("date", "desc");
				$orders = NewOrder::where('cms_user_id', CRUDBooster::myId())->orderBy("order_date", "desc");

				if ($q != null) {
					$orders->where('invoice_number', 'like', "%" . $q . "%");
					$invoices->where('invoice_number', 'like', "%" . $q . "%");
				}

				// if($quarter!=null ){
				// 	$invoices->whereBetween('date',[date('Y').'-'.$firstMonth.'-01',date('Y').'-'.$lastMonth.'-'.$lastDayOfMonth]);
				// 	$orders->whereBetween('order_date',[date('Y').'-'.$firstMonth.'-01',date('Y').'-'.$lastMonth.'-'.$lastDayOfMonth]);

				// }else{
				if ($from != null) {
					if ($to == null) {

						$orders->whereBetween('order_date', [$from, now()]);
						$invoices->whereBetween('date', [$from, now()]);
					} else {

						$orders->whereBetween('order_date', [$from, $to]);
						$invoices->whereBetween('date', [$from, $to]);
					}
				}
				// }

				if ($category != null) {
					$invoices->where("category", $category);
				}

				if ($supplier != null) {
					$invoices->where("delivery_company_id", $supplier);
				}
				if ($supplier || $category) {
					$orders->where('id', '<', 0);
				}
				if ($country != null) {
					$orders->whereRaw('JSON_CONTAINS(billing, \'{"country": "' . $country . '"}\')');
					$invoices->where('id', '<', 0);
				}
				$amount += $invoices->sum('amount');
				$amount += $orders->sum('total');
				$orders = $orders->get();
				$invoices = $invoices->get();

				$orCount = 0;
				$invCount = 0;

				break;
		}

		return view("admin.accounting.index", compact('statementAllCount', 'statementAssignedCount', 'statementUnAssignedCOunt', 'q', 'from', 'to', 'today', 'category', 'supplier', 'due_invoices', 'page', 'invoices', 'bankStatements', 'app_details', 'orders', 'orCount', 'invCount', 'page_id', 'amount', 'country'));
	}

	public function fetchMoreData(Request $request)
	{

		if ($request::ajax()) {
			$bankStatements = DB::table("statements")
				->where('user_id', CRUDBooster::myId())
				->orderBy('date', 'desc')
				->simplePaginate(10);
			return view('admin.accounting.components.index.all_and_unassigned_statements', compact('bankStatements'))->render();
		}
	}

	public function fetchMoreAssignedData(Request $request)
	{

		if ($request::ajax()) {
			$bankStatements = DB::table("statements")
				->where('user_id', CRUDBooster::myId())
				->where('ui_id', '!=', NULL)
				->orderBy('date', 'desc')
				->simplePaginate(10);
			return view('admin.accounting.components.index.assigned_statement', compact('bankStatements'))->render();
		}
	}

	public function fetchMoreUnassignedData(Request $request)
	{

		if ($request::ajax()) {
			$bankStatements = DB::table("statements")
				->where('user_id', CRUDBooster::myId())
				->where('ui_id', NULL)
				->orderBy('date', 'desc')
				->simplePaginate(10);
			return view('admin.accounting.components.index.all_and_unassigned_statements', compact('bankStatements'))->render();
		}
	}

	function convertXmlToCsvFile($xml_file_input, $csv_file_output)
	{

		$xml = simplexml_load_file(url('storage/app/' . $xml_file_input));

		$output_file = fopen(url('storage/app/' . $csv_file_output), 'w');

		$header = false;

		foreach ($xml as $key => $value) {
			if (!$header) {
				fputcsv($output_file, array_keys(get_object_vars($value)));
				$header = true;
			}
			fputcsv($output_file, get_object_vars($value));
		}

		fclose($output_file);
	}


	public function postDoUploadImportData()
	{
		ini_set('memory_limit', -1);
		$this->cbLoader();
		if (Request::hasFile('userfile')) {
			$file = Request::file('userfile');
			$ext = $file->getClientOriginalExtension();

			$validator = Validator::make([
				'extension' => $ext,
			], [
				'extension' => 'in:xls,xlsx,csv,XLS,XLSX,CSV,txt,TXT,XML,xml',
			]);

			if ($validator->fails()) {
				$message = $validator->errors()->all();

				return redirect()->back()->with(['message' => implode('<br/>', $message), 'message_type' => 'warning']);
			}

			//Create Directory Monthly
			$filePath = 'uploads/data';
			Storage::makeDirectory($filePath);

			//Move file to storage
			$filename = $file->getClientOriginalName();
			$url_filename = '';
			if (Storage::putFileAs($filePath, $file, $filename)) {
				$url_filename = $filePath . '/' . $filename;
			}

			$url = CRUDBooster::mainpath('import-data') . '?file=' . base64_encode($url_filename);

			return redirect($url);
		} else {
			return redirect()->back();
		}
	}

	function delTree($dir)
	{
		$files = array_diff(scandir($dir), array('.', '..'));

		foreach ($files as $file) {
			(is_dir("$dir/$file")) ? $this->delTree("$dir/$file") : unlink("$dir/$file");
		}

		return rmdir($dir);
	}


	public function getImportData()
	{
		$pro_check  = AppStore::CheckAppPurchaseBoolean('36');
		if (!$pro_check[0]) {
			CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
		}

		$this->cbLoader();
		$data['page_menu']  = \Route::getCurrentRoute()->getActionName();
		$data['page_title'] = 'Import Data ' . $module->name;

		if (Request::get('file') && !Request::get('import')) {
			$file = base64_decode(Request::get('file'));
			$fileWpath = storage_path('app/' . $file);

			$exists = Storage::disk('spaces')->exists('storage/app/' . CRUDBooster::myId() . '/' . basename($file));

			if ($exists) {
				$this->delTree(storage_path('app/uploads/data'));
				CRUDBooster::redirect(CRUDBooster::adminPath('upcoming_invoices/import-data'), "This file is already imported", "danger");
			}

			Storage::disk('spaces')->put('storage/app/' . CRUDBooster::myId() . '/' . basename($file), 'public');

			$type = pathinfo($fileWpath, PATHINFO_EXTENSION);
			$key = null;
			$key_count = 0;

			if ($type == 'xml' || $type == 'XML') {
				$arrContextOptions = array(
					"ssl" => array(
						"verify_peer" => false,
						"verify_peer_name" => false,
					),
				);

				$xml_data = file_get_contents(url($file), false, stream_context_create($arrContextOptions));

				$rows = Formatter::make($xml_data, Formatter::XML)->toCsv();

				$csv_name = pathinfo($fileWpath, PATHINFO_FILENAME);
				$dir = pathinfo($fileWpath, PATHINFO_DIRNAME);
				$fileWpath = $dir . '/' . $csv_name . '.csv';
				$new_data = file_put_contents($fileWpath, $rows);
			}

			if ($type == 'csv' || $type == 'txt') {
				$reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
				$reader->setInputEncoding('UTF-8');

				$spreadsheet = $reader->load($fileWpath);
			} else {
				$spreadsheet = IOFactory::load($fileWpath);
			}

			$spreadsheet = $spreadsheet->getActiveSheet();

			$rows = $spreadsheet->toArray();


			if ($key == null) {
				$key = array_map('trim', $rows[0]);
				$key_count = count($key);
			}

			if ($rows) {
				$countRows = $spreadsheet->getHighestRow() - 1;
			} else {
				$countRows = 0;
			}

			Session::put('total_data_import', $countRows);

			$table_columns = DB::getSchemaBuilder()->getColumnListing($this->table_statement);
			unset($table_columns[1]);
			unset($table_columns[2]);
			unset($table_columns[8]);

			$data['table_columns'] = $table_columns;
			$data['data_import_column'] = $key;
		}
		return view('admin.accounting.import', $data);
	}


	public function postDoImportChunk()
	{
		ini_set('max_execution_time', '0'); // for infinite time of execution
		$this->cbLoader();
		$file_md5 = md5(Request::get('file'));

		if (Request::get('file') && Request::get('resume') == 1) {
			$total = Session::get('total_data_import');
			$prog = intval(Cache::get('success_' . $file_md5)) / $total * 100;
			$prog = round($prog, 2);
			// if ($prog >= 100) {
			// 	Cache::forget('success_'.$file_md5);
			// }

			return response()->json(['progress' => $prog, 'last_error' => Cache::get('error_' . $file_md5)]);
		}

		$select_column = Session::get('select_column');
		$select_column = array_filter($select_column);
		$table_columns = DB::getSchemaBuilder()->getColumnListing($this->table_statement);

		$file = base64_decode(Request::get('file'));

		$file = storage_path('app/' . $file);

		$type = pathinfo($file, PATHINFO_EXTENSION);

		if ($type == 'xml' || $type == 'XML') {
			$csv_name = pathinfo($file, PATHINFO_FILENAME);
			$dir = pathinfo($file, PATHINFO_DIRNAME);
			$file = $dir . '/' . $csv_name . '.csv';
		}

		$key = null;
		$key_count = 0;
		$array = array();

		if ($type == 'csv' || $type == 'txt') {
			$reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
			$reader->setInputEncoding('UTF-8');


			$spreadsheet = $reader->load($file);
		} else {
			$spreadsheet = IOFactory::load($file);
		}

		$spreadsheet = $spreadsheet->getActiveSheet()->toArray();

		if ($key == null) {
			$key = array_map('trim', $spreadsheet[0]);
			$key_count = count($key);
		}

		$has_created_at = false;
		if (CRUDBooster::isColumnExists($this->table_statement, 'created_at')) {
			$has_created_at = true;
		}

		$data_import_column = [];
		$table_statement = $this->table_statement;
		unset($spreadsheet[0]);
		LazyCollection::make(function () use (&$spreadsheet) {
			$datas = $spreadsheet;
			foreach ($datas as $line) {
				yield $line;
			}
		})
			->chunk(1000) //split in chunk to reduce the number of queries
			->each(function ($lines) use (&$key, $key_count, $array, $select_column, $table_statement, $table_columns, $has_created_at, $file_md5) {

				foreach ($lines as $row) {

					if (count($row) == $key_count && !containsOnlyNull($row)) {
						$array = array_combine($key, $row);
						$value = (object)$array;
						$a = [];

						foreach ($select_column as $sk => $s) {
							$a['user_id'] = CRUDBooster::myId();
							$colname = $table_columns[$sk];
							if (CRUDBooster::isForeignKey($colname)) {

								if ($value->$s == '') {
									continue;
								}

								if (intval($value->$s)) {
									$a[$colname] = $value->$s;
								} else {
									$relation_table = CRUDBooster::getTableForeignKey($colname);
									$relation_moduls = DB::table('cms_moduls')->where('table_name', $relation_table)->first();

									$relation_class = __NAMESPACE__ . '\\' . $relation_moduls->controller;

									if (!class_exists($relation_class)) {
										$relation_class = '\App\Http\Controllers\\' . $relation_moduls->controller;
									}

									$relation_class = new $relation_class;
									$relation_class->cbLoader();

									$title_field = $relation_class->title_field;

									$relation_insert_data = [];
									$relation_insert_data[$title_field] = $value->$s;

									if (CRUDBooster::isColumnExists($relation_table, 'created_at')) {
										$relation_insert_data['created_at'] = date('Y-m-d H:i:s');
									}

									try {
										$relation_exists = DB::table($relation_table)
											->where($title_field, $value->$s)
											->first();

										if ($relation_exists) {
											$relation_primary_key = $relation_class->primary_key;
											$relation_id = $relation_exists->$relation_primary_key;
										} else {
											$relation_id = DB::table($relation_table)->insertGetId($relation_insert_data);
										}

										$a[$colname] = $relation_id;
									} catch (\Exception $e) {
										exit($e);
									}
								}
							} else {
								if ($colname == 'date') {
									$fdate  = str_replace(array('.', '/'), '-', $value->$s);
									$dateModified = Carbon::parse($fdate)->format('Y-m-d H:i:s');
									$a[$colname] = $dateModified;
								} else {
									$a[$colname] = $value->$s;
								}
							}
						}
					}
					try {
						if ($has_created_at) {
							$a['created_at'] = date('Y-m-d H:i:s');
						}
						DB::table($table_statement)->insert($a);
						Cache::increment('success_' . $file_md5);
					} catch (\Exception $e) {
						$e = (string) $e;
						Cache::put('error_' . $file_md5, $e, 500);
					}
				}
			});
		unlink($file);

		return response()->json(['status' => true]);
	}

	public function postDoneImport()
	{
		$this->cbLoader();
		$data['page_menu'] = Route::getCurrentRoute()->getActionName();
		$data['page_title'] = trans('crudbooster.import_page_title', ['module' => $module->name]);
		Session::put('select_column', Request::get('select_column'));

		return view('admin.accounting.import', $data);
	}

	public function getDownload()
	{
		$pro_check  = AppStore::CheckAppPurchaseBoolean('36');
		if (!$pro_check[0]) {
			CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
		}

		$user = User::find(CRUDBooster::myId());
		$q = request()->q;
		$date = request()->from; //from date
		$from = $to = null;
		if (isset($date)) {
			$explodeDate = explode(" – ", $date);
			if (isset($explodeDate[0])) {
				$from = date("Y-m-d", strtotime($explodeDate[0]));
			}
			if (isset($explodeDate[0])) {
				$to = date("Y-m-d", strtotime($explodeDate[1]));
				// $to=\Carbon\Carbon::parse($to)->addDay(1); //to date
			}
		}
		$category = request()->category;
		$supplier = request()->supplier;
		$country = request()->country;
		$page = request()->p;

		if ($page == null) {
			$page = "all";
		}
		if ($page == "all_incoming" || $page == "due") {
			$invoices = UpcomingInvoice::where('user_id', CRUDBooster::myId());
			if ($page == "due") {
				$invoices->where('status', 'unpaid')->whereDate('due_date', '<', date('Y-m-d'));
			}
			if ($q != null) {
				$invoices->where('invoice_number', 'like', "%" . $q . "%");
			}

			if ($from != null) {
				if ($to == null) {
					$invoices->whereBetween('date', [$from, now()]);
				} else {
					$invoices->whereBetween('date', [$from, $to]);
				}
			}

			if ($category != null) {
				$invoices->where("category", $category);
			}

			if ($supplier != null) {
				$invoices->where("delivery_company_id", $supplier);
			}

			if ($country != null) {
				$invoices->where('id', '<', 0);
			}

			$invoices = $invoices->pluck('id')->toArray();

			if (count($invoices)) {
				AccountingArchiveJob::dispatch($user, ['order' => [], 'invoice' => $invoices])->onQueue('long-running-queue');
				return redirect()->back()->with(['message' => 'Archive processing! After completing the process, we sent you notification!', 'message_type' => 'success']);
			} else {
				return redirect()->back()->with(['message' => 'Sorry! No files to archive.', 'message_type' => 'warning']);
			}
		} elseif ($page == 'ongoing') {
			$orders = NewOrder::where('cms_user_id', CRUDBooster::myId())->orderBy("id", "desc");
			if ($q != null) {
				$orders->where('invoice_number', 'like', "%" . $q . "%");
			}

			if ($from != null) {
				if ($to == null) {
					$orders->whereBetween('order_date', [$from, now()]);
				} else {
					$orders->whereBetween('order_date', [$from, $to]);
				}
			}

			if ($country != null) {
				$orders->whereRaw('JSON_CONTAINS(billing, \'{"country": "' . $country . '"}\')');
			}

			$orders = ($supplier || $category) ? $orders->where('id', '<', 0)->pluck('id')->toArray() : $orders->pluck('id')->toArray();
			if (count($orders)) {
				AccountingArchiveJob::dispatch($user, ['order' => $orders, 'invoice' => []])->onQueue('long-running-queue');
				return redirect()->back()->with(['message' => 'Archive processing! After completing the process, we sent you notification!', 'message_type' => 'success']);
			} else {
				return redirect()->back()->with(['message' => 'Sorry! No files to archive.', 'message_type' => 'warning']);
			}
		} elseif ($page == "all") {
			$invoices = UpcomingInvoice::where('user_id', CRUDBooster::myId());
			$orders = NewOrder::where('cms_user_id', CRUDBooster::myId())->orderBy("id", "desc");
			if ($q != null) {
				$orders->where('invoice_number', 'like', "%" . $q . "%");
				$invoices->where('invoice_number', 'like', "%" . $q . "%");
			}

			if ($from != null) {
				if ($to == null) {
					$invoices->whereBetween('date', [$from, now()]);
					$orders->whereBetween('order_date', [$from, now()]);
				} else {
					$orders->whereBetween('order_date', [$from, $to]);
					$invoices->whereBetween('date', [$from, $to]);
				}
			}

			if ($category != null) {
				$invoices->where("category", $category);
			}

			if ($supplier != null) {
				$invoices->where("delivery_company_id", $supplier);
			}

			if ($country != null) {
				$orders->whereRaw('JSON_CONTAINS(billing, \'{"country": "' . $country . '"}\')');
				$invoices->where('id', '<', 0);
			}

			$invoices = $invoices->pluck('id')->toArray();

			$orders = ($supplier || $category) ? $orders->where('id', '<', 0)->pluck('id')->toArray() : $orders->pluck('id')->toArray();
			if (count($orders) || count($invoices)) {
				AccountingArchiveJob::dispatch($user, ['order' => $orders, 'invoice' => $invoices])->onQueue('long-running-queue');
				return redirect()->back()->with(['message' => 'Archive processing! After completing the process, we sent you notification!', 'message_type' => 'success']);
			} else {
				return redirect()->back()->with(['message' => 'Sorry! No files to archive.', 'message_type' => 'warning']);
			}
		}

		// return response()->download(public_path('invoice_files/'.$fileName))->deleteFileAfterSend(true);
	}


	public function postAjaxOcr()
	{
		$file = request()->data;
		$data = [
			'apikey' => 'e6d34f8c7512bccfc900fe3e556a3202',
			'input' => 'base64',
			'file' => $file,
			'filename' => 'ocr.pdf',
			'outputformat' => 'txt',
		];
		$data = json_encode($data);
		$ch = curl_init('https://api.convertio.co/convert');
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);;


		curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
		$output = curl_exec($ch);
		$info = curl_getinfo($ch);
		curl_close($ch);
		$res = json_decode($output);

		if (!empty($res->data->id)) {
			$fileData = file_get_contents("https://api.convertio.co/convert/" . $res->data->id . "/dl/base64");
			return $fileData;
		}

		abort(400);
	}


	private function generate_invoice_pdf($order_id)
	{
		$data = [];
		$data['page_title'] = 'Invoice Details';

		$order = NewOrder::find($order_id);
		$data['order'] = $order;
		$data['product_list'] = json_decode($order->cart);
		$data['customer'] = $order->customer;
		$data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->first();
		$pdf_path = 'storage/order_invoice_new/order' . $data['order']->id . '.pdf';

		$pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.invoice.daily' : 'admin.invoice.general';
		$pdf = \PDF::loadView($pdf_view, $data)->setWarnings(false)->save($pdf_path);
		return $pdf_path;
	}

	public function getAdd()
	{
		$pro_check  = AppStore::CheckAppPurchaseBoolean('36');
		if (!$pro_check[0]) {
			CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
		}

		return view("admin.accounting.upcomming_invoice");
	}

	public function getEdit($id)
	{
		$pro_check  = AppStore::CheckAppPurchaseBoolean('36');
		if (!$pro_check[0]) {
			CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
		}

		$invoice = UpcomingInvoice::where('id', $id)->first();
		return view("admin.accounting.upcomming_invoice_edit", compact("invoice"));
	}

	public function getDetail($id)
	{
		$pro_check  = AppStore::CheckAppPurchaseBoolean('36');
		if (!$pro_check[0]) {
			CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
		}

		$invoice = UpcomingInvoice::with('invoice_category')->where('id', $id)->first();
		return view("admin.accounting.upcomming_invoice_detail", compact('invoice'));
	}

	//Delete invoice
	public function postDeleteInvoice()
	{
		//DB::beginTransaction();
		try {
			// checking for app is purchased or not
			$pro_check  = AppStore::CheckAppPurchaseBoolean('36');
			$basic_check  = AppStore::CheckAppPurchaseBoolean('12');

			if ((!$basic_check[0]) && (!$pro_check[0])) {
				throw new \Exception(trans("crudbooster.denied_access"));
			}

			if (!isset($_REQUEST['id']) || is_null($_REQUEST['id'])) throw new \Exception('Something went wrong!');

			$id = $_REQUEST['id'];
			$invoice = UpcomingInvoice::where('id', $id)->where('user_id', CRUDBooster::myId())->first();
			if ($invoice) {
				DB::table("upcoming_invoice_documents")->where('upcoming_invoice_id', $invoice->id)->delete();
				$invoice->delete();
			} else {
				throw new \Exception('Something went wrong!');
			}
			//DB::commit();
			return response()->json([
				'success' => true,
				'message' => 'Delete successfully!'
			]);
		} catch (\Exception $e) {
			//DB::rollBack();
			return response()->json([
				'success' => false,
				'message' => 'Something went wrong!'
			]);
		}
	}

	//generate tiny url using tiny url
	function get_tiny_url($url)
	{
		$ch = curl_init();
		$timeout = 5;
		curl_setopt($ch, CURLOPT_URL, 'http://tinyurl.com/api-create.php?url=' . $url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
		$data = curl_exec($ch);
		curl_close($ch);
		return $data;
	}

	// generate shareable link | expire after 30 days
	public function getShareableLink(Request $request)
	{
		$pro_check  = AppStore::CheckAppPurchaseBoolean('36');
		if (!$pro_check[0]) {
			CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
		}

		try {
			Validator::make($_REQUEST, [
				'url' => 'required',
			])->validate();

			$post_data = Request::post();
			$token = md5(rand(1, 20) . microtime());

			ShareableLink::updateOrCreate(
				[
					'token' => $token,
				],
				[
					'user_id' => CRUDBooster::myId(),
					'original_url' =>  $post_data['url'],
				]
			);

			$mail_data['body'] = $post_data['body'] . '<br> Shared Link: ' . $this->get_tiny_url(route('linkExpiration', $token));
			$mail_data['subject'] = CRUDBooster::myName() . " shared an invoice with you";

			app('drm.mailer')->getMailer()->to($post_data['email'])->send(new DRMSEndMail($mail_data));
			CRUDBooster::redirect(CRUDBooster::adminPath('upcoming_invoices'), "Link send to provided email.", "success");
		} catch (\Exception $e) {
			CRUDBooster::redirect(CRUDBooster::adminPath('upcoming_invoices'), "Error: Something went wrong!", "error");
		}
	}

	// check if link expired or not
	public function linkExpiration($token)
	{
		try {
			$is_avaliable_link = false;
			$ShareableLink = ShareableLink::where('token', $token)->first(['original_url', 'updated_at']);
			if ($ShareableLink) {
				$is_avaliable_link = \Carbon\Carbon::parse($ShareableLink->updated_at)->addDays(30)->greaterThan(\Carbon\Carbon::today());
			} else {
				throw new \Exception("Invalid url!");
			}
			if ($is_avaliable_link) {
				$link = $ShareableLink->original_url;
				return view("admin.accounting.components.diposableInvoice", compact('link'));
			} else {
				throw new \Exception("This link has expired!");
			}
		} catch (\Exception $e) {
			return response()->json($e->getMessage());
		}
	}

	public function copyShareableLink()
	{
		try {
			$pro_check  = AppStore::CheckAppPurchaseBoolean('36');
			if (!$pro_check[0]) {
				throw new \Exception("You do not have aceess this!");
			}
			Validator::make($_REQUEST, [
				'url' => 'required',
			])->validate();

			$url = $_REQUEST['url'];
			$token = md5(rand(1, 20) . microtime());
			$hit_url = route('linkExpiration', $token);

			$savedOrUpdatedData = ShareableLink::updateOrCreate(
				[
					'token' => $token,
				],
				[
					'user_id' => CRUDBooster::myId(),
					'original_url' =>  $url,
				]
			);

			$tiny_url = $this->get_tiny_url($hit_url);
			return response()->json([
				'success' => true,
				'message' => 'URL generated successfully!',
				'url' => $tiny_url
			]);
		} catch (\Exception $e) {
			return response()->json([
				'success' => false,
				'message' => $e->getMessage()
			]);
		}
	}


	/**Invoice download background*/
	public function archiveInvoices($user, $ids)
	{
		try {
			$order_ids = (isset($ids['order']) && count($ids['order'])) ? $ids['order'] : null;
			$invoice_ids = (isset($ids['invoice']) && count($ids['invoice'])) ? $ids['invoice'] : null;
			$zip = new ZipArchive(); // Load zip library

			$date = \Carbon\Carbon::now()->format('Y_m_d');
			$unix_time = \Carbon\Carbon::now()->unix();
			$filename = $date . "_" . $user->id . '_' . $unix_time;

			$zip_name = "storage\order_invoice_new\orders_" . $filename . ".zip"; // Zip name
			\Storage::disk('public')->makeDirectory('order_invoice_new');

			$orders = ($order_ids) ? NewOrder::where('is_locked', '<>', 1)->find($order_ids) : null;
			$invoices = ($invoice_ids) ? UpcomingInvoice::find($invoice_ids) : null;

			if ((is_null($orders) && is_null($invoices))) throw new \Exception('Invoice file empty!');

			if (($orders || $invoices) && ($zip->open($zip_name, ZIPARCHIVE::CREATE | ZIPARCHIVE::OVERWRITE))) {
				$hasData = false;

				$logs = app(\App\Services\Accounting\ArchiveLog::class)->logs($orders, $invoices);

				if ($orders && count($orders)) {
					$hasData = true;
					foreach ($orders as $order) {
						$order_date = date('Y-m-d', strtotime($order->order_date));
						$invoice_name = "inv_{$order->id}_" . $order_date . "_" . inv_number_string($order->invoice_number, $order->inv_pattern) . ".pdf";

						// $data = [];

						// if($order->credit_number && $order->cms_user_id == 2455) {
						// 	$order->marketplace_order_ref = DB::table('new_orders')->where('credit_ref', '=', $order->id)->value('marketplace_order_ref');
						// }

						// if($order->marketplace_order_ref && $order->cms_user_id == 2455) {
						//   $data['shipping_details'] = DB::table("new_orders")
						//   ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
						//   ->select("new_orders.id as mp_order_id", "new_customers.*")
						//   ->where('new_orders.id', $order->marketplace_order_ref)->first();
						// }

						// $data['order'] = $order;
						// $data['product_list'] = json_decode($order->cart);
						// $data['customer'] = $order->customer;

						// if($order->invoice_layout_id)
						// {
						//     $data['setting'] = DB::table('drm_invoice_setting')
						//         ->where('cms_user_id', $order->cms_user_id)
						//         ->where('id', $order->invoice_layout_id)
						//         ->first();
						// }else{
						//     $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();
						// }

						// $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439]))? 'admin.invoice.daily' : 'admin.invoice.general';
						// $pdf_view = ($order->insert_type == 4)? 'admin.invoice.charge_inv' : $pdf_view;

						// TODO:: DROPMATIX
						$pdf = (new \App\Services\Order\InvoiceDoc($order->id));

						$zip->addFromString($invoice_name, $pdf->stream());
					}
				}

				if ($invoices && count($invoices)) {
					$hasData = true;
					foreach ($invoices as $invoice) {
						$files = DB::table("upcoming_invoice_documents")->where('upcoming_invoice_id', $invoice->id)->get();
						foreach ($files as $file) {
							$relativeNameInZipFile = $invoice->invoice_number . "/" . basename($file->file);
							$folderPath = public_path('invoice_files/' . $filename . '/' . $invoice->invoice_number);

							if (!is_dir($folderPath)) {
								\File::makeDirectory($folderPath, $mode = 0755, true, true);
							}
							try {
								$download_file = file_get_contents($file->file);
								$zip->addFromString($relativeNameInZipFile, $download_file);
							}
							//catch exception
							catch (\Exception $e) {
							}
						}
					}
				}

				if($hasData && $logs)
                {
                    $zip->addFromString('logs.txt', $logs);
                }

				$zip->close();
				if (!$hasData) return false;

				$invoice_file_path = 'archive/invoice_' . $filename;
				Storage::disk('spaces')->put($invoice_file_path . ".zip", file_get_contents(realpath($zip_name)), 'public');
				@unlink(realpath($zip_name));

				// $download_url = url('admin/drm_all_orders/download-archive-order/'.$invoice_file_path);
				$download_url = Storage::disk('spaces')->url($invoice_file_path . ".zip");

				//Auto export
				if (isset($ids['export_service']) && $export_service = $ids['export_service']) {
					app(\App\Services\Accounting\AutoExportFile\ExportFile::class)->sendTo(
						[
							'user_id' => $user->id,
							'export_service' => $export_service,
						],
						[$download_url]
					);

					return;
				}

				$user->notify(new DRMNotification('Upcomming Invoice Archived successfully! Download it.', 'BACKGROUND_ARCHIVE_JOB', $download_url));
			} else {
				throw new \Exception('Invoice Archive failed. Please try again!');
			}
		} catch (\Exception $e) {
			$user->notify(new DRMNotification('Invoice Archive Error: ' . $e->getMessage(), '', '#'));
		}
	}

	public function getDownloadArchiveOrder($folder, $filename)
	{
		$url = Storage::disk('spaces')->url($folder . '/' . $filename . ".zip");
		header('Content-type: application/zip');
		header('Content-Disposition: attachment; filename="Orders.zip"');
		readfile($url);
		//remove zip file logic write hear..
	}

	//Create Category
	public function postCreateCategory()
	{
		$request = $_REQUEST;
		//DB::beginTransaction();
		try {
			// checking for app is purchased or not
			$pro_check  = AppStore::CheckAppPurchaseBoolean('36');
			$basic_check  = AppStore::CheckAppPurchaseBoolean('12');
			if ((!$basic_check[0]) && (!$pro_check[0])) {
				throw new \Exception(trans("crudbooster.denied_access"));
			}

			$category = UpcommingInvoiceCategory::where(['user_id' => CRUDBooster::myId(), 'category_name' => $request['category_name']])->first();
			if (is_null($category)) {
				$category = UpcommingInvoiceCategory::create(['user_id' => CRUDBooster::myId(), 'category_name' => $request['category_name']]);
			} else {
				throw new \Exception('Category already exist!');
			}
			//DB::commit();
			return response()->json([
				'success'	=> true,
				'message'	=> 'Category created successfully!',
				'id'		=> $category->id,
				'category_name'		=> $category->category_name,
			]);
		} catch (\Exception $e) {
			//DB::rollBack();
			return response()->json([
				'success'	=> false,
				'message'	=> $e->getMessage()
			]);
		}
	}

	//Update Category
	public function postUpdateCategory()
	{
		$request = $_REQUEST;
		// //DB::beginTransaction();
		try {
			// checking for app is purchased or not
			$pro_check  = AppStore::CheckAppPurchaseBoolean('36');
			$basic_check  = AppStore::CheckAppPurchaseBoolean('12');

			if ((!$basic_check[0]) && (!$pro_check[0])) {
				throw new \Exception(trans("crudbooster.denied_access"));
			}

			$category = UpcommingInvoiceCategory::where(['id' => $request['id'], 'user_id' => CRUDBooster::myId()])->first();
			if ($category) {
				$category->update(['category_name' => $request['category_name']]);
			} else {
				throw new \Exception('Category update Failed!');
			}
			// //DB::commit();
			return response()->json([
				'success'	=> true,
				'message'	=> 'Category update successfully!'
			]);
		} catch (\Exception $e) {
			// //DB::rollBack();
			return response()->json([
				'success'	=> false,
				'message'	=> $e->getMessage()
			]);
		}
	}

	//Delete Category
	public function postTrashCategory()
	{
		$request = $_REQUEST;
		//DB::beginTransaction();
		try {
			// checking for app is purchased or not
			$pro_check  = AppStore::CheckAppPurchaseBoolean('36');
			$basic_check  = AppStore::CheckAppPurchaseBoolean('12');

			if ((!$basic_check[0]) && (!$pro_check[0])) {
				throw new \Exception(trans("crudbooster.denied_access"));
			}

			$category = UpcommingInvoiceCategory::where(['id' => $request['id'], 'user_id' => CRUDBooster::myId()])->first();
			if ($category) {
				DB::table("upcoming_invoices")->where('category', $category->id)->update(['category' => null]);
				$category->delete();
			} else {
				throw new \Exception('Category delete Failed!');
			}
			//DB::commit();
			return response()->json([
				'success'	=> true,
				'message'	=> 'Category delete successfully!'
			]);
		} catch (\Exception $e) {
			//DB::rollBack();
			return response()->json([
				'success'	=> false,
				'message'	=> $e->getMessage()
			]);
		}
	}

	//Fetch all Category
	public function postAllCategory()
	{
		try {
			// checking for app is purchased or not
			$pro_check  = AppStore::CheckAppPurchaseBoolean('36');
			$basic_check  = AppStore::CheckAppPurchaseBoolean('12');

			if ((!$basic_check[0]) && (!$pro_check[0])) {
				throw new \Exception(trans("crudbooster.denied_access"));
			}
			$category = UpcommingInvoiceCategory::where('user_id', CRUDBooster::myId())->orderBy('category_name')->pluck('category_name', 'id')->toArray();
			return response()->json([
				'success'	=> true,
				'category'	=> $category
			]);
		} catch (\Exception $e) {
			return response()->json([
				'success'	=> false,
				'message'	=> $e->getMessage()
			]);
		}
	}








	public function getExcel()
	{
		$pro_check  = AppStore::CheckAppPurchaseBoolean('36');
		if (!$pro_check[0]) {
			CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
		}

		$user = User::find(CRUDBooster::myId());
		$q = request()->q;
		$date = request()->from; //from date
		$from = $to = null;
		if (isset($date)) {
			$explodeDate = explode(" – ", $date);
			if (isset($explodeDate[0])) {
				$from = date("Y-m-d", strtotime($explodeDate[0]));
			}
			if (isset($explodeDate[0])) {
				$to = date("Y-m-d", strtotime($explodeDate[1]));
				// $to=\Carbon\Carbon::parse($to)->addDay(1); //to date
			}
		}
		$category = request()->category;
		$supplier = request()->supplier;
		$country = request()->country;
		$page = request()->p;

		if ($page == null) {
			$page = "all";
		}
		if ($page == "all_incoming" || $page == "due") {
			$invoices = UpcomingInvoice::where('user_id', CRUDBooster::myId());
			if ($page == "due") {
				$invoices->where('status', 'unpaid')->whereDate('due_date', '<', date('Y-m-d'));
			}
			if ($q != null) {
				$invoices->where('invoice_number', 'like', "%" . $q . "%");
			}

			if ($from != null) {
				if ($to == null) {
					$invoices->whereBetween('date', [$from, now()]);
				} else {
					$invoices->whereBetween('date', [$from, $to]);
				}
			}

			if ($category != null) {
				$invoices->where("category", $category);
			}

			if ($supplier != null) {
				$invoices->where("delivery_company_id", $supplier);
			}

			if ($country != null) {
				$invoices->where('id', '<', 0);
			}

			$invoices = $invoices->pluck('id')->toArray();

			if (count($invoices)) {
				return $this->invoiceExcel($user, ['order' => [], 'invoice' => $invoices]);
			} else {
				return redirect()->back()->with(['message' => 'Sorry! No files to download.', 'message_type' => 'warning']);
			}
		} elseif ($page == 'ongoing') {
			$orders = NewOrder::where('cms_user_id', CRUDBooster::myId())->orderBy("id", "desc");
			if ($q != null) {
				$orders->where('invoice_number', 'like', "%" . $q . "%");
			}

			if ($from != null) {
				if ($to == null) {
					$orders->whereBetween('order_date', [$from, now()]);
				} else {
					$orders->whereBetween('order_date', [$from, $to]);
				}
			}

			if ($country != null) {
				$orders->whereRaw('JSON_CONTAINS(billing, \'{"country": "' . $country . '"}\')');
			}

			$orders = ($supplier || $category) ? $orders->where('id', '<', 0)->pluck('id')->toArray() : $orders->pluck('id')->toArray();
			if (count($orders)) {
				return $this->invoiceExcel($user, ['order' => $orders, 'invoice' => $invoices]);
			} else {
				return redirect()->back()->with(['message' => 'Sorry! No files to download.', 'message_type' => 'warning']);
			}
		} elseif ($page == "all") {
			$invoices = UpcomingInvoice::where('user_id', CRUDBooster::myId());
			$orders = NewOrder::where('cms_user_id', CRUDBooster::myId())->orderBy("id", "desc");
			if ($q != null) {
				$orders->where('invoice_number', 'like', "%" . $q . "%");
				$invoices->where('invoice_number', 'like', "%" . $q . "%");
			}

			if ($from != null) {
				if ($to == null) {
					$invoices->whereBetween('date', [$from, now()]);
					$orders->whereBetween('order_date', [$from, now()]);
				} else {
					$orders->whereBetween('order_date', [$from, $to]);
					$invoices->whereBetween('date', [$from, $to]);
				}
			}

			if ($category != null) {
				$invoices->where("category", $category);
			}

			if ($supplier != null) {
				$invoices->where("delivery_company_id", $supplier);
			}

			if ($country != null) {
				$orders->whereRaw('JSON_CONTAINS(billing, \'{"country": "' . $country . '"}\')');
				$invoices->where('id', '<', 0);
			}

			$invoices = $invoices->pluck('id')->toArray();

			$orders = ($supplier || $category) ? $orders->where('id', '<', 0)->pluck('id')->toArray() : $orders->pluck('id')->toArray();
			if (count($orders) || count($invoices)) {
				return $this->invoiceExcel($user, ['order' => $orders, 'invoice' => $invoices]);
			} else {
				return redirect()->back()->with(['message' => 'Sorry! No files to download.', 'message_type' => 'warning']);
			}
		}
	}

	private function invoiceExcel($user, $data = [])
	{

		if (Excel::store(new InvoicesExport($data), 'upcomming/invoices_' . CRUDBooster::myId() . '.xlsx')) {
			header('Content-type: application/xlsx');
			header('Content-Disposition: attachment; filename="invoices.xlsx"');
			readfile(storage_path('app/upcomming/invoices_' . CRUDBooster::myId() . '.xlsx'));
		}
	}
}

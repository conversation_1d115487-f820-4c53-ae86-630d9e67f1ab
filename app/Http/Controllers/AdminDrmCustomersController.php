<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;
	use App\CustomerTag;
	use Illuminate\Support\Facades\Validator;

	class AdminDrmCustomersController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "company_name";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = true;
			$this->button_delete = false;
			$this->button_detail = true;
			$this->button_show = false;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "drm_customers";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Customer Id","name"=>"id"];
			$this->col[] = ["label"=>"Customer Name","name"=>"full_name"];
			$this->col[] = ["label"=>"Email","name"=>"email"];
			$this->col[] = ["label"=>"Phone","name"=>"phone"];
			// $this->col[] = ["label"=>"Website","name"=>"website"];
			$this->col[] = ["label"=>"Currency","name"=>"currency"];
			$this->col[] = ["label"=>"Country Id","name"=>"country"];
			if(CRUDBooster::isSuperadmin()){
			$this->col[] = ["label"=>"Payment Status","name"=>"status"];
			}
			// $this->col[] = ["label"=>"Insert Type","name"=>"insert_type"];


			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];

			$this->form[] = ['label'=>'User Id','name'=>'user_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'cms_users,name'];

			$this->form[] = ['label'=>'Company Name','name'=>'company_name','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Vat Number','name'=>'vat_number','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Phone','name'=>'phone','type'=>'number','validation'=>'required|numeric','width'=>'col-sm-10','placeholder'=>'You can only enter the number only'];
			$this->form[] = ['label'=>'Website','name'=>'website','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Groups','name'=>'groups','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Currency','name'=>'currency','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Default Language','name'=>'default_language','type'=>'select2','validation'=>'required|min:1|max:255','width'=>'col-sm-10','datatable'=>'languages,name'];
			$this->form[] = ['label'=>'Address','name'=>'address','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'City','name'=>'city','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'State','name'=>'state','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Zip Code','name'=>'zip_code','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Country','name'=>'country','type'=>'select2','validation'=>'required|min:1|max:255','width'=>'col-sm-10','datatable'=> 'countries,name','datatable_where'=>'countries.is_active=1'];



			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ["label"=>"Company Name","name"=>"company_name","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Vat Number","name"=>"vat_number","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
			//$this->form[] = ["label"=>"Phone","name"=>"phone","type"=>"number","required"=>TRUE,"validation"=>"required|numeric","placeholder"=>"You can only enter the number only"];
			//$this->form[] = ["label"=>"Website","name"=>"website","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Groups","name"=>"groups","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Currency","name"=>"currency","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Default Language","name"=>"default_language","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Address","name"=>"address","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"City","name"=>"city","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"State","name"=>"state","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Zip Code","name"=>"zip_code","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
			//$this->form[] = ["label"=>"Country","name"=>"country","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"User Id","name"=>"user_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"user,id"];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
				if(!CRUDBooster::isSuperadmin()){
					$query->where('user_id',CRUDBooster::myId());
				}

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
			*/

	    public function hook_row_index($column_index,&$column_value) {

				// if($column_index ==2)
				// {
				// 	$user_id = $column_value;

				// 	$user = DB::table('drm_customers')->where('id',$user_id)->first();
				// 	$column_value = $user->f_name . " " .$user->l_name  ;
				// }

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }


		public function getDetail($id) {
			//Create an Auth
			if(!CRUDBooster::isRead() && $this->global_privilege==FALSE || $this->button_edit==FALSE) {
				CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
			}

			$data = [];
			$data['page_title'] = 'Customer Overview ';

			if(CRUDBooster::isSuperadmin())
			{
				$customer = DB::table('drm_customers')->find($id);
			}
			else
			{
				$customer = DB::table('drm_customers')->where([
					'id' => $id,
					'user_id' =>  CRUDBooster::myId(),
				])->first();
			}

			// error if customer not found
			if($customer->id == NULL)
			{
				return CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Please enter input correctly'), 'error');
			}

			// dd($customer);

			$orders = DB::table('drm_orders_new')->where('drm_customer_id',$customer->id)
			// ->get()
			;


			// dd($orders);

			$temp = $orders->pluck('id');

			foreach( (object) $temp as $item)
			{
				$order_ids [] = $item;
			}

			// dd($order_ids);

			$prouducts = 0;
			if(count((array)$order_ids)!=0)
			{
				$prouducts = DB::table('drm_order_products')->whereIn('drm_order_id',$order_ids)->count();
			}

			$data['manual_tags'] = CustomerTag::where('user_id',CRUDBooster::myId())->where('customer_id',$customer->id)->where('type','manual_tags')->get();

			// $smart_tags = CustomerTag::where('user_id',CRUDBooster::myId())->where('customer_id',$customer->id)->where('type','smart_tags')->first();
			// $data['smart_tags'] = json_decode($smart_tags->tags, TRUE);

			// $priority_tags = CustomerTag::where('user_id',CRUDBooster::myId())->where('customer_id',$customer->id)->where('type','tags')->first();
			// $data['tags'] = json_decode($priority_tags->tags, TRUE);

			$data['customer'] = $customer;
			$data['orders'] = $orders->get();

			$data['total_order'] = $orders->count();
			$data['shipped'] = $orders->where("status","Shipped")->count() + $orders->where("status","Versendet")->count();
			$data['others'] = $data['total_order'] - $data['shipped'];
			$data['total'] = DB::table('drm_orders_new')->where('drm_customer_id',$customer->id)->sum('total');
			$data['total_product'] =$prouducts;

			return view('admin.drm_customer.details',$data);

		}

		public function getAdd()
		{
			//Create an Auth
			if(!CRUDBooster::isCreate() && $this->global_privilege==FALSE || $this->button_add==FALSE) {
				CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
			}

			$data = [];
			$data['page_title'] = 'Add Data';

			// $data['languages'] = DB::table('languages')->get();
			$data['countries'] = DB::table('countries')->get();
			$data['users'] = DB::table('cms_users')->get();

			return view('admin.drm_customer.add', $data);
		}

		// return 1 if successfull
		// return 0 if unsuccessful
		public function add_customer($customer_info = [] )
		{

			// dd($customer_info);
			if($customer_info == [])
			{
				return 0;
			}

			$user_id = $customer_info['user_id'] ?? CRUDBooster::myId();

			//$userInfo = DB::table('cms_users')->find($user_id);
			// dd($userInfo->plan_status);
			//if($userInfo->plan_status == 'Free'){
			//	$status = 'Unpaid';
			//}
			//else{
			//	$status = 'Paid';
			//}


			// customer add
			DB::table('drm_customers')->updateOrInsert([
				'email' => $customer_info['email'],
				'user_id' => $user_id,
			],
			[
				'full_name' => $customer_info['customer_full_name'],
				'company_name' => $customer_info['company_name'],
				'country' => $customer_info['country'],
				'phone' => $customer_info['phone'],
				'website' => $customer_info['website'],
				'city' => $customer_info['city'],
				'zip_code' => $customer_info['zip_code'],
				'state' => $customer_info['state'],
				'currency' => $customer_info['currency'],
				'default_language' => $customer_info['default_language'],
				'address' => $customer_info['address'],
				'insert_type'=>$customer_info['insert_type'],
				// 'status'	=> $status,
				// 'vat_number' => $customer_info['vat_number'],
			]);


			$customer = DB::table('drm_customers')->where([
				'email' => $customer_info['email'],
				'user_id' => $user_id,
			])->first();

			// dd($customer->id);
			// sh1ipping
			DB::table('drm_customer_address')->updateOrInsert([
				'drm_customer_id' => $customer->id,
				'type' => 'shipping',
			],
			[
				'street' => $customer_info['street_shipping'],
				'city' => $customer_info['city_shipping'],
				// 'state' => $customer_info['state_shipping'],
				'zipcode' => $customer_info['zipcode_shipping'],
				'country' => $customer_info['country_shipping'],
			]);



			// billing
			if(isset($customer_info['is_same_address']))
			{
				DB::table('drm_customer_address')->updateOrInsert([
					'drm_customer_id' => $customer->id,
					'type' => 'billing',
				],
				[
					'street' => $customer_info['street_shipping'],
					'city' => $customer_info['city_shipping'],
					// 'state' => $customer_info['state_shipping'],
					'zipcode' => $customer_info['zipcode_shipping'],
					'country' => $customer_info['country_shipping'],

				]);
			}
			else{
				DB::table('drm_customer_address')->updateOrInsert([
					'drm_customer_id' => $customer->id,
					'type' => 'billing',
				],
				[
					'street' => $customer_info['street_billing'],
					'city' => $customer_info['city_billing'],
					// 'state' => $customer_info['state_billing'],
					'zipcode' => $customer_info['zipcode_billing'],
					'country' => $customer_info['country_billing'],

				]);
			}

			return $customer->id;
			// return $customer_info;
		}

		public function postAddSave()
		{

			$validator = Validator::make($_REQUEST, [

				"customer_full_name" => "required",
				"email" => "required | unique:drm_customers",
				// "company_name" => "required",
				// "phone" => "required",
				"currency" => "required",
				"default_language" => "required",
				"city" => "required",
				// "state" => "required",
				"country" => "required",
				// "street_shipping" => "required",
				// "city_shipping" => "required",
				// "state_shipping" => "required",
				"country_shipping" => "required"

			]);

			// if(!isset($_REQUEST['is_same_address']))
			// {
			// 	$validator = Validator::make($_REQUEST, [
			// 		"street_billing" => "required",
			// 		"city_billing" => "required",
			// 		"state_billing" => "required",
			// 		"country_billing" => "required"
			// 	]);
			// }

			if(CRUDBooster::isSuperadmin())
			{
				$validator = Validator::make($_REQUEST, [
					"user_id" => "required",
				]);

				$user_id =  $_REQUEST['user_id'];
			}else{
				$user_id = CRUDBooster::myId();
			}


			if ($validator->fails()) {
				return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
				// return 0;
			}

			$_REQUEST['insert_type'] ="Manual";
			// $this->add_customer($_REQUEST);
			if(!$this->add_customer($_REQUEST))
			{
				CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Please enter input correctly'), 'error');
			}

			// dd($_REQUEST);

			return redirect('admin/drm_customers')->with('success',"Successfully Added Customer");

		}

		// ----------- add customer ajax ---------
		public function postAddCustomerAjax()
		{
			// return response()->json($_REQUEST);
			// return response()->json($_REQUEST["street_billing"]);

			$validator = Validator::make($_REQUEST, [

				"customer_full_name" => "required",
				"email" => "required | unique:drm_customers",
				// "company_name" => "required",
				// "phone" => "required",
				"currency" => "required",
				"default_language" => "required",
				"city" => "required",
				// "state" => "required",
				"country" => "required",
				// "street_shipping" => "required",
				// "city_shipping" => "required",
				// "state_shipping" => "required",
				"country_shipping" => "required"

			]);

			if(CRUDBooster::isSuperadmin())
			{
				$validator = Validator::make($_REQUEST, [
					"user_id" => "required",
				]);

				$user_id =  $_REQUEST['user_id'];
			}else{
				$user_id = CRUDBooster::myId();
			}


			if ($validator->fails()) {
				$_REQUEST["error"] = "1";
				return response()->json($validator->errors(),500);
				// return 0;
			}

			$_REQUEST['insert_type'] ="Manual";

			$_REQUEST['customer_id'] = "";
			$_REQUEST['customer_id'] = $this->add_customer($_REQUEST) ;

			$data = $_REQUEST;

			if($data['customer_id'] == null || $data['customer_id'] == "")
			{
				return response()->json($data,500);
			}

			return response()->json($data, 200);

		}

		public function getEdit($id) {
			//Create an Auth
			if(!CRUDBooster::isUpdate() && $this->global_privilege==FALSE || $this->button_edit==FALSE) {
				CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
			}


			session(['customer_id' => $id]);

			$data = [];
			$data['page_title'] = 'Edit Data';
			// $data['languages'] = DB::table('countries')->get(); // jahdiulhasazahid
			$data['countries'] = DB::table('countries')->get();

			$customer = DB::table('drm_customers')->where('id',$id);

			if(!CRUDBooster::isSuperadmin()){
				$customer->where('user_id',CRUDBooster::myId());
			}

			$data['customer'] = $customer->first();
			$data['selectUserName'] = DB::table('cms_users')->where('id',$data['customer']->user_id)->select('name')->first();

			if($data['customer'] == null)
			{
				CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
			}
			// dd($data['customer']);

			$data['customer']->shipping = DB::table('drm_customer_address')->where([
				'drm_customer_id'=> $id,
				'type' => 'shipping'
			])->first();

			$data['customer']->billing = DB::table('drm_customer_address')->where([
				'drm_customer_id'=> $id,
				'type' => 'billing'
			])->first();

			// jahidulhasanzahid
			$data['users'] = DB::table('cms_users')->select('id','name')->get();
			// dd($data['customer']);

			return view('admin.drm_customer.edit',$data);
		}



		public function postEditSave($id)
		{
			// dd($_REQUEST);

			$validator = Validator::make($_REQUEST, [

				"customer_full_name" => "required",
				"email" => "required",
				// "company_name" => "required",
				// "phone" => "required",
				"currency" => "required",
				"default_language" => "required",
				"city" => "required",
				// "state" => "required",
				"country" => "required",
				// "street_shipping" => "required",
				// "city_shipping" => "required",
				// "state_shipping" => "required",
				"country_shipping" => "required"

			]);


			if ($validator->fails()) {
				return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
				// return 0;
			}

			$customer_id = session('customer_id')?? $id;

			$user_id = $_REQUEST['user_id'] ?? CRUDBooster::myId();

			// customer add
			DB::table('drm_customers')
			->where([
				'id' => $customer_id,
			])
			->update([

				'full_name' => $_REQUEST['customer_full_name'],
				'company_name' => $_REQUEST['company_name'],
				'email' => $_REQUEST['email'],
				'city' => $_REQUEST['city'],
				'zip_code' => $_REQUEST['zip_code'],
				'state' => $_REQUEST['state'],
				'country' => $_REQUEST['country'],
				'phone' => $_REQUEST['phone'],
				'website' => $_REQUEST['website'],
				'currency' => $_REQUEST['currency'],
				'default_language' => $_REQUEST['default_language'],
				'address' => $_REQUEST['address'],
				'insert_type'=>$_REQUEST['insert_type'],
				'user_id'	=> $user_id,
				// 'vat_number' => $_REQUEST['vat_number'],
			]);

			// dd($customer->id);
			// sh1ipping
			DB::table('drm_customer_address')->where([
				'drm_customer_id' => $customer_id,
				'type' => 'shipping',
			])
			->update([
				'street' => $_REQUEST['street_shipping'],
				'city' => $_REQUEST['city_shipping'],
				'state' => $_REQUEST['state_shipping'],
				'zipcode' => $_REQUEST['zipcode_shipping'],
				'country' => $_REQUEST['country_shipping'],
			]);



			// billing
			if(isset($_REQUEST['is_same_address']))
			{
				DB::table('drm_customer_address')->where([
					'drm_customer_id' => $customer_id,
					'type' => 'billing',
				])
				->update([
					'street' => $_REQUEST['street_shipping'],
					'city' => $_REQUEST['city_shipping'],
					'state' => $_REQUEST['state_shipping'],
					'zipcode' => $_REQUEST['zipcode_shipping'],
					'country' => $_REQUEST['country_shipping'],
				]);
			}
			else{
				DB::table('drm_customer_address')->where([
					'drm_customer_id' => $customer_id,
					'type' => 'billing',
				])
				->update([
					'street' => $_REQUEST['street_billing'],
					'city' => $_REQUEST['city_billing'],
					'state' => $_REQUEST['state_billing'],
					'zipcode' => $_REQUEST['zipcode_billing'],
					'country' => $_REQUEST['country_billing'],
				]);
			}

			CRUDBooster::redirect(CRUDBooster::adminPath('drm_customers'), trans('Save seccessfull.'), 'success');
		}

		public function getMarge()
		{
			$data['customers'] = DB::table('drm_customers')->where('user_id',CRUDBooster::myId())->get();
			return view("admin.drm_customer.marge",$data);
		}

		// customer-details
		public function getCustomerDetails()
		{
			$data['customer'] = DB::table('drm_customers')->find($_REQUEST['customer_id']);
			$data['shipping'] = DB::table('drm_customer_address')->where([
				'drm_customer_id' => $_REQUEST['customer_id'],
				'type' => 'shipping'
			])->first();
			$data['billing'] = DB::table('drm_customer_address')->where([
				'drm_customer_id' => $_REQUEST['customer_id'],
				'type' => 'billing'
			])->first();
			return response()->json($data);
		}


		// --------------- update customer ajax ----------
		public function postUpdateUser()
		{
			// return response()->json($_REQUEST);

			// $validator = Validator::make($_REQUEST, [

			// 	"customer_first_name" => "required",
			// 	"customer_last_name" => "required",
			// 	"customer_full_name" => "required",
			// 	"company_name" => "required",
			// 	"phone" => "required",
			// 	"currency" => "required",
			// 	"default_language" => "required",
			// 	"city" => "required",
			// 	// "state" => "required",
			// 	"country" => "required",
			// 	"street_shipping" => "required",
			// 	"city_shipping" => "required",
			// 	// "state_shipping" => "required",
			// 	"country_shipping" => "required"

			// ]);


			// if ($validator->fails()) {
			// 	return response()->json($data, 500, $headers);
			// 	return response()->json($_REQUEST);
			// }

			if($_REQUEST['table'] == "customer_info")
			{
				DB::table("drm_customers")->where('id', $_REQUEST['customer_id'] )->update([
					$_REQUEST['field'] =>  $_REQUEST['value']
				]);
			}
			else if ($_REQUEST['table'] == "billing" || $_REQUEST['table'] == "shipping")
			{
				DB::table("drm_customer_address")->where( [
					'drm_customer_id' => $_REQUEST['customer_id'],
					'type' => $_REQUEST['table'],
				])->update([
					$_REQUEST['field'] =>  $_REQUEST['value']
				]);
			}

		}


	}

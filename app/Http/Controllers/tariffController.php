<?php

namespace App\Http\Controllers;

use App\Coupon;
use App\Enums\Apps;
use App\Models\Product\AnalysisProduct;
use App\NewOrder;
use App\User;
use Carbon\Carbon;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Http\Request;
use DB;
use App\DrmUserCreditAddLog;
use App\DrmUserCreditRemoveLog;
use App\DrmUserCredit;
use App\Enums\CreditType;
use App\Jobs\DestroyProduct;
use App\Services\Tariff\Credit\ChargeCredit;
use App\Services\Tariff\Credit\CreditService;
use App\Services\Tariff\Credit\RefillCredit;
use App\Services\TariffService;

class tariffController extends Controller
{

    private TariffService $tariffService;
    
    public function __construct(TariffService $tariff_service)
    {
        $this->tariffService = $tariff_service;
    }

    function drmUserCreditAdd($user_id, $credit, $message, $type, $status)
    {
        $insert_data = [
            'user_id' => $user_id,
            'credit' => $credit,
            'message' => $message,
            'type' => $type
        ];

        $insert_data['status'] = ($status == CreditType::CREDIT_ADD) ? CreditType::CREDIT_ADD  : CreditType::CREDIT_REMOVE;

        DrmUserCreditAddLog::create($insert_data);

        $message = '';

        if($status == CreditType::CREDIT_ADD){
            $message = 'Credit Addition Data Inserted';
        }else{
            $message = 'Credit Remove Data Inserted';
        }

        return $message;
    }

    function CreditUpdate($user_id, $credit, $flag)
    {
        $user_credit = DrmUserCredit::firstOrNew([
            'user_id' => $user_id
        ]);

        if(!empty($user_credit->credit)){
            if($flag == 'credit_add'){
                $user_credit->credit = $user_credit->credit + $credit;
            }else if($flag == 'credit_deduct'){
                $user_credit->credit = $user_credit->credit - $credit;
            }
        }else{
            $user_credit->credit = $credit;
        }

        $user_credit->save();

        $message = ($flag == 'credit_add') ? 'Credit Added' : 'credit_deduct';

        return $message;
    }

    function drmUserCreditRemove($user_id, $credit, $message, $type)
    {
        DrmUserCreditRemoveLog::create([
            'user_id' => $user_id,
            'credit' => $credit,
            'message' => $message,
            'type' => $type
        ]);

        return 'Credit Expense Log Inserted';
    }

    public function creditReduction($user_id, $count, $payPerAsYouGoRate, $type){  // if it costs 0.1 eur per unit when token isn't available, $payPerAsYouGoRate will be 0.1
        $tariff_available = 0;

        // $user_import_plan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);

        $dropmatix_acc = DB::table('cms_users')->where('id', $user_id)->value('id_cms_privileges');
        $status = \App\Enums\CreditType::CREDIT_REMOVE;

        // in_array($user_import_plan['plan'], ['Free', 'Trial']) || 
        
        if($dropmatix_acc == 10 || $user_id == 2455){
            // $this->drmUserCreditRemove($user_id, $count, 'product_translation_credit_deduct', $type);
            // $this->drmUserCreditAdd($user_id, $count, 'product_translation_credit_deduct', $type, $status);
            (new ChargeCredit)->charge($user_id, $count, \App\Services\Tariff\Credit\CreditType::PRODUCT_TRANSLATION_CREDIT_DEDUCT);

            $tariff_available = 1;
        }

        else{
            // $credit = DB::table('drm_user_credits')->where('user_id', $user_id)->first();

            // if($credit->credit >= $count){
                
            $remaining_credit = (new CreditService())->remainingCredit($user_id);
            if ($remaining_credit >= $count) {

                // $this->CreditUpdate($user_id, $count, 'credit_deduct');
                // $this->drmUserCreditAdd($user_id, $count, 'product_translation_credit_deduct', $type, $status);

                (new ChargeCredit)->charge($user_id, $count, \App\Services\Tariff\Credit\CreditType::PRODUCT_TRANSLATION_CREDIT_DEDUCT);

                $tariff_available = 1;
            }
            else{
                $top_up = DB::table('drm_tariff_balance')->where('user_id', $user_id)->first();

                if($top_up){
                    if($top_up->use_top_up == 1 && ($top_up->balance >= ($count * $payPerAsYouGoRate) ) ){
                        $balance = $top_up->balance - ($count * $payPerAsYouGoRate);

                        DB::table('drm_tariff_balance')->where('user_id', $user_id)->update([
                            'balance' => $balance
                        ]);

                        $tariff_available = 1;
                    }
                }
            }
        }

        return $tariff_available;
    }

    public function tariffAllDataDelete($user_id)
    {
        // 1st All Products Delete
        $product_ids = app('App\Services\DRMProductService')->getSelectedIds($user_id, []);

        $languageId = app('App\Services\UserService')->getProductCountry($user_id);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        foreach(array_chunk($product_ids, 500) as $chunkIds){
            DestroyProduct::dispatch($chunkIds, $user_id, $lang, $languageId);
        }

        // 2nd All MP Categories Delete
        app('App\Http\Controllers\Marketplace\MarketPlaceController')->customerBulkParentCategoryRemove($user_id);

        // 3rd All Channels Delete
        $this->tariffService->userChannelStatusUpdate($user_id);

        // 4th All Credits/Token Delete
        // $this->tariffService->creditDelete($user_id); //No credit will be delete from now 23-10-23

        if(request()->ajax()){
            return response()->json([
                'success' => true,
                'message' => __('Tariff related data deletion process has been started. It may take some time. Please try to purchase your desire plan after some time.')
            ], 200);
        }
    }

    public function remainCreditSet($user_id, $credit)
    {
        DrmUserCredit::where('user_id', $user_id)
        ->update([
            'credit' => $credit
        ]);
    }

    public function creditReset($user_id, $plan_start_date)
    {
        // $remaining_credit = $this->tariffService->getRemainingCredit($user_id, $plan_start_date);
        $remaining_credit = (new CreditService)->remainingCredit($user_id);

        // if(!empty($remaining_credit['total_credit_remain'])){
        //     $credit = $remaining_credit['total_credit_remain'];
        if (!empty($remaining_credit)) {
            $message = 'Credit reset';
            $type = \App\Enums\CreditType::CREDIT_RESET;
            $status = \App\Enums\CreditType::CREDIT_REMOVE;

            // $this->drmUserCreditAdd($user_id, $credit, $message, $type, $status);
            (new RefillCredit)->resetTariffCredit($user_id, (float) $remaining_credit, \App\Services\Tariff\Credit\CreditType::CREDIT_RESET);

            // $this->remainCreditSet($user_id, 0);
        }
    }

}

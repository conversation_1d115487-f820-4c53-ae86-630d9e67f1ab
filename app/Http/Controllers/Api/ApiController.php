<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use DB;
use Illuminate\Support\Facades\Mail;
use App\Jobs\NotifyDroptiendaProductStock;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Artisan;
use App\Services\OrderTracking\OrderTracking;
use App\Services\OrderTracking\OrderCancel;

use Exception;

use App\Models\Export\UniversalExport;
use App\NewOrder;
use App\Services\UniversalExportService;
use App\DropfunnelCustomerTag;
use App\Services\DRM\CampaignService;
use Illuminate\Support\Facades\Storage;
use App\Shop;

class ApiController extends \App\Http\Controllers\Controller
{

    public function sendTestCampaign(Request $request)
    {
        if($request->header('token') != 'dftkns6Tc') abort(404);
        $request->validate([
            'campaign' => 'required',
            'step' => 'nullable',
            'email' => 'required|email',
            'full_test' => 'nullable'
        ]);

        try {
           return app(CampaignService::class)->sendTestCampaign($request->input());
       } catch (Exception $e) {
            return response()->json(["messages" => $e->getMessage()], 402);
       }
    }

    public function saveImportOrdersFile(Request $request)
    {    
        try {
            $data = [];

            $filename = md5(\Str::random(6)) . '.' . 'txt';
            $filePath = '/import_orders/' . $request->user_id . '/' . date('Y-m');
    
            if (!file_exists(public_path($filePath))) {
                mkdir(public_path($filePath), 0777, true);
            }

            $write_handler = fopen(public_path($filePath) . '/' . $filename, "w");
            fwrite($write_handler, base64_decode($request->file));
            fclose($write_handler);
    
            $uploaded_file_path = $filePath . '/' . $filename;

            if ($uploaded_file_path != '') {
                // store uploaded file path
                $imported_pid = DB::table('import_orders_histories')->insertGetId([
                    'user_id' => $request->user_id,
                    'shop_id' => $request->shop_id,
                    'file_path' => $uploaded_file_path,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // fire the job
                if ($imported_pid) {
                    $data['processing'] = 1;
                    \App\Jobs\ChannelManager\ImportNewOrders::dispatch($imported_pid);
                }
            }

            return response()->json([
                'success' => true, 
                'data' => $data, 
            ]);
            
        } catch (Exception $e) {
            return response()->json(['success' => false, "messages" => $e->getMessage()], 402);
        }
    }


    public function notifyStockUpdate(Request $request)
    {
        $channel_product_id = $request->product_id;
        if(empty($channel_product_id)) return;

        \DB::table('dt_stock_requests')
        ->where('channel_product_id', '=', $channel_product_id)
        ->where('status', '<>', 0)
        ->select('id')
        ->get()
        ->each(function($item) {
            NotifyDroptiendaProductStock::dispatch($item->id);
        });
    }

    public function notifyUvpUpdate(Request $request)
    {
        $channel_product_id = (int)$request->product_id;
        $request_info = $request->all();
        $product_id = $request_info['product_id'];
        $user_id = $request_info['user_id'];
        $ean = DB::table('channel_products')->where('id',$channel_product_id)->value('ean');
        $offer_orders = DB::table('temp_offer_infos')->where('ean',$ean)->where('user_id',$user_id)->get();

        if(!empty($offer_orders)){
            foreach ($offer_orders as $offer){
                $order_info = json_decode($offer->order_info, true);
                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://drm.software/api/droptienda/offer/create',
//                    CURLOPT_URL => 'http://localhost:8000/api/droptienda/offer/create',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => array(
                        'name' => $order_info['name'],
                        'email' => $order_info['email'],
                        'phone' => $order_info['phone'],
                        'address' => $order_info['address'],
                        'country' => $order_info['country'],
                        'product_info[product_name]' => $order_info['product_info']['product_name'],
                        'product_info[description]' => $order_info['product_info']['description'],
                        'product_info[qty]' => $order_info['product_info']['qty'],
                        'product_info[rate]' => $order_info['product_info']['rate'],
                        'product_info[ean]' => $order_info['product_info']['ean'],
                        'offer_options' => $order_info['offer_options'],
                        'tax_rate' => $order_info['tax_rate'],
                        'zip_code' => $order_info['zip_code'],
                        'state' => $order_info['state'],
                        'uvp_request' => '1',
                        'user_id' => $user_id
                    ),
                ));
                curl_exec($curl);
                curl_close($curl);
                DB::table('temp_offer_infos')->where('ean',$order_info['product_info']['ean'])->where('user_id',$user_id)->delete();
            }
        }
    }


    public function mpOrderPlace(Request $request)
    {
        try{

            $req = $request->only(['token', 'order_id']);
            $token = $req['token'];
            if($token != 'd946qcyT6gd1rb5')
            {
                throw new \Exception('Invalid access!');
            }

            $order_id = $req['order_id'] ?? null;
            if(empty($order_id))
            {
                throw new \Exception('Invalid order!');
            }

            return app('App\Http\Controllers\AdminDrmAllOrdersController')->getOrderSupplier($order_id);

        }catch(\Exception $e) {
            return response()->json([
                'success' => false,
                "message" => $e->getMessage(),
            ], 400);
        }
    }


    public function mpOrderPlaceOnApi(Request $request)
    {
        try{

            $req = $request->only(['token', 'order_id']);
            $token = $req['token'];
            if($token != 'd946qcyT6gd1rb5')
            {
                throw new \Exception('Invalid access!');
            }

            $order_id = $req['order_id'] ?? null;
            if(empty($order_id))
            {
                throw new \Exception('Invalid order!');
            }

            return app('App\Http\Controllers\AdminDrmAllOrdersController')->sendApiOrder($order_id);

        }catch(\Exception $e) {
            return response()->json([
                'success' => false,
                "message" => $e->getMessage(),
            ], 400);
        }
    }



    public function createManualSupplierOrder(Request $request){
        try{

            $req = $request->only(['token', 'order_id']);
            $token = $req['token'];
            if($token != 'd946qcyT6gd1rb5')
            {
                throw new \Exception('Invalid access!');
            }

            $order_id = $req['order_id'] ?? null;
            if(empty($order_id))
            {
                throw new \Exception('Invalid order!');
            }

            $order = NewOrder::with('shop:id,lang,channel')->find($order_id);
            return app('App\Services\Marketplace\MPSupplierOrder')->createSuppliersOrders($order);

        }catch(\Exception $e) {
            return response()->json([
                'success' => false,
                "message" => $e->getMessage(),
            ], 400);
        }

    }

    public function dtStockRequest(Request $request)
    {
        try{

            $req = $request->only(['token', 'payload']);
            $token = $req['token'];
            if($token != 'd946gm34gd1rb5')
            {
                throw new \Exception('Invalid access!');
            }


            $payload = $req['payload'];
            $channel_product_id = $payload['channel_product_id'];
            $data = $payload['data'];

            if(empty($data['customer']))
            {
                throw new \Exception('Invalid customer data');
            }

            if(empty($data['product']))
            {
                throw new \Exception('Invalid product data');
            }

            $email = $data['customer']['email'];
            if(empty($email))
            {
                throw new \Exception('Invalid customer email');
            }

            $exists = \DB::table('dt_stock_requests')->where([
                'email' => $email,
                'channel_product_id' => $channel_product_id,
                'status' => 1,
            ])
            ->exists();

            if($exists) {
                return response()->json([
                    'success' => false,
                    "message" => "You have already a request!",
                ]);
            }

            $id = \DB::table('dt_stock_requests')->insertGetId([
                'email' => $email,
                'channel_product_id' => $channel_product_id,
                'status' => 1,
                'data' => json_encode($data),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            if($id)
            {
                try {
                    $email = $data['customer']['email'];
                    $name = $data['customer']['name'];
                    $user_id = $data['user_id'];

                    $customer_info = DB::table('new_customers')
                        ->where('user_id', $user_id)
                        ->where('email', $email)
                        ->first();

                    if (!empty($customer_info)) {
                        $customer_id = $customer_info->id;
                        DB::table('new_customers')
                            ->where('id', $customer_id)
                            ->where('email', $email)
                            ->update([
                                'full_name' => $name,
                            ]);
                    } else {
                        $customer_id = DB::table('new_customers')->insertGetId([
                            'full_name' => $name,
                            'email' => $email,
                            'insert_type' => '12',
                            'user_id' => $user_id,
                        ]);
                    }

                    $curl = curl_init();

                    curl_setopt_array($curl, array(
                        CURLOPT_URL => 'https://drm.software/api/droptienda/customer-tag/insert',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'POST',
                        CURLOPT_POSTFIELDS => array(
                            'tag_name' => $data['product']['name'],
                            'user_id' => $data['user_id'],
                            'customer_id' => $customer_id,
                            'insert_type' => '21'
                        ),
                    ));

                    $response = curl_exec($curl);

                    curl_close($curl);

            } catch (\Exception $e) {
                throw new \Exception('Failed to Insert Tag!');
            }



                $this->sendDroptiendaStockMail($id);
                //Create tag
                return response()->json([
                    'success' => true,
                    "message" => "Product request success",
                ]);
            }

            throw new \Exception('Failed to create product stock request!');

        }catch(\Exception $e) {
            return response()->json([
                'success' => false,
                "message" => $e->getMessage(),
            ], 400);
        }
    }




    public function sendDroptiendaStockMail($id, $slug = 'drm_dt_stock_mail_entry')
    {
        try {

            $stock_request = \DB::table('dt_stock_requests')->where('id', '=', $id)->where('status', '<>', 0)->select('id', 'status', 'data')->first();
            if(empty($stock_request)) return false;
            $stock_data = json_decode($stock_request->data, true);

            $user_id = $stock_data['user_id']; // User id
            $customer = $stock_data['customer'];
            $product = $stock_data['product'];
            $logo = \DB::table('drm_invoice_setting')->where('cms_user_id', $user_id)->orderBy('id', 'desc')->value('logo') ?? '';

            $tags = [
                'logo' => $logo,
                'customer_name' => $customer['name'],
                'company_name' => $customer['company_name'] ?? null,
                'customer_email' => $customer['email'],
                'product_name' => $product['name'],
                'product_ean' => $product['ean'],
                'product_url' => $product['url'],
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            $template = DRMParseDtProductStockTemplate($tags, $user_id, $slug);
            $data['email_to'] = $customer['email'];
            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new \Exception("Something Wrong! Offer email Not Sent!.");
            }

            $data['subject'] = $template['subject'];
            app('drm.mailer')->getMailer($user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                if (!empty($data['bcc'])) {
                    $messages->bcc($data['bcc']);
                }
            });

            if($slug == 'drm_dt_stock_mail') {
                \DB::table('dt_stock_requests')->where('id', '=', $id)->update(['status' => 0]);
            }


            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function mpToCoreSync()
    {
        \App\Jobs\Marketplace\FixCoreStocks::dispatch();
    }

    public function coreToChannelSync()
    {
        \App\Jobs\Marketplace\FixChannelStocks::dispatch();
    }

    //Order tracking
    public function orderTrackingNumberSend(Request $request, $id)
    {
        try {

            if($request->token != 'token345rt') throw new Exception('Invalid Token');

            $request->validate([
                'parcel_name' => 'required',
                'tracking_number' => 'required',
            ]);

            $payload = $request->only(['parcel_name', 'tracking_number']);

            app(OrderTracking::class)->tracking($id, $payload);

        }catch(Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function orderCancelApiSend(Request $request, $id)
    {
        try {

            if($request->token != 'token345rt') throw new Exception('Invalid Token');
            app(OrderCancel::class)->cancel($id);

        }catch(Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }


    public function syncUserFeeds(Request $request)
    {
        $feeds = UniversalExport::where([
            'user_id' => $request->user_id,
            'dt_feed' => 0,
          ])->get();

          foreach ($feeds as $feed)
          {
            app(UniversalExportService::class)->syncFeed($feed->user_id,$feed->id);
          }

          $dt_feeds = UniversalExport::where([
            'user_id' => $request->user_id,
            'dt_feed' => 1,
          ])->get();

          foreach ($dt_feeds as $dt_feed)
          {
            app(UniversalExportService::class)->syncDtFeed($feed->user_id,$dt_feed->id);
          }
    }
    public function droptiendaCustomerGroupTitle(Request $request){

        $customer_id = DB::table('new_customers')->where('user_id', $request->user_id)->where('email', $request->email)->value('id');

        if(empty($customer_id)) return;

        $group = $request->type == 'dt_manual' ? 4 : 26;

        if($group == 26)
        {
            DB::table('dropfunnel_customer_tags')->where('customer_id', $customer_id)->where('insert_type', 26)->delete();
        }
        
        $res = DropfunnelCustomerTag::insertTag($request->group_name, $request->user_id, $customer_id, $group);
        return ['data', $customer_id, $res, $request->only(['user_id', 'email'])];
    }
    
}
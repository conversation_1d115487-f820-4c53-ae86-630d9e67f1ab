<?php namespace App\Http\Controllers;

	use Session;
	use DB;
	use CRU<PERSON><PERSON>ooster;
	use Illuminate\Support\Facades\Storage;
	use File;
	use ZipArchive;
	use Maatwebsite\Excel\Facades\Excel;
	use App\Http\Controllers\AdminDrmImportsController;
	use Illuminate\Support\Facades\Cache;
	use App\UpcomingInvoice;
	use App\NewOrder;
	use App\User;
	use Carbon\Carbon;
	use Illuminate\Support\Str;
	use SoapBox\Formatter\Formatter;
	use Illuminate\Support\Facades\Validator;
	use PhpOffice\PhpSpreadsheet\IOFactory;
	use Request;
	use Illuminate\Support\Facades\Route;
	use Illuminate\Support\LazyCollection;
	use Toastr;
	use AppStore;
	use App\Jobs\AccountingArchiveJob;
	use App\Exports\InvoicesExport;
	
	class AdminUpcomingInvoicesBasicController extends \crocodicstudio\crudbooster\controllers\CBController {
		
	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "invoice_number";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = true;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_filter = true;
			$this->button_import = true;
			$this->button_export = false;
			$this->table = "upcoming_invoices";
			$this->table_statement = "statements";

			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Supplier","name"=>"delivery_company_id","join"=>"delivery_companies,name"];
			$this->col[] = ["label"=>"Invoice Number","name"=>"invoice_number"];
			$this->col[] = ["label"=>"Date","name"=>"date"];
			$this->col[] = ["label"=>"Due Date","name"=>"due_date"];
			$this->col[] = ["label"=>"Category","name"=>"category"];
			$this->col[] = ["label"=>"Status","name"=>"status"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			$this->form[] = ['label'=>'Supplier','name'=>'delivery_company_id','type'=>'select2','validation'=>'required|min:1|max:255','width'=>'col-sm-10','datatable'=>'delivery_companies,name'];
			$this->form[] = ['label'=>'Invoice Number','name'=>'invoice_number','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Date','name'=>'date','type'=>'datetime','validation'=>'required|date_format:Y-m-d','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Due Date','name'=>'due_date','type'=>'datetime','validation'=>'date_format:Y-m-d','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Type Of Issue','name'=>'type_of_issue','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Category','name'=>'category','type'=>'text','validation'=>'min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Status','name'=>'status','type'=>'text','validation'=>'min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Amount','name'=>'amount','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Total Amount','name'=>'total_amount','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Tax','name'=>'tax','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Description','name'=>'description','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
			# END FORM DO NOT REMOVE THIS LINE

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	//Your code here
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
			$postdata['user_id']=CRUDBooster::myId();
			Validator::make($_REQUEST, [
				'status' => 'required',
				'delivery_company_id' => 'required',
				'invoice_number' => 'required',
				'date' => 'required',
				'due_date' => 'nullable|required_if:status,unpaid',
				'tax' => 'required',
				'amount' => 'required',
				'file.*' => 'required|mimes:jpeg,bmp,png,gif,svg,pdf',
				'description' => 'nullable|string|min:5|max:5000',
			],[
				'delivery_company_id.required' => 'Supplier required',
			]
			)->validate();
			if($postdata['tax']){
				$postdata['tax']= removeCommaPrice($postdata['tax']);
			}
			if($postdata['amount']){
				$postdata['amount']=removeCommaPrice($postdata['amount']);
			}
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
			

			$allowedfileExtension=['pdf','jpg','png','jpeg','PDF','JPG','PNG','JPEG'];
			if(is_array(request()->file)){
				foreach(request()->file as $file){
					if(!in_array($file->getClientOriginalExtension(),$allowedfileExtension)){
						continue;
					}
					$rand=Str::random(40);
					$extension = $file->extension();
        			$path = $file->storeAs('storage/invoice_files',$rand.'.'.$extension, ['visibility'=>'public', 'disk'=>'spaces']);
					$file_path = Storage::disk('spaces')->url($path);
					DB::table('upcoming_invoice_documents')->insert([
						"upcoming_invoice_id"=>$id,
						"file"=>$file_path
					]);
				}
			}
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
			$postdata['user_id']=CRUDBooster::myId();
			Validator::make($_REQUEST, [
				'status' => 'required',
				'delivery_company_id' => 'required',
				'invoice_number' => 'required',
				'date' => 'required',
				'due_date' => 'nullable|required_if:status,unpaid',
				'tax' => 'required',
				'amount' => 'required',
				'file.*' => 'nullable|mimes:jpeg,bmp,png,gif,svg,pdf',
				'description' => 'nullable|string|min:5|max:5000',
			],[
				'delivery_company_id.required' => 'Supplier required',
			]
			)->validate();
			if($postdata['tax']){
				$postdata['tax']= removeCommaPrice($postdata['tax']);
			}
			if($postdata['amount']){
				$postdata['amount']=removeCommaPrice($postdata['amount']);
			}
		}
		public function getDeleteFile($id){
			$data= DB::table('upcoming_invoice_documents')->where('id',$id)->delete();
		}

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
			if(request()->delete_f_lists && is_array(request()->delete_f_lists) ){
				foreach (request()->delete_f_lists as $file_id) {
					DB::table('upcoming_invoice_documents')->where('id', $file_id)->where('upcoming_invoice_id', $id)->delete();
				}
			}
	        $allowedfileExtension=['pdf','jpg','png','jpeg','PDF','JPG','PNG','JPEG'];
			if(is_array(request()->file)){
				foreach(request()->file as $file){
					if(!in_array($file->getClientOriginalExtension(),$allowedfileExtension)){
						continue;
					}
					$rand=Str::random(40);
					$extension = $file->extension();
					$path = $file->storeAs('storage/invoice_files',$rand.'.'.$extension, ['visibility'=>'public', 'disk'=>'spaces']);
					$file_path = Storage::disk('spaces')->url($path);
					DB::table('upcoming_invoice_documents')->insert([
						"upcoming_invoice_id"=>$id,
						"file"=>$file_path
					]);
				}
			}
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {

			DB::table("upcoming_invoice_documents")->where('upcoming_invoice_id',$id)->delete();
		}


		public function getIndex()
		{
			$basic_check  = AppStore::CheckAppPurchaseBoolean('12');
		
			if(!$basic_check[0]){
				CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
			}else{
				$app_details = $basic_check[1];
			}
			
			$q=request()->q;
			$from=request()->from;
			$to=request()->to;
			$todate=$to;
			// $to=Carbon\Carbon::parse($to)->addDay(1);
			$category=request()->category;
			$supplier=request()->supplier;
			$amount = 0;
	  
			
			$page="all";
			
			if(request()->p!=null){
			  $page=request()->p;
			}
			$due_invoices=UpcomingInvoice::where('status','unpaid')->where('user_id',CRUDBooster::myId())->whereDate('due_date','<',date('Y-m-d'))->orderBy("date","desc");
	  
			if($q!=null){
			  $due_invoices->where('invoice_number','like',"%".$q."%");
			}
	  
			if($from!=null ){
			  if($to==null){
				$due_invoices->whereBetween('date',[$from,now()]);
			  }else{
				$due_invoices->whereBetween('date',[$from,$to]);
			  }
			}
	  
			if($category!=null){
			  $due_invoices->where("category",$category);
			}
	  
			if($supplier!=null){
			  $due_invoices->where("delivery_company_id",$supplier);
			}
	  
			$due_invoices=$due_invoices->get();
			switch($page){
				case "due":
				  	$invoices=$due_invoices;
				 	break;

				case "all_incoming":

					$invoices=UpcomingInvoice::where('user_id',CRUDBooster::myId())->orderBy("date","desc");
					if($q!=null){
					$invoices->where('invoice_number','like',"%".$q."%");
					}

					if($from!=null )
					{
					if($to==null)
					{
						$invoices->whereBetween('date',[$from,now()]);
					}
					else
					{
						$invoices->whereBetween('date',[$from,$to]);
					}
					}

					if($category!=null){
					$invoices->where("category",$category);
					}
					if($supplier!=null){
					$invoices->where("delivery_company_id",$supplier);
					}

					$amount += $invoices->sum('amount');				
					$invoices=$invoices->get();

					break;
				default:
					$invoices=UpcomingInvoice::where('user_id',CRUDBooster::myId())->orderBy("date","desc");
					$orders=NewOrder::where('cms_user_id',CRUDBooster::myId())->orderBy("order_date","desc");
					
					if($q!=null){
						$orders->where('invoice_number','like',"%".$q."%");
						$invoices->where('invoice_number','like',"%".$q."%");
					}
		
					// if($quarter!=null ){
					// 	$invoices->whereBetween('date',[date('Y').'-'.$firstMonth.'-01',date('Y').'-'.$lastMonth.'-'.$lastDayOfMonth]);
					// 	$orders->whereBetween('order_date',[date('Y').'-'.$firstMonth.'-01',date('Y').'-'.$lastMonth.'-'.$lastDayOfMonth]);
						
					// }else{
						if($from!=null ){
							if($to==null){
								
								$orders->whereBetween('order_date',[$from,now()]);
								$invoices->whereBetween('date',[$from,now()]);
							}else{
							
								$orders->whereBetween('order_date',[$from,$to]);
								$invoices->whereBetween('date',[$from,$to]);
							}
						}
					// }
		
					if($category!=null){$invoices->where("category",$category);}
					
					if($supplier!=null){$invoices->where("delivery_company_id",$supplier);}
					if($supplier || $category){
						$orders->where('id', '<', 0);
					}
					$amount += $invoices->sum('amount');
					$amount += $orders->sum('total');
					$orders = $orders->get();					
					$invoices = $invoices->get();

					$orCount=0; $invCount=0;
            	break;
			}
			return view("admin.accounting.basic.index",compact('q','from','to','today','category','supplier','due_invoices','page','invoices','app_details', 'orders', 'orCount', 'invCount', 'amount'));
		}
	
		public function getDownload()
		{
			$basic_check  = AppStore::CheckAppPurchaseBoolean('12');
			if(!$basic_check[0]){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access")); }

			$user = User::find(CRUDBooster::myId());
			$q=request()->q;
			$from=request()->from;
			$to=request()->to;
			// $to=\Carbon\Carbon::parse($to)->addDay(1);
			$category=request()->category;
			$supplier=request()->supplier;
			$page=request()->p;

			if($page==null)
			{
				$page="all";
			}
			if($page=="all_incoming" || $page=="due")
			{
				$invoices=UpcomingInvoice::where('user_id',CRUDBooster::myId());
				if($page=="due")
				{
					$invoices->where('status','unpaid')->whereDate('due_date','<',date('Y-m-d'));
				}
				if($q!=null)
				{
					$invoices->where('invoice_number','like',"%".$q."%");
				}

				if($from!=null )
				{
					if($to==null)
					{
					  $invoices->whereBetween('date',[$from,now()]);
					}
					else
					{
					  $invoices->whereBetween('date',[$from,$to]);
					}
				}

				if($category!=null)
				{
					$invoices->where("category",$category);
				}

				if($supplier!=null)
				{
					$invoices->where("delivery_company_id",$supplier);
				}

				$invoices=$invoices->pluck('id')->toArray();

				if(count($invoices)){
					AccountingArchiveJob::dispatch($user, ['order' => [], 'invoice' => $invoices ])->onQueue('long-running-queue');
					return redirect()->back()->with(['message' => 'Archive processing! After completing the process, we sent you notification!', 'message_type' => 'success']);
				}else{
					return redirect()->back()->with(['message' => 'Sorry! No files to archive.', 'message_type' => 'warning']);
				}
			}
			elseif($page=='ongoing')
			{
				$orders=NewOrder::where('cms_user_id',CRUDBooster::myId())->orderBy("id","desc");
				if($q!=null)
				{
					$orders->where('invoice_number','like',"%".$q."%");
				}
					  
				if($from!=null )
				{
					if($to==null)
					{
						$orders->whereBetween('order_date',[$from,now()]);
					}
					else
					{
						$orders->whereBetween('order_date',[$from,$to]);
					}
				}

				$orders = ($supplier || $category)? $orders->where('id', '<', 0)->pluck('id')->toArray() : $orders->pluck('id')->toArray();
				if(count($orders)){
					AccountingArchiveJob::dispatch($user, ['order' => $orders, 'invoice' => [] ])->onQueue('long-running-queue');
					return redirect()->back()->with(['message' => 'Archive processing! After completing the process, we sent you notification!', 'message_type' => 'success']);
				}else{
					return redirect()->back()->with(['message' => 'Sorry! No files to archive.', 'message_type' => 'warning']);
				}
			}
			elseif($page=="all")
			{
				$invoices=UpcomingInvoice::where('user_id',CRUDBooster::myId());
				$orders=NewOrder::where('cms_user_id',CRUDBooster::myId())->orderBy("id","desc");
				if($q!=null)
				{
					$orders->where('invoice_number','like',"%".$q."%");
					$invoices->where('invoice_number','like',"%".$q."%");
				}

				if($from!=null )
				{
					if($to==null)
					{
						$invoices->whereBetween('date',[$from,now()]);
						$orders->whereBetween('order_date',[$from,now()]);
					}
					else
					{
						$orders->whereBetween('order_date',[$from,$to]);
						$invoices->whereBetween('date',[$from,$to]);
					}
				}

				if($category!=null)
				{
					$invoices->where("category",$category);
				}

				if($supplier!=null)
				{
					$invoices->where("delivery_company_id",$supplier);
				}

				$invoices=$invoices->pluck('id')->toArray();

				$orders = ($supplier || $category)? $orders->where('id', '<', 0)->pluck('id')->toArray() : $orders->pluck('id')->toArray();
				if(count($orders) || count($invoices) ){
					AccountingArchiveJob::dispatch($user, ['order' => $orders, 'invoice' => $invoices ])->onQueue('long-running-queue');
					return redirect()->back()->with(['message' => 'Archive processing! After completing the process, we sent you notification!', 'message_type' => 'success']);
				}else{
					return redirect()->back()->with(['message' => 'Sorry! No files to archive.', 'message_type' => 'warning']);
				}
			}

			// return response()->download(public_path('invoice_files/'.$fileName))->deleteFileAfterSend(true);
		}


		public function postAjaxOcr()
		{
			$file =request()->data;
			$data=[
				'apikey'=>'e6d34f8c7512bccfc900fe3e556a3202',
                      'input'=>'base64',
                      'file'=>$file,
                      'filename'=>'ocr.pdf',
                      'outputformat'=>'txt',
			];
			$data=json_encode($data);
			$ch = curl_init('https://api.convertio.co/convert');
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);;


			curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
			$output = curl_exec($ch);
			$info = curl_getinfo($ch);
			curl_close($ch);
			$res=json_decode($output);
			
			if(!empty($res->data->id)){
				$fileData=file_get_contents("https://api.convertio.co/convert/".$res->data->id."/dl/base64");
				return $fileData;
			}
			
			abort(400);
		}


		private function generate_invoice_pdf($order_id)
        {
            $data = [];
            $data['page_title'] = 'Invoice Details';
            
            $order = NewOrder::find($order_id);
            $data['order'] = $order;
            $data['product_list'] = json_decode($order->cart);
            $data['customer'] = $order->customer;
            $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->first();
            $pdf_path = 'storage/order_invoice_new/order'.$data['order']->id.'.pdf';

            $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439]))? 'admin.invoice.daily' : 'admin.invoice.general';
            $pdf=\PDF::loadView($pdf_view, $data)->setWarnings(false)->save($pdf_path);
            return $pdf_path;
		}
		
		public function getAdd()
		{
			$basic_check  = AppStore::CheckAppPurchaseBoolean('12');
			if(!$basic_check[0]){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access")); }
			
			return view("admin.accounting.basic.upcomming_invoice");
		}

		public function getEdit($id)
		{
			$basic_check  = AppStore::CheckAppPurchaseBoolean('12');
			if(!$basic_check[0]){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access")); }

			$invoice= UpcomingInvoice::where('id',$id)->first();
			return view("admin.accounting.basic.upcomming_invoice_edit",compact("invoice"));
		}

		public function getDetail($id)
		{
			$basic_check  = AppStore::CheckAppPurchaseBoolean('12');
			if(!$basic_check[0]){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access")); }
			$invoice=UpcomingInvoice::with('invoice_category')->where('id',$id)->first();
			return view("admin.accounting.basic.upcomming_invoice_detail",compact('invoice'));
		}

		public function getExcel()
		{
			$basic_check  = AppStore::CheckAppPurchaseBoolean('12');
			if(!$basic_check[0]){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access")); }

			$user = User::find(CRUDBooster::myId());
			$q=request()->q;
			$from=request()->from;
			$to=request()->to;
			// $to=\Carbon\Carbon::parse($to)->addDay(1);
			$category=request()->category;
			$supplier=request()->supplier;
			$page=request()->p;

			if($page==null)
			{
				$page="all";
			}
			if($page=="all_incoming" || $page=="due")
			{
				$invoices=UpcomingInvoice::where('user_id',CRUDBooster::myId());
				if($page=="due")
				{
					$invoices->where('status','unpaid')->whereDate('due_date','<',date('Y-m-d'));
				}
				if($q!=null)
				{
					$invoices->where('invoice_number','like',"%".$q."%");
				}

				if($from!=null )
				{
					if($to==null)
					{
					  $invoices->whereBetween('date',[$from,now()]);
					}
					else
					{
					  $invoices->whereBetween('date',[$from,$to]);
					}
				}

				if($category!=null)
				{
					$invoices->where("category",$category);
				}

				if($supplier!=null)
				{
					$invoices->where("delivery_company_id",$supplier);
				}

				$invoices=$invoices->pluck('id')->toArray();

				if(count($invoices)){
					return $this->invoiceExcel( $user, ['order' => $orders, 'invoice' => $invoices ] );
				}else{
					return redirect()->back()->with(['message' => 'Sorry! No files to download.', 'message_type' => 'warning']);
				}
			}
			elseif($page=='ongoing')
			{
				$orders=NewOrder::where('cms_user_id',CRUDBooster::myId())->orderBy("id","desc");
				if($q!=null)
				{
					$orders->where('invoice_number','like',"%".$q."%");
				}
					  
				if($from!=null )
				{
					if($to==null)
					{
						$orders->whereBetween('order_date',[$from,now()]);
					}
					else
					{
						$orders->whereBetween('order_date',[$from,$to]);
					}
				}

				$orders = ($supplier || $category)? $orders->where('id', '<', 0)->pluck('id')->toArray() : $orders->pluck('id')->toArray();
				if(count($orders)){
					return $this->invoiceExcel( $user, ['order' => $orders, 'invoice' => $invoices ] );
				}else{
					return redirect()->back()->with(['message' => 'Sorry! No files to download.', 'message_type' => 'warning']);
				}
			}
			elseif($page=="all")
			{
				$invoices=UpcomingInvoice::where('user_id',CRUDBooster::myId());
				$orders=NewOrder::where('cms_user_id',CRUDBooster::myId())->orderBy("id","desc");
				if($q!=null)
				{
					$orders->where('invoice_number','like',"%".$q."%");
					$invoices->where('invoice_number','like',"%".$q."%");
				}

				if($from!=null )
				{
					if($to==null)
					{
						$invoices->whereBetween('date',[$from,now()]);
						$orders->whereBetween('order_date',[$from,now()]);
					}
					else
					{
						$orders->whereBetween('order_date',[$from,$to]);
						$invoices->whereBetween('date',[$from,$to]);
					}
				}

				if($category!=null)
				{
					$invoices->where("category",$category);
				}

				if($supplier!=null)
				{
					$invoices->where("delivery_company_id",$supplier);
				}

				$invoices=$invoices->pluck('id')->toArray();

				$orders = ($supplier || $category)? $orders->where('id', '<', 0)->pluck('id')->toArray() : $orders->pluck('id')->toArray();
				if(count($orders) || count($invoices) ){
					return $this->invoiceExcel( $user, ['order' => $orders, 'invoice' => $invoices ] );
				}else{
					return redirect()->back()->with(['message' => 'Sorry! No files to download.', 'message_type' => 'warning']);
				}
			}
		}

        private function invoiceExcel($user, $data = []){

			if(Excel::store(new InvoicesExport($data), 'upcomming/invoices_'.CRUDBooster::myId().'.xlsx')){
			    header('Content-type: application/xlsx');
			    header('Content-Disposition: attachment; filename="invoices.xlsx"');
			    readfile(storage_path('app/upcomming/invoices_'.CRUDBooster::myId().'.xlsx'));
			}
        }
	}
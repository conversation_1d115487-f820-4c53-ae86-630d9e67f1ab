<?php

namespace App\Http\Controllers;

use Exception;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request as LaravelRequest;
use Request;
use Illuminate\Support\Facades\DB;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\Validator;
use crocodicstudio\crudbooster\helpers\CB;
use App\NewCustomer;
use App\NewOrder;
use App\User;
use App\CustomerTag;
use App\DrmUserLockUnlock;
use Illuminate\Support\Str;
use AppStore;
use App\Traits\ProjectShare;
use Carbon\Carbon;

use App\DropfunnelCustomerTag;
use App\Enums\Channel;
use App\Jobs\DtUserLockUnlockJob;

class AdminDrmAllCustomersController extends \crocodicstudio\crudbooster\controllers\CBController
{

    use ProjectShare;

    public function cbInit()
    {

        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "id";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = true;
        $this->button_bulk_action = true;
        $this->button_action_style = "button_icon";
        $this->button_add = true;
        $this->button_edit = true;
        $this->button_delete = false;
        $this->button_detail = true;
        $this->button_show = false;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "new_customers";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
        $this->col[] = ["label" => "Customer ID", "name" => "id"];
        $this->col[] = ["label" => "Name", "name" => "full_name"];
        $this->col[] = ["label" => "Company Name", "name" => "company_name"];
        $this->col[] = ["label" => "Vat Number", "name" => "vat_number"];
        $this->col[] = ["label" => "Phone", "name" => "phone"];
        $this->col[] = ["label" => "Email", "name" => "email"];
        $this->col[] = ["label" => "Currency", "name" => "currency"];
        $this->col[] = ["label" => "Country", "name" => "country"];
        if (CRUDBooster::isSuperadmin()) {
            $this->col[] = ["label" => "Payment Status", "name" => "status"];
        }
        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];

        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ["label"=>"Full Name","name"=>"full_name","type"=>"text","required"=>TRUE,"validation"=>"required|string|min:3|max:70","placeholder"=>"You can only enter the letter only"];
        //$this->form[] = ["label"=>"Company Name","name"=>"company_name","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Vat Number","name"=>"vat_number","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Phone","name"=>"phone","type"=>"number","required"=>TRUE,"validation"=>"required|numeric","placeholder"=>"You can only enter the number only"];
        //$this->form[] = ["label"=>"Website","name"=>"website","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Email","name"=>"email","type"=>"email","required"=>TRUE,"validation"=>"required|min:1|max:255|email|unique:new_customers","placeholder"=>"Please enter a valid email address"];
        //$this->form[] = ["label"=>"Currency","name"=>"currency","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Default Language","name"=>"default_language","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Address","name"=>"address","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"City","name"=>"city","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"State","name"=>"state","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Zip Code","name"=>"zip_code","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Country","name"=>"country","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Note","name"=>"note","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Shipping","name"=>"shipping","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Billing","name"=>"billing","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Insert Type","name"=>"insert_type","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"User Id","name"=>"user_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"user,id"];
        //$this->form[] = ["label"=>"Status","name"=>"status","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        # OLD END FORM

        /*
        | ----------------------------------------------------------------------
        | Sub Module
        | ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key 	  = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
        |
        */
        $this->sub_module = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Action Button / Menu
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
        | @icon        = Font awesome class icon. e.g : fa fa-bars
        | @color 	   = Default is primary. (primary, warning, succecss, info)
        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
        |
        */
        $this->addaction = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon 	   = Icon from fontawesome
        | @name 	   = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();


        /*
        | ----------------------------------------------------------------------
        | Add alert message to this module at overheader
        | ----------------------------------------------------------------------
        | @message = Text of message
        | @type    = warning,success,danger,info
        |
        */
        $this->alert = array();


        /*
        | ----------------------------------------------------------------------
        | Add more button to header button
        | ----------------------------------------------------------------------
        | @label = Name of button
        | @url   = URL Target
        | @icon  = Icon from Awesome.
        |
        */
        $this->index_button = array();


        /*
        | ----------------------------------------------------------------------
        | Customize Table Row Color
        | ----------------------------------------------------------------------
        | @condition = If condition. You may use field alias. E.g : [id] == 1
        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
        |
        */
        $this->table_row_color = array();


        /*
        | ----------------------------------------------------------------------
        | You may use this bellow array to add statistic at dashboard
        | ----------------------------------------------------------------------
        | @label, @count, @icon, @color
        |
        */
        $this->index_statistic = array();


        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
        */
        $this->script_js = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code before index table
        | ----------------------------------------------------------------------
        | html code to display it before index table
        | $this->pre_index_html = "<p>test</p>";
        |
        */
        $this->pre_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code after index table
        | ----------------------------------------------------------------------
        | html code to display it after index table
        | $this->post_index_html = "<p>test</p>";
        |
        */
        $this->post_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include Javascript File
        | ----------------------------------------------------------------------
        | URL of your javascript each array
        | $this->load_js[] = asset("myfile.js");
        |
        */
        $this->load_js = array();


        /*
        | ----------------------------------------------------------------------
        | Add css style at body
        | ----------------------------------------------------------------------
        | css code in the variable
        | $this->style_css = ".style{....}";
        |
        */
        $this->style_css = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include css File
        | ----------------------------------------------------------------------
        | URL of your css each array
        | $this->load_css[] = asset("myfile.css");
        |
        */
        $this->load_css = array();


    }


    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here

    }


    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
    public function hook_query_index(&$query)
    {
        if (!CRUDBooster::isSuperadmin()) {
            $query->where('new_customers.user_id', CRUDBooster::myId());
        }

        //Custom Searching If user search by tag using #
        if (Request::get('q')) {
            $searchText = Request::get('q');
            $tagSearchChecker = Str::contains($searchText, '#');
            $filteredSearchText = str_replace('#', '', $searchText);

            if ($tagSearchChecker) { //if string contains '#' search with tag will be executed
                $query->join('customer_tags', 'customer_tags.customer_id', '=', 'new_customers.id')
                    ->whereRaw('JSON_CONTAINS(customer_tags.tags, \'{"label": "' . $filteredSearchText . '"}\')');
            }
        }

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    public function hook_row_index($column_index, &$column_value)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */
    public function hook_before_add(&$postdata)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_before_delete($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
        //Your code here

    }

    //suggesting tag when searching for customer
    public function autoCompleteSearch(Request $request)
    {

        if ($request::ajax()) {

            $all_data = $request::all();
            $tagSearchChecker = Str::contains($all_data['tag'], '#');
            $filteredSearchText = str_replace('#', '', $all_data['tag']);


            if (CRUDBooster::isSuperadmin()) {
                $data = CustomerTag::where('tags', 'like', '%' . $filteredSearchText . '%')->get();
            } else {
                $data = CustomerTag::where('user_id', CRUDBooster::myId())->where('tags', 'like', '%' . $filteredSearchText . '%')->get();
            }

            // whereRaw('JSON_CONTAINS(tags, "LIKE", \'{"label": "'.$filteredSearchText.'"}\'."%")')->get();
            $output = [];

            if (count($data) > 0) {

                foreach ($data as $manual_tag) {

                    $man_tag = json_decode($manual_tag->tags, TRUE);

                    foreach ($man_tag as $mn_tag) {

                        if (Str::contains($mn_tag['label'], $filteredSearchText) && !in_array('#' . $mn_tag['label'], $output)) {
                            $output[] = '#' . $mn_tag['label'];
                        }
                    }

                }

            }

            return response()->json($output);

        }
    }


    public function getIndex()
    {
        $this->cbLoader();

        $module = CRUDBooster::getCurrentModule();

        if (!CRUDBooster::isView() && $this->global_privilege == false) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_view', ['module' => $module->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        if (Request::get('parent_table')) {
            $parentTablePK = CB::pk(g('parent_table'));
            $data['parent_table'] = DB::table(Request::get('parent_table'))->where($parentTablePK, Request::get('parent_id'))->first();
            if (Request::get('foreign_key')) {
                $data['parent_field'] = Request::get('foreign_key');
            } else {
                $data['parent_field'] = CB::getTableForeignKey(g('parent_table'), $this->table);
            }

            if ($parent_field) {
                foreach ($this->columns_table as $i => $col) {
                    if ($col['name'] == $parent_field) {
                        unset($this->columns_table[$i]);
                    }
                }
            }
        }

        $data['table'] = $this->table;
        $data['table_pk'] = CB::pk($this->table);
        $data['page_title'] = $module->name;
        $data['page_description'] = trans('crudbooster.default_module_description');
        $data['date_candidate'] = $this->date_candidate;
        $data['limit'] = $limit = (Request::get('limit')) ? Request::get('limit') : $this->limit;

        $tablePK = $data['table_pk'];
        $table_columns = CB::getTableColumns($this->table);
        $result = DB::table($this->table)->select(DB::raw($this->table . "." . $this->primary_key));

        if (Request::get('parent_id')) {
            $table_parent = $this->table;
            $table_parent = CRUDBooster::parseSqlTable($table_parent)['table'];
            $result->where($table_parent . '.' . Request::get('foreign_key'), Request::get('parent_id'));
        }

        $this->hook_query_index($result);

        if (in_array('deleted_at', $table_columns)) {
            $result->where($this->table . '.deleted_at', null);
        }

        $alias = [];
        $join_alias_count = 0;
        $join_table_temp = [];
        $table = $this->table;
        $columns_table = $this->columns_table;
        foreach ($columns_table as $index => $coltab) {

            $join = @$coltab['join'];
            $join_where = @$coltab['join_where'];
            $join_id = @$coltab['join_id'];
            $field = @$coltab['name'];
            $join_table_temp[] = $table;

            if (!$field) {
                continue;
            }

            if (strpos($field, ' as ') !== false) {
                $field = substr($field, strpos($field, ' as ') + 4);
                $field_with = (array_key_exists('join', $coltab)) ? str_replace(",", ".", $coltab['join']) : $field;
                $result->addselect(DB::raw($coltab['name']));
                $columns_table[$index]['type_data'] = 'varchar';
                $columns_table[$index]['field'] = $field;
                $columns_table[$index]['field_raw'] = $field;
                $columns_table[$index]['field_with'] = $field_with;
                $columns_table[$index]['is_subquery'] = true;
                continue;
            }

            if (strpos($field, '.') !== false) {
                $result->addselect($field);
            } else {
                $result->addselect($table . '.' . $field);
            }

            $field_array = explode('.', $field);

            if (isset($field_array[1])) {
                $field = $field_array[1];
                $table = $field_array[0];
            } else {
                $table = $this->table;
            }

            if ($join) {

                $join_exp = explode(',', $join);

                $join_table = $join_exp[0];
                $joinTablePK = CB::pk($join_table);
                $join_column = $join_exp[1];
                $join_alias = str_replace(".", "_", $join_table);

                if (in_array($join_table, $join_table_temp)) {
                    $join_alias_count += 1;
                    $join_alias = $join_table . $join_alias_count;
                }
                $join_table_temp[] = $join_table;

                $result->leftjoin($join_table . ' as ' . $join_alias, $join_alias . (($join_id) ? '.' . $join_id : '.' . $joinTablePK), '=', DB::raw($table . '.' . $field . (($join_where) ? ' AND ' . $join_where . ' ' : '')));
                $result->addselect($join_alias . '.' . $join_column . ' as ' . $join_alias . '_' . $join_column);

                $join_table_columns = CRUDBooster::getTableColumns($join_table);
                if ($join_table_columns) {
                    foreach ($join_table_columns as $jtc) {
                        $result->addselect($join_alias . '.' . $jtc . ' as ' . $join_alias . '_' . $jtc);
                    }
                }

                $alias[] = $join_alias;
                $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table, $join_column);
                $columns_table[$index]['field'] = $join_alias . '_' . $join_column;
                $columns_table[$index]['field_with'] = $join_alias . '.' . $join_column;
                $columns_table[$index]['field_raw'] = $join_column;

                @$join_table1 = $join_exp[2];
                @$joinTable1PK = CB::pk($join_table1);
                @$join_column1 = $join_exp[3];
                @$join_alias1 = $join_table1;

                if ($join_table1 && $join_column1) {

                    if (in_array($join_table1, $join_table_temp)) {
                        $join_alias_count += 1;
                        $join_alias1 = $join_table1 . $join_alias_count;
                    }

                    $join_table_temp[] = $join_table1;

                    $result->leftjoin($join_table1 . ' as ' . $join_alias1, $join_alias1 . '.' . $joinTable1PK, '=', $join_alias . '.' . $join_column);
                    $result->addselect($join_alias1 . '.' . $join_column1 . ' as ' . $join_column1 . '_' . $join_alias1);
                    $alias[] = $join_alias1;
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table1, $join_column1);
                    $columns_table[$index]['field'] = $join_column1 . '_' . $join_alias1;
                    $columns_table[$index]['field_with'] = $join_alias1 . '.' . $join_column1;
                    $columns_table[$index]['field_raw'] = $join_column1;
                }
            } else {

                if (isset($field_array[1])) {
                    $result->addselect($table . '.' . $field . ' as ' . $table . '_' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $table . '_' . $field;
                    $columns_table[$index]['field_raw'] = $table . '.' . $field;
                } else {
                    $result->addselect($table . '.' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $field;
                    $columns_table[$index]['field_raw'] = $field;
                }

                $columns_table[$index]['field_with'] = $table . '.' . $field;
            }
        }

        if (Request::get('q')) {
            //check if search string contains '#' => which means user is searching by tag
            $tagSearchChecker = Str::contains(Request::get('q'), '#');

            if (!$tagSearchChecker) { //if search strin contains '#' default search will be skipped

                $result->where(function ($w) use ($columns_table, $request) {
                    foreach ($columns_table as $col) {
                        if (!$col['field_with']) {
                            continue;
                        }
                        if ($col['is_subquery']) {
                            continue;
                        }
                        $w->orwhere($col['field_with'], "like", "%" . Request::get("q") . "%");
                    }
                });

            }

        }

        if (Request::get('where')) {
            foreach (Request::get('where') as $k => $v) {
                $result->where($table . '.' . $k, $v);
            }
        }

        $filter_is_orderby = false;
        if (Request::get('filter_column')) {

            $filter_column = Request::get('filter_column');
            $result->where(function ($w) use ($filter_column, $fc) {
                foreach ($filter_column as $key => $fc) {

                    $value = @$fc['value'];
                    $type = @$fc['type'];

                    if ($type == 'empty') {
                        $w->whereNull($key)->orWhere($key, '');
                        continue;
                    }

                    if ($value == '' || $type == '') {
                        continue;
                    }

                    if ($type == 'between') {
                        continue;
                    }

                    switch ($type) {
                        default:
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'like':
                        case 'not like':
                            $value = '%' . $value . '%';
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'in':
                        case 'not in':
                            if ($value) {
                                $value = explode(',', $value);
                                if ($key && $value) {
                                    $w->whereIn($key, $value);
                                }
                            }
                            break;
                    }
                }
            });

            foreach ($filter_column as $key => $fc) {
                $value = @$fc['value'];
                $type = @$fc['type'];
                $sorting = @$fc['sorting'];

                if ($sorting != '') {
                    if ($key) {
                        $result->orderby($key, $sorting);
                        $filter_is_orderby = true;
                    }
                }

                if ($type == 'between') {
                    if ($key && $value) {
                        $result->whereBetween($key, $value);
                    }
                } else {
                    continue;
                }
            }
        }

        if ($filter_is_orderby == true) {
            $data['result'] = $result->paginate($limit);
        } else {
            if ($this->orderby) {
                if (is_array($this->orderby)) {
                    foreach ($this->orderby as $k => $v) {
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                            $k = explode(".", $k)[1];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                } else {
                    $this->orderby = explode(";", $this->orderby);
                    foreach ($this->orderby as $o) {
                        $o = explode(",", $o);
                        $k = $o[0];
                        $v = $o[1];
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                }
                $data['result'] = $result->paginate($limit);
            } else {
                $data['result'] = $result->orderby($this->table . '.' . $this->primary_key, 'desc')->paginate($limit);
            }
        }

        $data['columns'] = $columns_table;

        if ($this->index_return) {
            return $data;
        }

        //LISTING INDEX HTML
        $addaction = $this->data['addaction'];

        if ($this->sub_module) {
            foreach ($this->sub_module as $s) {
                $table_parent = CRUDBooster::parseSqlTable($this->table)['table'];
                $addaction[] = [
                    'label' => $s['label'],
                    'icon' => $s['button_icon'],
                    'url' => CRUDBooster::adminPath($s['path']) . '?return_url=' . urlencode(Request::fullUrl()) . '&parent_table=' . $table_parent . '&parent_columns=' . $s['parent_columns'] . '&parent_columns_alias=' . $s['parent_columns_alias'] . '&parent_id=[' . (!isset($s['custom_parent_id']) ? "id" : $s['custom_parent_id']) . ']&foreign_key=' . $s['foreign_key'] . '&label=' . urlencode($s['label']),
                    'color' => $s['button_color'],
                    'showIf' => $s['showIf'],
                ];
            }
        }

        $mainpath = CRUDBooster::mainpath();
        $orig_mainpath = $this->data['mainpath'];
        $title_field = $this->title_field;
        $html_contents = [];
        $page = (Request::get('page')) ? Request::get('page') : 1;
        $number = ($page - 1) * $limit + 1;
        foreach ($data['result'] as $row) {
            $html_content = [];

            if ($this->button_bulk_action) {

                $html_content[] = "<input type='checkbox' class='checkbox' name='checkbox[]' value='" . $row->{$tablePK} . "'/>";
            }

            if ($this->show_numbering) {
                $html_content[] = $number . '. ';
                $number++;
            }

            foreach ($columns_table as $col) {
                if ($col['visible'] === false) {
                    continue;
                }

                $value = @$row->{$col['field']};
                $title = @$row->{$this->title_field};
                $label = $col['label'];

                if (isset($col['image'])) {
                    if ($value == '') {
                        $value = "<a  data-lightbox='roadtrip' rel='group_{{$table}}' title='$label: $title' href='" . asset('vendor/crudbooster/avatar.jpg') . "'><img width='40px' height='40px' src='" . asset('vendor/crudbooster/avatar.jpg') . "'/></a>";
                    } else {
                        $pic = (strpos($value, 'http://') !== false) ? $value : asset($value);
                        $value = "<a data-lightbox='roadtrip'  rel='group_{{$table}}' title='$label: $title' href='" . $pic . "'><img width='40px' height='40px' src='" . $pic . "'/></a>";
                    }
                }

                if (@$col['download']) {
                    $url = (strpos($value, 'http://') !== false) ? $value : asset($value) . '?download=1';
                    if ($value) {
                        $value = "<a class='btn btn-xs btn-primary' href='$url' target='_blank' title='Download File'><i class='fa fa-download'></i> Download</a>";
                    } else {
                        $value = " - ";
                    }
                }

                if ($col['str_limit']) {
                    $value = trim(strip_tags($value));
                    $value = Str::limit($value, $col['str_limit']);
                }

                if ($col['nl2br']) {
                    $value = nl2br($value);
                }

                if ($col['callback_php']) {
                    foreach ($row as $k => $v) {
                        $col['callback_php'] = str_replace("[" . $k . "]", $v, $col['callback_php']);
                    }
                    @eval("\$value = " . $col['callback_php'] . ";");
                }

                //New method for callback
                if (isset($col['callback'])) {
                    $value = call_user_func($col['callback'], $row);
                }

                $datavalue = @unserialize($value);
                if ($datavalue !== false) {
                    if ($datavalue) {
                        $prevalue = [];
                        foreach ($datavalue as $d) {
                            if ($d['label']) {
                                $prevalue[] = $d['label'];
                            }
                        }
                        if ($prevalue && count($prevalue)) {
                            $value = implode(", ", $prevalue);
                        }
                    }
                }

                $html_content[] = $value;
            } //end foreach columns_table

            if ($this->button_table_action):

                $button_action_style = $this->button_action_style;
                $html_content[] = "<div class='button_action' style='text-align:right'>" . view('crudbooster::components.action', compact('addaction', 'row', 'button_action_style', 'parent_field'))->render() . "</div>";

            endif;//button_table_action

            foreach ($html_content as $i => $v) {
                $this->hook_row_index($i, $v);
                $html_content[$i] = $v;
            }

            $html_contents[] = $html_content;
        } //end foreach data[result]

        $html_contents = ['html' => $html_contents, 'data' => $data['result']];

        $data['html_contents'] = $html_contents;

        return view("admin.new_customer.index", $data);
    }

    public function getDetail($id)
    {
        //Create an Auth
        if (!CRUDBooster::isRead() && $this->global_privilege == FALSE || $this->button_edit == FALSE) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $data = [];
        $data['page_title'] = 'Customer Overview ';

        if (CRUDBooster::isSuperadmin()) {
            $customer = NewCustomer::find($id);
        } else {
            $customer = NewCustomer::where([
                'id' => $id,
                'user_id' => CRUDBooster::myId(),
            ])->first();
        }

        // error if customer not found
        if ($customer->id == NULL) {
            return CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Please enter input correctly'), 'error');
        }

        $orders = NewOrder::where('drm_customer_id', $customer->id);
        $order_data = $orders->get();

        $prouducts = [];
        if (count($order_data)) {
            foreach ((object)$order_data as $item) {
                foreach (json_decode($item->cart) as $cart) {
                    $prouducts[] = $cart;
                }
            }
        }


        $data['purchase_check'] = AppStore::CheckAppPurchaseBoolean('40');

        if (!CRUDBooster::isSuperadmin()) {
            $data['manual_tags'] = CustomerTag::where('user_id', CRUDBooster::myId())->where('customer_id', $id)->where('type', 'manual_tags')->get();
        } else {
            $data['manual_tags'] = CustomerTag::where('customer_id', $id)->where('type', 'manual_tags')->get();
        }

        $data['customer'] = $customer;
        $data['orders'] = $order_data;

        $data['total_order'] = $orders->count();
        $data['shipped'] = $orders->where("status", "Shipped")->count() + $orders->where("status", "Versendet")->count();
        $data['others'] = $data['total_order'] - $data['shipped'];
        $data['total'] = NewOrder::where('drm_customer_id', $customer->id)->sum('total');
        $data['total_product'] = count($prouducts);
        $data['languages'] = DB::table('countries')->where('is_active', 1)->orderBy('language_shortcode')->get();
        $data['countries'] = DB::table('tax_rates')->orderBy('country')->get();
        return view('admin.new_customer.details', $data);
    }


    // --------------- update customer ajax ----------
    public function postUpdateUser()
    {
        if ($_REQUEST['table'] == "customer_info") {
            NewCustomer::find($_REQUEST['customer_id'])->update([
                $_REQUEST['field'] => $_REQUEST['value']
            ]);
        } else if ($_REQUEST['table'] == "billing" || $_REQUEST['table'] == "shipping") {
            $column = $_REQUEST['table'];
            $customer = NewCustomer::find($_REQUEST['customer_id']);
            if ($customer && $column) {
                $tmp_data = json_decode($customer->{$column});
                $tmp_data->{$_REQUEST['field']} = $_REQUEST['value'];
                $customer->update([
                    $column => json_encode($tmp_data)
                ]);
            }
        }
    }


    public function getEdit($id)
    {
        //Create an Auth
        if (!CRUDBooster::isUpdate() && $this->global_privilege == FALSE || $this->button_edit == FALSE) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }


        session(['customer_id' => $id]);

        $data = [];
        $data['page_title'] = 'Edit Data';
        // $data['languages'] = DB::table('countries')->get(); // jahdiulhasazahid
        $data['languages'] = DB::table('countries')->where('is_active', 1)->get();
        $data['countries'] = DB::table('tax_rates')->orderBy('country')->get();

        $customer = DB::table('new_customers')->where('id', $id);

        if (!CRUDBooster::isSuperadmin()) {
            $customer->where('user_id', CRUDBooster::myId());
        }

        $data['customer'] = $customer->first();
        // $data['selectUserName'] = DB::table('cms_users')->where('id',$data['customer']->user_id)->select('name')->first();

        if ($data['customer'] == null) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }


        $data['shipping'] = json_decode($data['customer']->shipping);
        $data['billing'] = json_decode($data['customer']->billing);

        // dd($data['shipping'], $data['billing']);

        // jahidulhasanzahid
        $data['users'] = DB::table('cms_users')->orderBy('name')->select('id', 'name')->get();
        // dd($data['customer']);

        return view('admin.new_customer.edit', $data);
    }


    public function postEditSave($id)
    {
        // dd(billingInfoJson($_REQUEST));

        $validator = Validator::make($_REQUEST, [

            "customer_full_name" => "required",
            "email" => "required",
            "address" => "required",
            // "company_name" => "required",
            // "phone" => "required",
            "currency" => "required",
            "default_language" => "required",
            "city" => "required",
            // "state" => "required",
            "country" => "required",
            // "street_shipping" => "required",
            // "city_shipping" => "required",
            // "state_shipping" => "required",
            // "country_shipping" => "required"

        ]);


        if ($validator->fails()) {
            return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
        }


        $customer = NewCustomer::find($id);
        $user_id = $_REQUEST['user_id'] ?? CRUDBooster::myId();
        // $insert_type = $_REQUEST['insert_type'] ?? 6;

        if ($customer) {
            $shipping = updateBillingShippingAddress(shippingInfoJson($_REQUEST), $customer->shipping);
            $billing = (isset($_REQUEST['is_same_address'])) ? $shipping : updateBillingShippingAddress(billingInfoJson($_REQUEST), $customer->shipping);

            $customer->update([
                'full_name' => $_REQUEST['customer_full_name'],
                'company_name' => $_REQUEST['company_name'],
                'email' => $_REQUEST['email'],
                'city' => $_REQUEST['city'],
                'zip_code' => $_REQUEST['zip_code'],
                'state' => $_REQUEST['state'],
                'country' => $_REQUEST['country'],
                'phone' => $_REQUEST['phone'],
                'website' => $_REQUEST['website'],
                'currency' => $_REQUEST['currency'],
                'default_language' => $_REQUEST['default_language'],
                'address' => $_REQUEST['address'],
                // 'insert_type'=>$_REQUEST['insert_type'],
                'user_id' => $user_id,
                'vat_number' => $_REQUEST['vat_number'],
                'shipping' => $shipping,
                'billing' => $billing,
            ]);
        }

        CRUDBooster::redirect(CRUDBooster::adminPath('drm_all_customers'), trans('Save seccessfull.'), 'success');
    }


    public function getAdd()
    {
        //Create an Auth
        if (!CRUDBooster::isCreate() && $this->global_privilege == FALSE || $this->button_add == FALSE) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $data = [];
        $data['page_title'] = 'Add Data';

        // $data['languages'] = DB::table('languages')->get();
        $data['languages'] = DB::table('countries')->where('is_active', 1)->orderBy('name')->get();
        $data['countries'] = DB::table('tax_rates')->orderBy('country')->get();;
        $data['users'] = DB::table('cms_users')->orderBy('name')->get();

        return view('admin.new_customer.add', $data);
    }


    public function postAddSave()
    {

        $validator = Validator::make($_REQUEST, [
            "customer_full_name" => "required",
            // "email" => "required | unique:new_customers",
            // "company_name" => "required",
            // "phone" => "required",
            "currency" => "required",
            "default_language" => "required",
            "city" => "required",
            // "state" => "required",
            "country" => "required",
            // "street_shipping" => "required",
            // "city_shipping" => "required",
            // "state_shipping" => "required",
            // "country_shipping" => "required"
        ]);


        if (CRUDBooster::isSuperadmin()) {
            $validator = Validator::make($_REQUEST, [
                "user_id" => "required",
            ]);

            $user_id = $_REQUEST['user_id'];
        } else {
            $user_id = CRUDBooster::myId();
        }

        if ($validator->fails()) {
            return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
        }

        $_REQUEST['insert_type'] = 6;
        if ($this->add_customer($_REQUEST)) {
            return redirect('admin/drm_all_customers')->with('success', "Successfully Added Customer");
        }

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Please enter input correctly'), 'error');
    }

    // return id if successfull
    // return null if unsuccessful
    public function add_customer($customer_info)
    {
        // //DB::beginTransaction();
        try {

            $validator = Validator::make($customer_info, [
                'email' => 'required',
                'user_id' => 'required',
            ]);
            if ($validator->fails()) {
                return null;
            }
            $user_id = $customer_info['user_id'];

            $customer_data = [];
            $customer_data['full_name'] = $customer_info['customer_full_name'];
            $customer_data['company_name'] = $customer_info['company_name'];
            $customer_data['country'] = drmCountryNameFull($customer_info['country']);
            $customer_data['phone'] = $customer_info['phone'];
            $customer_data['website'] = $customer_info['website'];
            $customer_data['city'] = $customer_info['city'];
            $customer_data['zip_code'] = $customer_info['zip_code'];
            $customer_data['state'] = $customer_info['state'];
            $customer_data['currency'] = $customer_info['currency'];
            $customer_data['default_language'] = $customer_info['default_language'];
            $customer_data['address'] = $customer_info['address'];
            $customer_data['insert_type'] = $customer_info['insert_type'];
            $customer_data['vat_number'] = $customer_info['vat_number'];

            $customer_data = array_filter($customer_data);
            $customer = null;

            $customer = NewCustomer::updateOrCreate([
                'email' => $customer_info['email'],
                'user_id' => $user_id,
            ], $customer_data);

            //Update order data
            if ($customer && $customer->id) {

                //Remove this users duplicate cc_user_id
                if (isset($customer_info['cc_user_id']) && $customer_info['cc_user_id']) {
                    $cc_user_id = $customer_info['cc_user_id'];
                    $customer_id = $customer->id;
                    DB::table('new_customers')->where('user_id', $user_id)->where('cc_user_id', $cc_user_id)->where('id', '<>', $customer_id)->update(['cc_user_id' => null]);
                    DB::table('new_customers')->where('id', $customer_id)->update(['cc_user_id' => $cc_user_id]);
                }

                //Update billing and shipping address
                $shipping = updateBillingShippingAddress(shippingInfoJson($customer_info), $customer->shipping);
                $billing = (isset($_REQUEST['is_same_address'])) ? $shipping : updateBillingShippingAddress(billingInfoJson($customer_info), $customer->billing);
                $customer->update([
                    'billing' => $billing,
                    'shipping' => $shipping
                ]);

                $this->updateOrderCustomerInfo($customer->id);

                //Customer manual tags
                if (isset($customer_info['customer_tag_suggest']) && $customer_info['customer_tag_suggest']) {
                    $manual_tags = $customer_info['customer_tag_suggest'];
                    foreach ($manual_tags as $manual_tag) {
                        $tag = trim($manual_tag);
                        if ($tag) {
                            //insert tag
                            try {
                                DropfunnelCustomerTag::insertTag($tag, $user_id, $customer->id, 4);
                            } catch (\Exception $ev) {
                            }
                        }
                    }
                }
                // customer tag

            } else {
                return null;
            }

            // //DB::commit();
            return $customer->id;

        } catch (\Exception $e) {
            // //DB::rollBack();
            return null;
        }
    }

    public function updateOrderCustomerInfo($customer_id)
    {
        // TODO:: DROPMATIX
        return app(\App\Services\Order\Store\CustomerOrder::class)->updateOrderCustomerInfo($customer_id);
    }

    public function update_customer($customer_info, $id)
    {
        $mpCustomer = isset($customer_info['dropmatix_mp']) && $customer_info['dropmatix_mp'];

        $customer = NewCustomer::find($id);
        if ($customer) {
            $customer_data = [];
            $customer_data['full_name'] = $customer_info['customer_full_name'];
            $customer_data['company_name'] = $customer_info['company_name'];
            $customer_data['country'] = drmCountryNameFull($customer_info['country']);
            $customer_data['city'] = $customer_info['city'];
            $customer_data['zip_code'] = $customer_info['zip_code'];
            $customer_data['address'] = $customer_info['address'];

            $optional = [];
            $optional['state'] = $customer_info['state'];
            $optional['phone'] = $customer_info['phone'];
            $optional['website'] = $customer_info['website'];
            $optional['currency'] = $customer_info['currency'];
            $optional['default_language'] = $customer_info['default_language'];
            $optional = array_filter($optional);

            $customer_data = array_merge($customer_data, $optional);

            if (isset($customer_info['vat_number'])) {
                $customer_data['vat_number'] = $customer_info['vat_number'];
            }

            $shipping = shippingInfoJson($customer_info, true); //updateBillingShippingAddress(shippingInfoJson($customer_info), $customer->shipping);
            $billing = (isset($customer_info['is_same_address']) && !$mpCustomer) ? $shipping : billingInfoJson($customer_info, true);

            $customer_data['billing'] = $billing;

            if(!$mpCustomer)
            {
                $customer_data['shipping'] = $shipping;
            }

            // customer filter empty value
            // $customer_data = array_filter($customer_data);
            $customer->update($customer_data);

            //Update order data
            $this->updateOrderCustomerInfo($customer->id);

            return $id;
        }
        return null;
    }


    // ----------- add customer ajax ---------
    public function postAddCustomerAjax()
    {
        // return response()->json($_REQUEST);
        // return response()->json($_REQUEST["street_billing"]);

        $validator = Validator::make($_REQUEST, [

            "customer_full_name" => "required",
            // "email" => "required | unique:new_customers",
            // "company_name" => "required",
            // "phone" => "required",
            "currency" => "required",
            "default_language" => "required",
            "city" => "required",
            // "state" => "required",
            "country" => "required",
            // "street_shipping" => "required",
            // "city_shipping" => "required",
            // "state_shipping" => "required",
            "country_shipping" => "required"

        ]);

        // if(CRUDBooster::isSuperadmin())
        // {
        // 	$validator = Validator::make($_REQUEST, [
        // 		"user_id" => "required",
        // 	]);

        // 	$user_id =  $_REQUEST['user_id'];
        // }else{
        // 	$user_id = CRUDBooster::myId();
        // }


        if ($validator->fails()) {
            $_REQUEST["error"] = "1";
            return response()->json($validator->errors(), 500);
            // return 0;
        }

        $_REQUEST['user_id'] = CRUDBooster::myId();
        $_REQUEST['insert_type'] = 6;

        $_REQUEST['customer_id'] = "";
        $_REQUEST['customer_id'] = $this->add_customer($_REQUEST);

        $data = $_REQUEST;

        if ($data['customer_id'] == null || $data['customer_id'] == "") {
            return response()->json($data, 500);
        }

        return response()->json($data, 200);

    }


    // public function getCsvInsert()
    //   	{

    //   		// $str= '';
    //   		// foreach (NewCustomer::all() as $value) {
    //   		// 	$str .= '("'.$value->full_name.'","'.$value->email.'", '.$value->user_id.','.$value->insert_type.'),';
    //   		// }

    //   		// dd($str);


    //    	$count = 0;
    //        try{
    // 	$csv_path = 'csv/vod_customers.csv';
    // 	// $csv = \Storage::disk('public')->get($csv_path);

    //  //          \Storage::disk('spaces')->put($csv_path, $csv, 'public');

    //  //          if(\Storage::disk('spaces')->exists($csv_path)){
    //  //          	\Storage::disk('public')->put('vod_customers.csv', \Storage::disk('spaces')->get($csv_path) );
    // 	// }

    // 	// dd(\Storage::disk('spaces')->url($csv_path));


    // 	$order_content = @file_get_contents(\Storage::disk('spaces')->url($csv_path));
    //        if (!$order_content) {
    //            throw new \Exception("Can not access url or no order found!");
    //        }
    //        // dd($content);

    //        $putted_orders = @file_put_contents(storage_path() . "/vod_customers.csv", $order_content);
    //        if (!$putted_orders) {
    //            throw new \Exception("Content can not be putted to file " . storage_path() . "/vod_customers.csv");
    //        }


    //         $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
    //         $reader->setInputEncoding('UTF-8');
    //         $reader->setDelimiter(',');
    //         $spreadsheet = $reader->load(realpath(storage_path() . "/vod_customers.csv"));

    //         $customer_arr = $spreadsheet->getActiveSheet()->toArray();

    //         // dd($customer_arr);


    //         $customer_columns = $customer_arr[0];
    //         unset($customer_arr[0]);

    //         // $new_customers = [];
    //         if (count($customer_arr)) {
    // 	        foreach ($customer_arr as $item) {
    // 	        	$name = $item[1];
    // 	        	$email = $item[2];
    // 	        	$street = $item[4];

    // 	        	$address = $item[5];
    // 	        	$zip_code = null;
    // 	        	$city = $address;

    // 	        	if($address){
    // 	        		preg_match("/\b\d{4,6}(-\d{4,6})?\b/", $address, $matches);
    // 	        		$zip = $matches[0];
    // 	        		if ($zip) {
    // 	        			$zip_code = $zip;
    // 						$city = preg_replace('/\b'.$zip.'\b/i', '', $address);
    // 	        		}
    // 	        	}
    //                 $customer_info = [
    //                     "full_name" => $name,
    //                     'address' => $street,
    //                     'zip_code' => $zip_code,
    //                     'city' => $city,
    //                     'insert_type' => 2,
    //                 ];

    //                 $check = [
    //                 	'email' => $item[2],
    //                 	'user_id' => 98,
    //                 ];
    // 		        // customer add
    // 		        $customer_info = array_filter($customer_info);
    // 		        $customer = NewCustomer::updateOrCreate($check, $customer_info);
    // 		        $customer->update([
    // 		        	'billing' => updateBillingShippingAddress(customerToBillingJson($customer), $customer->billing),
    // 		        	'shipping' => updateBillingShippingAddress(customerToShippingJson($customer), $customer->shipping)
    // 		        ]);

    // 	        	// $new_customers[] = @array_combine($customer_columns, $item);
    // 	            $count++;
    // 	    	}
    // 	    }

    //     }  catch (\Exception $e) {
    //         dd($e->getMessage());
    //     }finally{
    //     	dd($count);
    //     }
    // }

    // public function getInsertOldData(){
    // 	$customers = DB::table('drm_customers')->get();
    // 	$count = 0;
    // 	foreach ($customers as $customer) {
    // 		$billing = DB::table('drm_customer_address')->where('drm_customer_id', $customer->id)->where('type', 'billing')->first();
    // 		$shipping = DB::table('drm_customer_address')->where('drm_customer_id', $customer->id)->where('type', 'shipping')->first();

    // 		$insert_type = ($customer->insert_type == 'Import')? 5 : ( ($customer->insert_type == 'API')? 1 : 6);

    // 		$exist_api = NewCustomer::where('user_id', $customer->user_id)->where('email', $customer->email)->whereIn('insert_type', [1, 2])->first();
    // 		if ($exist_api) {
    // 			continue;
    // 		}

    //               $customer_info = [
    //                   "customer_full_name" => $customer->full_name,
    //                   "company_name" =>  $customer->company_name,
    //                   "currency" => $customer->currency,
    //                   'email' => $customer->email,
    //                   'phone' => $customer->phone,
    //                   'website' => $customer->website,

    //                   'city' =>  $customer->city,
    //                   'zip_code' => $customer->zip_code,
    //                   'state' => $customer->state,
    //                   'address' =>  $customer->address,
    //                   'country' => $customer->country,
    //                   'default_language' => $customer->default_language,

    //                   'insert_type' => $insert_type,

    //                   //shipping
    //                   'street_shipping' => $shipping->street,
    //                   'city_shipping' => $shipping->city,
    //                   'state_shipping' => $shipping->state,
    //                   'zipcode_shipping' => $shipping->zipcode,
    //                   'country_shipping' => $shipping->countryIsoCode,

    //                   //billing
    //                   'street_billing' => $billing->street,
    //                   'city_billing' => $billing->city,
    //                   'state_billing' => $billing->state,
    //                   'zipcode_billing' => $billing->zipcode,
    //                   'country_billing' => $billing->countryIsoCode,

    //                   'user_id' => $customer->user_id,
    //               ];
    //               $count ++;
    //               $this->add_customer($customer_info);
    // 	}
    // 	dd($count);
    // }


    //Update cms client orders
    public function updateCmsClientOrder($customer_id)
    {
        // TODO:: DROPMATIX
        return app(\App\Services\Order\Store\CustomerOrder::class)->updateCmsClientOrder($customer_id);
    }

    //Create user billing details to Customer
    public function userBillingToCustomerProfile($user_id, $client_id)
    {
        $user_data = User::find($user_id);
        $user = User::with('billing_detail')->has('billing_detail')->find($user_id);

        $customer = NewCustomer::where('user_id', $client_id)->where('cc_user_id', $user_id)->select('id', 'tax_number')->first();
        $newly = (empty($customer)) ? true : false;

        $customer_id = null;
        $customer_info = [];

        if ($user) {
            $billing = $user->billing_detail;
            $country = ($billing->country) ? (($billing->country->id == 2) ? 'United Kingdom' : $billing->country->name) : null;
            $country = drmCountryNameFull($country);
            $language = ($billing->country) ? $billing->country->language : null;

            $customer_info["customer_full_name"] = $user->name;
            $customer_info["company_name"] = $billing->company_name;
            $customer_info["currency"] = 'EUR';
            $customer_info['email'] = $user->email;
            $customer_info['phone'] = $billing->phone;
            $customer_info['address'] = $billing->address;
            $customer_info['city'] = $billing->city;
            $customer_info['country'] = $country;
            $customer_info['default_language'] = $language;
            $customer_info['zip_code'] = $billing->zip;
            $customer_info['state'] = null;
            $customer_info['vat_number'] = $billing->vat_id;
            $customer_info['tax_number'] =  $customer ? $customer->tax_number : null;

            //shipping
            $customer_info['street_shipping'] = $billing->address;
            $customer_info['city_shipping'] = $billing->city;
            $customer_info['state_shipping'] = null;
            $customer_info['zipcode_shipping'] = $billing->zip;
            $customer_info['country_shipping'] = $country;

            //billing
            $customer_info['street_billing'] = $billing->address;
            $customer_info['city_billing'] = $billing->city;
            $customer_info['state_billing'] = null;
            $customer_info['zipcode_billing'] = $billing->zip;
            $customer_info['country_billing'] = $country;

        } else if (!empty($customer) && $customer->id) {
            return $customer->id;  //Customer_id
        } else {
            $customer_info["customer_full_name"] = $user_data->name;
            $customer_info["currency"] = 'EUR';
            $customer_info['email'] = $user_data->email;
        }

        $customer_info['insert_type'] = 6;
        $customer_info['user_id'] = $client_id;
        $customer_info['cc_user_id'] = $user_data->id;

        $customer_id = $this->add_customer($customer_info, $user_id);  //Customer_id
        
        // if ($customer_id && !is_null($customer_id) && $newly) {
        //     $this->updateCmsClientOrder($customer_id);    // Update cms clients customer_id
        // }

        return $customer_id;
    }


    public function getTagExtensionSave()
    {
        try {
            $purchased_app = DB::table('purchase_apps')
                ->select('app_stores.id', 'app_stores.menu_name', 'purchase_apps.cms_user_id')
                ->join('app_stores', 'purchase_apps.app_id', '=', 'app_stores.id')
                ->whereDate('purchase_apps.subscription_date_end', '>=', Carbon::now())
                ->get()
                ->toArray();

            $assigned_app = DB::table('app_assigns')
                ->select('app_stores.menu_name', 'app_assigns.user_id')
                ->join('app_stores', 'app_assigns.app_id', '=', 'app_stores.id')
                ->where(function($amnu){
                    $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
                })
                ->get()
                ->toArray();

            foreach ($purchased_app as $item) {
                $this->tagInsertToCustomer($item->cms_user_id, $item->menu_name, 7);
            }
            foreach ($assigned_app as $value) {
                $this->tagInsertToCustomer($value->cms_user_id, $value->menu_name, 7);
            }

            $purchased_app_expire = DB::table('purchase_apps')
                ->select('app_stores.id', 'app_stores.menu_name', 'purchase_apps.cms_user_id')
                ->join('app_stores', 'purchase_apps.app_id', '=', 'app_stores.id')
                ->whereDate('purchase_apps.subscription_date_end', '<', Carbon::now())
                ->get()
                ->toArray();

            foreach ($purchased_app_expire as $item) {
                $this->tagInsertToCustomer($item->cms_user_id, $item->menu_name . '-Cancelt', 7);
            }

        } catch (\Exception $ex) {
            return $ex->getMessage();
        }
    }

    //Add droptienda shop Tag
    public function addDroptiendaTag(){
        try{
            $request = $_REQUEST;
            $validator = Validator::make($request, [
                'user_id' => 'required|exists:cms_users,id',
            ]);

            if ($validator->fails()) {
                throw new \Exception('Invalid Data');
            }

            $user_id = $request['user_id'];
            if(empty($user_id)) throw new \Exception('Invalid Data');

            $has_dt_shop = \App\Shop::where('user_id', $user_id)->where('channel', 10)->exists();
            if($has_dt_shop){
              $this->tagInsertToCustomer($user_id, 'DT User', 10, 2439);
                return response()->json([
                    'success' => true,
                    'message' => 'Data added successfully!',
                ]);
            }else{
                throw new \Exception('User has no DT shop');
            }

            throw new \Exception('Invalid Data');
        }catch(\Exception $e){
            return response()->json([
                'success' => true,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function tagInsertion(LaravelRequest $request)
    {
        try {
            $requestCustomer = $request->customer;
            $conditions = ['email' => $requestCustomer['email'], 'user_id' => $request->user_id];
            $customer = NewCustomer::where($conditions)->select('id')->first();

            if (empty($customer)) {
                $customer = NewCustomer::create([
                    'email' => $requestCustomer['email'],
                    'full_name' => $requestCustomer['full_name'],
                    'phone' => $requestCustomer['phone'],
                    'address' => $requestCustomer['address'],
                    'user_id' => $request->user_id,
                    'insert_type' => 11
                ]);
            }

            $response = DropfunnelCustomerTag::insertTag($request->tag, $request->user_id, $customer->id, 18);
            if ($response['success']) {
                return response()->json(['success' => true, 'message' => 'Tag inserted successful']);
            }

            throw new Exception($response['message']);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => $exception->getMessage()]);
        }
    }

    public function lockUnlockCustomer($user_id, $status='')
    {
        try{
            $plan = app('App\Services\UserService')->userPlanStatus($user_id);
            // $plan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);
        
            $user_account_status = 1;
        
            //lock 
            if($status == 'inkasso' && !empty($user_id)){
                DrmUserLockUnlock::updateOrCreate([
                    'user_id' => $user_id] ,[
                    'status' => 1, //for lock status 1
                    // 'created_at' => now(),
                    // 'updated_at' => now()
                ]);

                $user_account_status = 1;


                // TODO:: Disconnect products
                \App\Jobs\SuspendUser\DisconnectProducts::dispatch($user_id);

                
            }else{
                //unlock
                $user_orders = DB::table('new_orders')->where('cms_client', $user_id)->where('status', 'inkasso')->exists();
               
                if(!$user_orders && in_array($plan['plan'], ['Purchased','Assigned','Trial','Unlimited'])){
                    
                    DrmUserLockUnlock::updateOrCreate([
                        'user_id' => $user_id] ,[
                        'status' => 0, //for unlock status 0
                        // 'created_at' => now(),
                        // 'updated_at' => now()
                    ]);
                }

                $user_account_status = 0;
            }

            $user_has_dt_shop = DB::table('shops')->where([
                'channel' => Channel::DROPTIENDA,
                'user_id' => $user_id
            ])->exists();

            if($user_has_dt_shop){
                DtUserLockUnlockJob::dispatch($user_id, $user_account_status);
            }

        }catch(\Exception $e){}
        
    }

}

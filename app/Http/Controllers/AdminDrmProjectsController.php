<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;

	use App\User;
 	use App\Notifications\DRMNotification;
	use crocodicstudio\crudbooster\helpers\CRUDBooster as HelpersCRUDBooster;
	use Illuminate\Support\Facades\Validator;
	use Illuminate\Support\Facades\Storage;
	use AppStore;

	use Illuminate\Support\Facades\Mail;
    use App\Mail\DRMSEndMail;

	class AdminDrmProjectsController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

			//jahidulhasanzahid
			// this is used for privacy access, like when user have no purchase plan, then it protect to visit incoming invoice feature
			// $this->CheckAppPurchase('DRM-Projekt');
			// if (app()->environment('local')) {
				// $app_details  = AppStore::ActiveApp('DRM-Projekt');
			// }
			//jahidulhasanzahid

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "title";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			if(!$this->app_purchased() && !CRUDBooster::isSuperadmin())
			{
				$this->button_add = false;
			}
			// $this->button_edit = false;
			// $this->button_delete = false;
			$this->button_detail = true;
			$this->button_show = false;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "drm_projects";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Title","name"=>"title"];
			// $this->col[] = ["label"=>"Company","name"=>"drm_customer_id","join"=>"drm_customers,company_name"];
			$this->col[] = ["label"=>"Created By","name"=>"cms_user_id", "join"=>"cms_users,name"];
			$this->col[] = ["label"=>"Start Date","name"=>"start_date"];
			$this->col[] = ["label"=>"Due Date","name"=>"due_date"];
			$this->col[] = ["label"=>"Status","name"=>"status"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			$this->form[] = ['label'=>'Title','name'=>'title','type'=>'text','validation'=>'required|string|min:3|max:70','width'=>'col-sm-10','placeholder'=>'You can only enter the letter only'];
			// $this->form[] = ['label'=>'Drm Customer Id','name'=>'drm_customer_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'drm_customer,id'];
			// $this->form[] = ['label'=>'Tags','name'=>'tags','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Start Date','name'=>'start_date','type'=>'date','validation'=>'required|date','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Due Date','name'=>'due_date','type'=>'date','validation'=>'required|date','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Rate Type','name'=>'rate_type','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Total Cost','name'=>'total_cost','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Status','name'=>'status','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Description','name'=>'description','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Created By','name'=>'cms_user_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'cms_users,name'];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ["label"=>"Title","name"=>"title","type"=>"text","required"=>TRUE,"validation"=>"required|string|min:3|max:70","placeholder"=>"You can only enter the letter only"];
			//$this->form[] = ["label"=>"Drm Customer Id","name"=>"drm_customer_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"drm_customer,id"];
			//$this->form[] = ["label"=>"Tags","name"=>"tags","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
			//$this->form[] = ["label"=>"Start Date","name"=>"start_date","type"=>"date","required"=>TRUE,"validation"=>"required|date"];
			//$this->form[] = ["label"=>"Due Date","name"=>"due_date","type"=>"date","required"=>TRUE,"validation"=>"required|date"];
			//$this->form[] = ["label"=>"Rate Type","name"=>"rate_type","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Total Cost","name"=>"total_cost","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Status","name"=>"status","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Description","name"=>"description","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
			//$this->form[] = ["label"=>"Cms User Id","name"=>"cms_user_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"cms_user,id"];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
			$this->addaction = array();
			// $this->addaction[] = ['label'=>'Set Active','url'=>CRUDBooster::mainpath('edit/[id]'),'icon'=>'fa fa-check','color'=>'success','showIf'=>"[cms_user_id] == [cms_user_id]"];
			$this->addaction[] = ['label'=>'Project Acitivity','url'=>CRUDBooster::mainpath('project/[id]'),'icon'=>'fa fa-check','color'=>'warning'];



	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
					*/

			$projects = DB::table('drm_project_members')->Join('drm_projects','drm_projects.id', '=', 'drm_project_members.drm_project_id')
				->where('drm_project_members.cms_user_id',CRUDBooster::myId())->get();

			$not_started = $projects->where('status','not_started')->count();
			$in_progress = $projects->where('status','in_progress')->count();
			$on_hold = $projects->where('status','on_hold')->count();
			$finished = $projects->where('status','finished')->count();
			$canceled = $projects->where('status','canceled')->count();
			$total  = $not_started + $in_progress + $on_hold + $finished + $canceled;

			$this->index_statistic = array();
			$this->index_statistic[] = ['label'=>'Not started','count'=>$not_started,'icon'=>'fa fa-area-chart','color'=>'success btn-primary not_started','width'=>'col-md-2 '];
			$this->index_statistic[] = ['label'=>'In progress','count'=>$in_progress,'icon'=>'fa fa-google-wallet','color'=>'success btn-primary in_progress','width'=>'col-md-2 '];
			$this->index_statistic[] = ['label'=>'On hold','count'=>$on_hold,'icon'=>'fa fa fa-line-chart','color'=>'success btn-warning on_hold','width'=>'col-md-2 '];
			$this->index_statistic[] = ['label'=>'Finished','count'=>$finished,'icon'=>'fa fa-briefcase','color'=>'success btn-success finished','width'=>'col-md-2 '];
			$this->index_statistic[] = ['label'=>'Canceled','count'=>$canceled,'icon'=>'fa fa-file-image-o','color'=>'success btn-danger canceled','width'=>'col-md-2 '];
			$this->index_statistic[] = ['label'=>'Total Data','count'=>$total,'icon'=>'fa fa-pie-chart','color'=>'success btn-default total','width'=>'col-md-2 '];


	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
					*/

			// $projects->Join('drm_project_members','drm_projects.id', '=', 'drm_project_members.drm_project_id')
			// 		->where('drm_project_members.cms_user_id',CRUDBooster::myId())/* ->orWhere('drm_projects.cms_user_id',CRUDBooster::myId()) */->get();
			// $project_ids = DB::table('drm_project_members')->where("cms_user_id",CRUDBooster::myId())->pluck('drm_project_id');


			$projects = DB::table('drm_project_members')->Join('drm_projects','drm_projects.id', '=', 'drm_project_members.drm_project_id')
			->where('drm_project_members.cms_user_id',CRUDBooster::myId())->get();


			// dd($projects);
			$this->script_js ="
				let projects = ".$projects.";
				let user_id = ".CRUDBooster::myId().";

				$('#table_dashboard tr').find('a[title=Delete]').hide();
				$('#table_dashboard tr').find('a[title=\"Edit Data\"]').hide();

				let tr = $('#table_dashboard tr');


				projects.forEach(function(item, i) {

					if(item.cms_user_id == user_id)
					{
							let string = item.title + item.start_date + item.status;
							// console.log(string);

							$.each(tr,function(index,value){

								let td = $(value).find('td') ;

								let title = $(td[1]).html(),
								date = $(td[3]).html(),
								status = $(td[5]).html();
								let string1 = title + date + status;
								if(string == string1)
								{
									$(value).find('a[title=Delete]').show() ;
									$(value).find('a[title=\"Edit Data\"]').show() ;
								}
							});
					}
				});" .

				'
				$(".total").click(function () {
					window.location.href = window.location.origin+ "/admin/drm_projects" ;
				});

				$(".not_started").click(function () {
					window.location.href = window.location.origin+ "/admin/drm_projects?filter=not_started";
				});

				$(".in_progress").click(function () {
					window.location.href = window.location.origin+ "/admin/drm_projects?filter=in_progress";
				});

				$(".on_hold").click(function () {
					window.location.href = window.location.origin+ "/admin/drm_projects?filter=on_hold";
				});

				$(".finished").click(function () {
					window.location.href = window.location.origin+ "/admin/drm_projects?filter=finished";
				});

				$(".canceled").click(function () {
					window.location.href = window.location.origin+ "/admin/drm_projects?filter=canceled";
				});
			';


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = ".small-box{cursor: pointer;border-radius: 5px;padding:15px 10px;}
					.small-box .icon i{vertical-align: top;font-size:35px;transition: .5s;vertical-align: top;margin-top: 18px;}
					.small-box:hover i{font-size: 40px;transition: .5s;}
					.margin-style{width: 159px;}
					.small-box>.inner{padding: 0px;}
					.small-box.bg-success.btn-default {background: #fff !important;}
					.small-box.bg-success.btn-default:hover {color: #444;background: #e7e7e7 !important;transition: .5s;}";




	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }



	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
			//Your code here
			// dd($id_selected,$button_name);

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here
	        // if(!CRUDBooster::isSuperadmin()){

					// }
			$query
				->Join('drm_project_members','drm_projects.id', '=', 'drm_project_members.drm_project_id')
				->where('drm_project_members.cms_user_id',CRUDBooster::myId());

			if(isset($_REQUEST['filter']))
			{
				if($_REQUEST['filter']=== 'not_started')
				{
					$query->where('drm_projects.status','not_started');
				}
				else if($_REQUEST['filter'] === 'in_progress')
				{
					$query->where('drm_projects.status','in_progress');
				}
				else if($_REQUEST['filter'] === 'on_hold')
				{
					$query->where('drm_projects.status','on_hold');
				}
				else if($_REQUEST['filter'] === 'finished')
				{
					$query->where('drm_projects.status','finished');
				}
				else if($_REQUEST['filter'] === 'canceled')
				{
					$query->where('drm_projects.status','canceled');
				}
			}
		}

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {

			if( DB::table("drm_projects")->find($id)->cms_user_id != CRUDBooster::myId())
			{
				CRUDBooster::redirect(Request::server('HTTP_REFERER'),"You do not have that privilage", 'error');
			}
		}

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
		*/

	    public function hook_before_delete($id) {

			if( DB::table("drm_projects")->find($id)->cms_user_id != CRUDBooster::myId())
			{
				CRUDBooster::redirect(Request::server('HTTP_REFERER'),"You do not have that privilage", 'error');
			}

			// drm_projects, drm_project_cards, drm_project_card_lists, drm_project_members,
			// drm_project_milestones, drm_project_tasks,
			// drm_task_checklist, drm_task_comments, drm_task_members

			$card_ids = DB::table('drm_project_cards')->where('drm_project_id',$id)->pluck('id')->toArray();
			// dd($card_ids);
			$task_ids = DB::table('drm_project_tasks')->whereIn('drm_project_card_id',$card_ids)->pluck('id')->toArray();
			// dd($task_ids);

			DB::table('drm_task_checklist')->whereIn('task_id', $task_ids)->delete();
			DB::table('drm_task_comments')->whereIn('task_id', $task_ids)->delete();
			// DB::table('drm_task_members')->whereIn('task_id', $task_ids)->delete();


			// dd($task_ids);
			// $cards;
			// DB::table('drm_project_card_lists')->where()-delete();

			DB::table('drm_project_tasks')->whereIn('id',$task_ids)->delete();
			DB::table('drm_project_cards')->where("drm_project_id", $id)->delete();
			// DB::table('drm_project_milestones')->where("drm_project_id", $id)->delete();
			DB::table('drm_project_members')->where("drm_project_id", $id)->delete();

			/* drm project will be deleted by crud booster  */
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }

		public function getAdd() {

			if(!CRUDBooster::isCreate() && $this->global_privilege==FALSE || $this->button_add==FALSE) {
				CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
			}

			if(!$this->app_purchased() && !CRUDBooster::isSuperadmin())
				CRUDBooster::redirect(Request::server('HTTP_REFERER'), "You haven't purchased the app!", "error");

			$data = [];
			$data['page_title'] = 'Add Project Details';

			if(CRUDBooster::isSuperadmin())
			{
				$data['customers']= DB::table('drm_customers')->get();
			}
			else
			{
				$data['customers']= DB::table('drm_customers')->where('user_id',CRUDBooster::myId())->get();
			}

			$data['members']= DB::table('cms_users')->get();

			//dd($data['customers']);
			//Please use cbView method instead view method from laravel
			$this->cbView('admin.drm_projects.add',$data);
		}

		public function postAddSave()
		{

			$validator = Validator::make($_REQUEST, [

				"title" => "required|string",
				// "drm_customer_id" => "required",
				"start_date" => "required|date",
			]);


			if ($validator->fails()) {
				return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
				// return 0;
			}


			// dd($_REQUEST);

			$project_id =  DB::table('drm_projects')->insertGetId([
				'title' => $_REQUEST['title'],
				'drm_customer_id' => $_REQUEST['drm_customer_id'],
				// 'tags' => $_REQUEST['tags'],
				'start_date' => $_REQUEST['start_date'],
				'due_date' => $_REQUEST['due_date'],
				// 'rate_type' => $_REQUEST['rate_type'],
				// 'total_cost' => $_REQUEST['total_cost'],
				'status' => $_REQUEST['status'],
				'description' => $_REQUEST['description'],
				'cms_user_id' =>$_REQUEST['user_id'] ?? CRUDBooster::myId(),
			]);


			DB::table('drm_project_members')->insert([
				'drm_project_id' => $project_id,
				'cms_user_id'=> CRUDBooster::myId()
			]);



			// add project mermbers
			if(isset($_REQUEST['drm_project_member_ids'] ))
			{
				foreach ($_REQUEST['drm_project_member_ids'] as $member_id) {
					DB::table('drm_project_members')->insert([
						'drm_project_id' => $project_id,
						'cms_user_id'=> $member_id
					]);

					User::find($member_id)->notify(new DRMNotification('You have been assign to an new project !','PROJECT_MANAGEMENT_MODULE',CRUDBooster::adminPath().'/drm_projects/project/'.$project_id));
				}
			}

			return redirect('admin/drm_projects')->with('success','Project added');

		}

		// get projects details
		public function getProject($project_id)
		{

			if(!CRUDBooster::isRead() && $this->global_privilege==FALSE || $this->button_edit==FALSE) {
				CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
			}

			$data['project_id'] = $project_id;

			session(['project_id' => $project_id]);

			$data['project']= $project = DB::table('drm_projects')->find($project_id);

			$data['page_title'] = "Project: ".$project->title;

			$members = DB::table("drm_project_members as pm")->join('cms_users as u','u.id','pm.cms_user_id')->select('pm.cms_user_id','u.name')->where('drm_project_id', $project_id)->get();

			foreach ($members as  $item) {

				$users[] = $item->cms_user_id;

				if($item->cms_user_id == CRUDBooster::myId())
				{
					continue;
				}

				$users[] = $user["cms_user_id"]= $item->cms_user_id;
				$user["name"]= $item->name;
				$data["members"][] =(object) $user;
			}

			// dd($users);
			if($users)
			if(!in_array(CRUDBooster::myId(),$users))
			{
				CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
			}

			// dd($data["members"]);

			// $this->cbView('admin.menudashboard.board_detail_view',$data);
			// $this->cbView('admin.drm_projects.project_detail',$data);
			return view('admin.drm_projects.project_detail',$data);
		}

		// get project edit
		public function getEdit($id) {
			//Create an Auth
			if(!CRUDBooster::isUpdate() && $this->global_privilege==FALSE || $this->button_edit==FALSE) {
				CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
			}

			$data = [];
			$data['page_title'] = 'Edit Data';
			$data['project'] = DB::table('drm_projects')->where('id',$id)->first();
			// $data['users'] = DB::table('cms_users')->get();

			if(CRUDBooster::isSuperadmin())
			{
				$data['customers']= DB::table('drm_customers')->get();
			}
			else
			{
				$data['customers']= DB::table('drm_customers')->where('user_id',CRUDBooster::myId())->get();
			}

			$data['members']= DB::table('cms_users')->get();
			$data['project_members']= DB::table('drm_project_members')->where('drm_project_id',$id)->get();

			// dd($id);
			// Session::set('project_id', $id);

			session(['project_id' => $id]);
			// dd($data['project_members']);


			//Please use cbView method instead view method from laravel
			// $this->cbView('admin.drm_projects.edit',$data);
			return view('admin.drm_projects.edit',$data);
		}

		// post -- project edit
		public function postEditSave($p_id)
		{
			// dd($_REQUEST);

			$project_id = session('project_id')??$p_id;

			// dd($project_id);

			$project = DB::table('drm_projects')->where('id',$project_id);
			if($project->first() == null )
			{
				return redirect('admin/drm_projects')->with('error','Project id not found');
			}

			$project->update([
				'title' => $_REQUEST['title'],
				'drm_customer_id' => $_REQUEST['drm_customer_id'],
				// 'tags' => $_REQUEST['tags'],
				'start_date' => $_REQUEST['start_date'],
				'due_date' => $_REQUEST['due_date'],
				// 'rate_type' => $_REQUEST['rate_type'],
				// 'total_cost' => $_REQUEST['total_cost'],
				'status' => $_REQUEST['status'],
				'description' => $_REQUEST['description'],
			]);

			// dd(($project->first())->id);

			DB::table('drm_project_members')->where('drm_project_id', ($project->first())->id )->delete();

			DB::table('drm_project_members')->Insert([
				'cms_user_id'=> ($project->first())->cms_user_id,
				'drm_project_id' => ($project->first())->id,
			]);

			if(isset($_REQUEST['drm_project_member_ids'] ))
			{
				foreach ($_REQUEST['drm_project_member_ids'] as $member_id) {
					DB::table('drm_project_members')->Insert([
						'cms_user_id'=> $member_id,
						'drm_project_id' => ($project->first())->id,
					]);

					User::find($member_id)->notify(new DRMNotification('A update in project is available!','PROJECT_MANAGEMENT_MODULE',CRUDBooster::adminPath().'/drm_projects/project/'.$project_id));
				}
			}

			CRUDBooster::redirect(CRUDBooster::adminPath('drm_projects'), trans('Save seccessfull.'), 'success');
        }

        public function postCopyProject()
        {
			// return response()->json($_REQUEST);

			$project = DB::table('drm_projects')->find($_REQUEST['project_id']);

			$data['project_copyed_id'] = $project_id = DB::table('drm_projects')->insertGetId([
				'title' => $_REQUEST['project_name'],
				'start_date' => $project->start_date,
				'due_date' => $project->due_date,
				'status' => $project->status,
				'description' => $project->description,
				'cms_user_id' => CRUDBooster::myId(),
			]);

			DB::table('drm_project_members')->insert([
				'drm_project_id' => $project_id,
				'cms_user_id'=> CRUDBooster::myId()
			]);


			DB::table('drm_project_cards')->where('drm_project_id', $project->id)->orderBy('id')->each(function ($card) use($project_id) {

				$card_id = DB::table('drm_project_cards')->insertGetId([
					'title' => $card->title,
					'position' => $card->position,
					'drm_project_id' => $project_id,
				]);

				$_GET['count_card'] = 0;
				if($_REQUEST['keep_task'] == 'on')
				DB::table('drm_project_tasks')->where('drm_project_card_id',$card->id)->orderBy('id')->each(function($task) use ($card_id){

					// add task
					$cover_image = null;

					if($task->cover_image && realpath('./storage/list_cover/'.$task->cover_image))
					{
						$ext  = end(explode(".",$task->cover_image));
						$cover_image = 'tc'.(++$_GET['count_card']).time().'.'.$ext;
						$data['copyed'][] = copy(realpath('./storage/list_cover/'.$task->cover_image), './storage/list_cover/'.$cover_image);

					}

					$task_id = DB::table('drm_project_tasks')->insertGetId([
						'drm_project_card_id' => $card_id,

						'name' => $task->name,
						'title' => $task->title,
						'description' => $task->description,
						'start_date' => $task->start_date,
						'due_date' => $task->due_date,
						'priority' => $task->priority,
						'status' => $task->status,
						'position' => $task->position,
						'cover_image' => $cover_image,
					]);

					foreach (DB::table('drm_task_checklist')->where('task_id',$task->id)->get() as $checklist) {

						$image_name = null;
						if($checklist->image && realpath('./storage/checklist_images/'.$checklist->image))
						{
							$ext  = end(explode(".",$checklist->image));
							$image_name = 'cl'.(++$_GET['count_card']).time().'.'.$ext;
							$data['name'][] = $image_name;
							$data['copyed'][] = copy(realpath('./storage/checklist_images/'.$checklist->image), './storage/checklist_images/'.$image_name);
						}

						$checklist_id = DB::table('drm_task_checklist')->insertGetId([
							'task_id' => $task_id,
							'title' => $checklist->title,
							'status' => $checklist->status,
							'image' => $image_name,
							'cms_user_id' => CRUDBooster::myId(),
						]);
					}

				});


			});

            return response()->json($data);
        }

		// ----------------- card -----------------------
		// add new card ajax
		public function postAddNewCard()
		{
			$card_id = DB::table('drm_project_cards')->insertGetId([
				'title' => $_REQUEST['title'],
				'position' => $_REQUEST['position'],
				'drm_project_id' => $_REQUEST['project_id'],
			]);

			return response()->json($card_id);
		}

		// update card position
		public function postUpdateCardPosition()
		{
			// $_REQUEST['card_position'][0]["id"]
			foreach ($_REQUEST['card_position'] as $key => $card) {
				DB::table('drm_project_cards')->where( "id",$card["id"])
						->update(['position' => $card["position"]]);
			}
			return response()->json("updated");
		}

		// get all records
		public function getGetAllCards()
		{
			// return response()->json($_REQUEST);

			$cards = DB::table('drm_project_cards')->where('drm_project_id',$_REQUEST['project_id'])->orderBy('position')->get();

			$data = [];

			foreach ($cards as  $card) {
				// $lists = DB::table('drm_project_card_lists')->where('drm_project_card_id',$card->id)->orderBy('position')->orderBy('created_at','DESC')->get();
				$tasks = DB::table('drm_project_tasks')->where('drm_project_card_id',$card->id)->orderBy('position')->orderBy('created_at','DESC')->get();
				// dd($tasks);


				foreach ($tasks as $task) {

					// $task = DB::table('drm_project_tasks')->where('drm_card_list_id', $list->id)->first();

					$checklist = DB::table('drm_task_checklist')->where('task_id',$task->id);
					$task->total_checklist = $checklist->count();
					$task->done_checklist = $checklist->where('drm_task_checklist.status','true')->count();

					$task->comment_count = DB::table('drm_task_comments')->where('task_id',$task->id)->count();
				}

				$data[] = [
					"card" => $card,
					"tasks" => $tasks
				];
			}

			return response()->json($data);
        }

		// /edit-card-name
		public function postEditCardName()
		{
			// return $_REQUEST;

			$data['changed'] = DB::table('drm_project_cards')->where('id',$_REQUEST['card_id'])->update([
				'title' => $_REQUEST['card_title']
			]);
			return response()->json($data);
		}


        public function postCopyCard()
        {
            // return $_REQUEST;
            // $card = DB::table('drm_projects')->find($_REQUEST['card_id']);
            // dd($project);


            $data['card_id'] = $card_id = DB::table('drm_project_cards')->insertGetId([
				'title' => DB::table('drm_project_cards')->find($_REQUEST['card_id'])->title,
				'position' => DB::table('drm_project_cards')->where('drm_project_id',$_REQUEST['project_id'])->count() + 1,
				'drm_project_id' => $_REQUEST['project_id'],
            ]);

            $_GET['count_card'] = 0;
            DB::table('drm_project_tasks')->where('drm_project_card_id',$_REQUEST['card_id'])->orderBy('id')->each(function($task) use ($card_id){
                // $list_id = DB::table('drm_project_card_lists')->insertGetId([
                //     'title' => $list->title,
                //     'position' => $list->position,
                //     'drm_project_card_id' => $card_id,
                // ]);

                // $task = DB::table('drm_project_tasks')->where('drm_project_card_id',$card_id)->first();

                // add task
                $cover_image = null;

                if($task->cover_image && realpath('./storage/list_cover/'.$task->cover_image))
                {
                    $ext  = end(explode(".",$task->cover_image));
                    $cover_image = 'tc'.(++$_GET['count_card']).time().'.'.$ext;
                    $data['copyed'][] = copy(realpath('./storage/list_cover/'.$task->cover_image), './storage/list_cover/'.$cover_image);

                }

                $task_id = DB::table('drm_project_tasks')->insertGetId([

                    'drm_project_card_id' => $card_id,

                    'name' => $task->name,
                    'title' => $task->title,
                    'description' => $task->description,
                    'start_date' => $task->start_date,
                    'due_date' => $task->due_date,
                    'priority' => $task->priority,
                    'status' => $task->status,
                    'position' => $task->position,
                    'cover_image' => $cover_image,
                ]);

                foreach (DB::table('drm_task_checklist')->where('task_id',$task->id)->get() as $checklist) {

                    $image_name = null;
                    if($checklist->image && realpath('./storage/checklist_images/'.$checklist->image))
                    {
                        $ext  = end(explode(".",$checklist->image));
                        $image_name = 'cl'.(++$_GET['count_card']).time().'.'.$ext;
                        $data['name'][] = $image_name;
                        $data['copyed'][] = copy(realpath('./storage/checklist_images/'.$checklist->image), './storage/checklist_images/'.$image_name);
                    }

                    $checklist_id = DB::table('drm_task_checklist')->insertGetId([
                        'task_id' => $task_id,
                        'title' => $checklist->title,
                        'status' => $checklist->status,
                        'image' => $image_name,
                        'cms_user_id' => CRUDBooster::myId(),
                    ]);
                }

            });

			return response()->json($data);
        }

		// ------------- edit delete card ----------------
		public function postEditDeleteCard()
		{
			// return response()->json($_REQUEST["card_id"]);

			if($_REQUEST["card_id"])
			{
				$data["card_deleted"] =  DB::table('drm_project_cards')->where('id',$_REQUEST["card_id"])->delete();
			}

			return response()->json($data);

		}

		// ----------------------------- tasks ------------------------//

		public function postAddNewTask()
		{
			// return $_REQUEST;
			// add task
			$task_id = DB::table('drm_project_tasks')->insertGetId([
				'name' => $_REQUEST['task_name'],
				'position' => DB::table('drm_project_tasks')->where('drm_project_card_id',$_REQUEST['card_id'])->count()+1,
				'drm_project_card_id' => $_REQUEST['card_id'],
			]);

			$task['id'] = $task_id;
			$task['name'] = $_REQUEST['task_name'];
			$task['position'] = DB::table('drm_project_tasks')->where('drm_project_card_id',$_REQUEST['card_id'])->count()+1;
			// $data['task']->id = $task_id;
			// $data['task']->position = DB::table('drm_project_tasks')->where('drm_project_card_id',$_REQUEST['card_id'])->count()+1;
			$data['task'] = $task;
			return response()->json($data);
		}

		// get task details
		public function getTaskDetails()
		{
			// $data['list'] = DB::table('drm_project_card_lists')->where('id',$list_id)->first();
			$data['task'] = DB::table('drm_project_tasks')->find($_REQUEST['task_id']);

			// $checklist = DB::table('drm_task_checklist')->where('task_id',$data['task']->id)->get();
			$data['checklist'] = DB::table('drm_task_checklist')->where('task_id',$data['task']->id)->get();
			foreach($data['checklist'] as $item )
			{
				$item->user_name =  (DB::table('cms_users')->find($item->cms_user_id))->name;
			}

			// dd($data['checklist']);

			// $data['checklist'] = $checklist;

			$comments = DB::table('drm_task_comments')->where('task_id',$data['task']->id)->orderBy('created_at')->get();
			// $data['comment']->name = 'asd';
			foreach ($comments as $key => $value) {

				$value->user_name = (DB::table('cms_users')->find($value->cms_user_id))->name;

				// for ($i=1; $i <= 4; $i++) {

					// $value->file.$id = ;
				// }

				$data['comment'][] = $value;

			}

			return response()->json($data);
		}

		// post save task
		public function postSaveTask()
		{
            // return response()->json($_REQUEST);
            $data['request'] = $_REQUEST;
			$data['updated'] = DB::table('drm_project_tasks')->where('id', $_REQUEST['task_id'])->update([

				'title' => $_REQUEST['title'],
				'description' => $_REQUEST['description'],
				'start_date' => $_REQUEST['start_date'],
				'due_date' => $_REQUEST['due_date'],
				'priority' => $_REQUEST['priority'],
				'status' => $_REQUEST['status'],
			]);

			return response()->json($data);
        }

        public function postEditDeleteTask()
		{
			// return response()->json($_REQUEST);

			if($_REQUEST['task_id'] && $_REQUEST['option'] == 'delete')
			{
				// deletting checklist
				DB::table('drm_task_checklist')->where('task_id', $_REQUEST['task_id'])->orderBy('id')->each(function($item){
					if($item->image)
					{
						unlink(realpath('storage/checklist_images/'.$item->image));
					}
				});

				$data['deleted_checklist'] = DB::table('drm_task_checklist')->where('task_id', $_REQUEST['task_id'])->delete();

				// deleting comments
				$data['deleted_comments'] = DB::table('drm_task_comments')->where('task_id', $_REQUEST['task_id'])->delete();

				// deleting task
				$data['deleted_task'] = DB::table('drm_project_tasks')->where('id', $_REQUEST['task_id'])->delete();

			}

			return response()->json($data);
		}

        public function postAddTaskCover()
		{
			// return response()->json($_POST);
			if($_FILES['task_cover']['name'] == null)
			{
				return response()->json('Picture not found', 500);
			}

			$ext  = end(explode(".",$_FILES['task_cover']['name']));
			$name = 'tc'.time().'.'.$ext;

			if(! move_uploaded_file($_FILES['task_cover']['tmp_name'], './storage/list_cover/'.$name))
			{
				return response()->json('File not moved', 500);
			}

			DB::table('drm_project_tasks')->where('id',$_POST['task_id'])->update(['cover_image' => $name]);

			$data['cover_image'] = $name;
			return response()->json($data);
		}


        public function getModalDismisRefresh()
		{
			// return response()->json($_REQUEST);

			// $data['cover_image'] = DB::table('drm_project_tasks')->find($_REQUEST['task_id'])->cover_image;

			$data['task'] = $task = DB::table('drm_project_tasks')->find($_REQUEST['task_id']);

			$checklist = DB::table('drm_task_checklist')->where('task_id',$task->id);
			$data['total'] = $checklist->count();
			$data['done'] = $checklist->where('drm_task_checklist.status','true')->count();

			$data['comment_count'] = DB::table('drm_task_comments')->where('task_id',$task->id)->count();

			return response()->json($data);
		}

		public function postUpdateTaskPosition()
		{
			foreach ((object) $_REQUEST['task_position'] as $item) {
				DB::table('drm_project_tasks')->where( "id",$item['task_id'])
				->update([
					'position' => $item["position"],
					'drm_project_card_id' => $item["card_id"],
				]);
			}
			return $_REQUEST['task_position'];
		}

        public function postCopyTask()
		{
			// return $_REQUEST;

			$task = DB::table('drm_project_tasks')->find($_REQUEST['task_id']);
            // add task
            $count = 0;
            $cover_image = null;
            if($task->cover_image && realpath('./storage/list_cover/'.$task->cover_image))
            {
                $ext  = end(explode(".",$task->cover_image));
                $cover_image = 'tc'.(++$count).time().'.'.$ext;
                $data['copyed'][] = copy(realpath('./storage/list_cover/'.$task->cover_image), './storage/list_cover/'.$cover_image);

            }


			$task_id = DB::table('drm_project_tasks')->insertGetId([
                'drm_project_card_id' => $_REQUEST['card_id'],

                'name' => $task->name,
				'title' => $task->title,
				'description' => $task->description,
				'start_date' => $task->start_date,
				'due_date' => $task->due_date,
				'priority' => $task->priority,
                'status' => $task->status,
                'position' => $task->position,
                'cover_image' => $cover_image,
			]);


			//copy all check list
			foreach((object) DB::table('drm_task_checklist')->where('task_id',$_REQUEST['task_id'])->get() as $item)
			{

                $image_name = null;
                if($item->image && realpath('./storage/checklist_images/'.$item->image))
				{
					$ext  = end(explode(".",$item->image));
                    $image_name = 'cl'.(++$count).time().'.'.$ext;
                    $data['copyed'][] = copy(realpath('./storage/checklist_images/'.$item->image), './storage/checklist_images/'.$image_name);

				}

                $checklist_id = DB::table('drm_task_checklist')->insertGetId([
                	'task_id' => $task_id,
                	'title' => $item->title,
                    'status' => $item->status,
                    'image' => $image_name,
                	'cms_user_id' => CRUDBooster::myId(),
                ]);

			}

            return response()->json($data, 200);

		}
		// insert-task-id-name
		public function getInsertTaskIdName()
		{
			$_GET['count'] = 0;
			DB::table('drm_project_card_lists')->orderBy('id')->each(function ($list){

				DB::table('drm_project_tasks')->where('drm_card_list_id',$list->id)->update([
					'name' => $list->title,
					'drm_project_card_id' => $list->drm_project_card_id,
					'position' => $list->position,
					'cover_image' => $list->image
				]);

				$_GET['count'] = $_GET['count'] +1;
				echo $_GET['count'] . "<br> \n";
			});

		}

		// -------------------- check list ---------------- //
		public function postSaveChecklist()
		{
			// return response()->json($_REQUEST['title']);
			if($_REQUEST['task_id'] && $_REQUEST['title'])
			{
				$checklist_id = DB::table('drm_task_checklist')->insertGetId([
					'task_id' => $_REQUEST['task_id'],
					'title' => $_REQUEST['title'],
					'status' => "false",
					'cms_user_id' => CRUDBooster::myId(),
				]);
			}
			else if ($_REQUEST['checklist_id'] && $_FILES['checklist_image']['name'])
			{
				// return response()->json($_REQUEST);
				// return response()->json($_FILES);

				if($_FILES['checklist_image']['name'] == null)
				{
					return response()->json('Picture not found', 500);
				}

				$ext  = end(explode(".",$_FILES['checklist_image']['name']));
				$name = 'cl'.time().'.'.$ext;

				if(! move_uploaded_file($_FILES['checklist_image']['tmp_name'], './storage/checklist_images/'.$name))
				{
					return response()->json('File not moved', 500);
				}

				DB::table('drm_task_checklist')->where('id', $_REQUEST['checklist_id'])->update(['image' => $name]);
			}
			else
			{
				return response()->json('Invalid request', 500);
			}

			$data['checklist']['id'] = $checklist_id ?? $_REQUEST['checklist_id'];
			$data['checklist']['image'] = $name;
			$data['checklist']['user_name'] = DB::table('cms_users')->find(DB::table('drm_task_checklist')->find($data['checklist']['id'])->cms_user_id)->name;

			return response()->json($data);
		}

		/* check-a-list */
		public function postCheckAList()
		{
			DB::table('drm_task_checklist')->where([
				'id' => $_REQUEST['checklist_id'],
			])->update([
				'status' => $_REQUEST['status'],
			]);

			return response()->json($_REQUEST);
		}

		// --------------------- edit delete checklist ----------
		// edit-delete-checklist
		public function postEditDeleteChecklist()
		{
			// return response()->json($_REQUEST);
			// edit
			if($_REQUEST['checklist_id'] && $_REQUEST['title'])
			{

			}
			// delete
			else if($_REQUEST['checklist_id'])
			{
				$data['deleted'] = DB::table('drm_task_checklist')->where('id',$_REQUEST['checklist_id'])->delete();
			}

			return response()->json($data);
		}

		// -------------------------- Comments ----------------------------- //

		// add-comment
		public function postAddComment()
		{
			// return response()->json($_FILES);
			// return response()->json($_POST);
			// return response()->json($_REQUEST);

			if( $_POST['task_id'] != "" || $_POST['comment'] )
			{
				$have = [];
				$will_be =[];
				$regex = '~(@\w+)~';
				$comment = $_POST['comment'];

				if (preg_match_all($regex, $comment, $matches)) {
					foreach ($matches[0] as $word) {
						if(!in_array($word,$have))
						{
							$have[] = $word;
							$will_be[] = '<b>'.$word.'</b>';
						}
						//   print_r($word) ;
					}
				}

				$coment_id = DB::table('drm_task_comments')->insertGetId([
					'task_id' => $_POST['task_id'],
					'comment' => str_replace($have, $will_be, $comment),
					'cms_user_id' => CRUDBooster::myId(),
				]);

			}
			else if( $_POST['comment_id'] != "" || isset($_FILES["comment_files"]))
			{
				// return response()->json($_REQUEST);
				// return response()->json($_FILES);
        // $file = Request::file('comment_files');
        // $file_type = pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION);
        // $rand=Str::random(40);
        // $path = $file->storeAs('public/csv_files',$rand.'.'.$file_type,['visibility'=>'public','disk'=>'local']);
				$coment_id = $_POST['comment_id'];

				if($_FILES['comment_files']['name'] == null)
				{
					return response()->json('Picture not found', 500);
				}

				$ext  = end(explode(".",$_FILES['comment_files']['name']));
				$name = 'cfs'.$_POST["file_no"].time().'.'.$ext;

				if(! move_uploaded_file($_FILES['comment_files']['tmp_name'], './storage/comment_files/'.$name))
				{
					return response()->json('File not moved', 500);
				}

				// $files = [];
				$files = json_decode(DB::table('drm_task_comments')->where('id',$coment_id)->first()->files, true);
				$files['file'.$_POST["file_no"]] = $name;

				DB::table('drm_task_comments')->where('id',$coment_id)->update([
					// 'file'.$_POST["file_no"] => $name,
					'files' => json_encode($files),
				]);
			}
			else
			{
				return response()->json('Nothing defined',500);
			}

			$data['comment'] = DB::table('drm_task_comments')->find($coment_id);
			$data['comment']->user_name = (DB::table('cms_users')->find($data['comment']->cms_user_id))->name;
			$data['comment']->file_name = $name;

			// $data['comment_id'] = $coment_id;
			return response()->json($data);
		}

		public function getEditComment()
		{
			// return response()->json($_REQUEST);
			$data["comment"] = DB::table('drm_task_comments')->find($_REQUEST['comment_id']);
			return response()->json($data);
		}

		// delete comment file
		public function postDeleteCommentFile()
		{
			// return response()->json($_REQUEST);

			$files_str = DB::table('drm_task_comments')->find($_REQUEST['comment_id'])->files;
			$files = json_decode($files_str,true);

			unset($files[array_search($_REQUEST['file_name'], $files)]);

			$i = 1;
			foreach($files as $file)
			{
				$new_files['file'.$i++] = $file;
			}

			DB::table('drm_task_comments')->where('id',$_REQUEST['comment_id'])->update([
				'files' =>json_encode($new_files)
			]);

			$data['comment']['files'] = $new_files;
			$data['comment']['id'] = $_REQUEST['comment_id'];

			return response()->json($data);
		}

		// ------- edit detete comment
		public function postEditDeleteComment()
		{
			// return response()->json($_REQUEST);

			// edit comment
			if($_REQUEST['comment_id'] && $_REQUEST['comment'])
			{
				$have = [];
				$will_be =[];
				$regex = '~(@\w+)~';
				$comment = $_REQUEST['comment'];

				if (preg_match_all($regex, $comment, $matches)) {
					foreach ($matches[0] as $word) {
						if(!in_array($word,$have))
						{
							$have[] = $word;
							$will_be[] = '<b>'.$word.'</b>';
						}
						//   print_r($word) ;
					}
				}

				$data["edited"] =  DB::table('drm_task_comments')->where('id',$_REQUEST['comment_id'])->update([
					'comment' => str_replace($have, $will_be, $comment),
				]);
				$data['comment'] = str_replace($have, $will_be, $comment);
			}
			else if($_REQUEST['comment_id'] && isset($_FILES["comment_files"]))
			{
				// return response()->json($_REQUEST);
				// return response()->json($_FILES);
				$coment_id = $_POST['comment_id'];

				if($_FILES['comment_files']['name'] == null)
				{
					return response()->json('Picture not found', 500);
				}

				$ext  = end(explode(".",$_FILES['comment_files']['name']));
				$name = 'cfs'.$_POST["file_no"].time().'.'.$ext;

				if(! move_uploaded_file($_FILES['comment_files']['tmp_name'], './storage/comment_files/'.$name))
				{
					return response()->json('File not moved', 500);
				}

				// $files = [];
				$files = json_decode(DB::table('drm_task_comments')->where('id',$coment_id)->first()->files, true);
				$files['file'.$_POST["file_no"]] = $name;

				$data["updated"] = DB::table('drm_task_comments')->where('id',$coment_id)->update([
					// 'file'.$_POST["file_no"] => $name,
					'files' => json_encode($files),
				]);

				$data['comment']['files'] = $files;
				$data['comment']['id'] = $coment_id;

			}
			// delete comment
			else if($_REQUEST['comment_id'])
			{
				$data["delected"] =  DB::table('drm_task_comments')->where('id',$_REQUEST['comment_id'])->delete();

			}
			// delete comment
			return response()->json($data);
		}

		public function postSendInvitation()
		{

			$data['have_account'] = DB::table('cms_users')->where( 'email',$_REQUEST['shared_email'])->first();


			$key = \Illuminate\Support\Str::random(16);
			// dd($_REQUEST);

			DB::table('drm_projects')->where('id',$_REQUEST['project_id'])->update([
			    'shared_key' => $key,
			]);

			$data = [];
            $data['page_title'] = 'Project Invitation';
            $data['project'] = DB::table('drm_projects')->where('id',$_REQUEST['project_id'])->first();
			$data['user'] = DB::table('cms_users')->find( CRUDBooster::myId());


			// return view('admin.drm_projects.invitation_mail',$data);

            if ( !( filter_var( $_REQUEST['shared_email'], FILTER_VALIDATE_EMAIL)) )
            {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'),"Email is not valid","error");
            }

            $this->sendInvitationMail($data, $_REQUEST['shared_email']);

			CRUDBooster::redirect(Request::server('HTTP_REFERER'),"Invitation Send. Project Shared.","success");
		}

		public function getAcceptInvitation($project_id,$key,$user_id)
		{
			// dd($project_id,$key,$user_id);

			$project =DB::table('drm_projects')->find($project_id);

			if($project->cms_user_id != $user_id || $project->shared_key == null)
			{
				CRUDBooster::redirect(CRUDBooster::mainPath(''),"Link expired");
			}

			$user_found = DB::table('drm_project_members')->where([
				'cms_user_id'=> CRUDBooster::myId(),
				'drm_project_id' => $project->id,
			])->first();

			if($user_found)
			{
				DB::table('drm_projects')->where('id',$project_id)->update([
					'shared_key' => null,
				]);

				CRUDBooster::redirect(CRUDBooster::adminPath('drm_projects'),"Project already joined.");
				return "Project already joined.";
			}

			DB::table('drm_project_members')->Insert([
				'cms_user_id'=> CRUDBooster::myId(),
				'drm_project_id' => $project->id,
			]);

			DB::table('drm_projects')->where('id',$project_id)->update([
			    'shared_key' => null,
			]);

			User::find($user_id)->notify(new DRMNotification('Your request have been accepted.','PROJECT_MANAGEMENT_MODULE',CRUDBooster::adminPath().'/drm_projects/project/'.$project_id));
			CRUDBooster::redirect(CRUDBooster::adminPath('drm_projects'),"Project joined.");

		}

		private function sendInvitationMail($data, $target_email ){
			//Use in method
			$has_user = ($data['have_account']->id)? true : false;

			$tags = [
			    'user_name' =>  $data['user']->name,
			    'project_accept_url' =>  CRUDBooster::mainPath('accept-invitation/'.$data['project']->id.'/'.$data['project']->shared_key.'/'.CRUDBooster::myId()),
			    'project_title' =>  $data['project']->title,
			    'USER' =>  $has_user,
			    'GUEST' =>  !$has_user,
			];

			$slug = 'drm_project_invitation'; //Page slugdrm_project_invitation
			$mail_data = DRMParseMailTemplate($tags, $slug); //Generated html
			// Mail::to($target_email)->send(new DRMSEndMail($mail_data)); //Send
			app('drm.mailer')->getMailer()->to($target_email)->send(new DRMSEndMail($mail_data));

		}

		public function app_purchased()
		{
			$assign_menus=DB::table('app_assigns')
                    ->join('app_stores','app_stores.id','app_assigns.app_id')
                    ->where(['app_stores.menu_name'=>'DRM-Projekt','app_assigns.user_id'=>CRUDBooster::myId()])
                    ->count();

			$data=DB::table('purchase_apps')
				->join('app_stores','app_stores.id','=','purchase_apps.app_id')
				->where([
					'app_stores.menu_name'=>'DRM-Projekt',
					'purchase_apps.cms_user_id'=>CRUDBooster::myId()
					])
				->where('purchase_apps.subscription_date_end','>',date('Y-m-d'))
				->count();
			if(($data==0) and (CRUDBooster::myPrivilegeId() ==3) and ($assign_menus==0)){
				return false;
			}

			return true;
		}

	}

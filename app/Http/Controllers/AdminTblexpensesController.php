<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;

	class AdminTblexpensesController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "expense_name";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = true;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "tblexpenses";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Name","name"=>"expense_name"];
			$this->col[] = ["label"=>"Note","name"=>"note"];
			$this->col[] = ["label"=>"Expense Category","name"=>"category","join"=>"tblexpensescategories,name"];
			$this->col[] = ["label"=>"Expense Date","name"=>"date"];
			$this->col[] = ["label"=>"Amount","name"=>"amount"];
			// $this->col[] = ["label"=>"Category","name"=>"category"];
			// $this->col[] = ["label"=>"Currency","name"=>"currency"];
			$this->col[] = ["label"=>"Currency","name"=>"currency","join"=>"tblcurrencies,name"];
			$this->col[] = ["label"=>"Tax","name"=>"tax"];
			$this->col[] = ["label"=>"Tax2","name"=>"tax2"];
			$this->col[] = ["label"=>"Reference No","name"=>"reference_no"];
			$this->col[] = ["label"=>"Status","name"=>"status"];
			$this->col[] = ["label"=>"Paymentmode","name"=>"paymentmode"];
			$this->col[] = ["label"=>"Repeat Every","name"=>"repeat_every"];



			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			$this->form[] = ['label'=>'Name','name'=>'expense_name','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Note','name'=>'note','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Expensescategories','name'=>'category','type'=>'select2','validation'=>'required','width'=>'col-sm-10','datatable'=>'tblexpensescategories,name'];
			$this->form[] = ['label'=>'Expense Date','name'=>'date','type'=>'date','validation'=>'required|date','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Amount','name'=>'amount','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Currency','name'=>'currency','type'=>'select2','validation'=>'required','width'=>'col-sm-10','datatable'=>'tblcurrencies,name'];
			$this->form[] = ['label'=>'Tax','name'=>'tax','type'=>'select','validation'=>'required','width'=>'col-sm-10','dataenum'=>'1|4.00%;2|7.00%;3|10.00%;4|19.00%;5|21.00%'];
			$this->form[] = ['label'=>'Tax2','name'=>'tax2','type'=>'select','validation'=>'required','width'=>'col-sm-10','dataenum'=>'1|4.00%;2|7.00%;3|10.00%;4|19.00%;5|21.00%'];
			$this->form[] = ['label'=>'Reference No','name'=>'reference_no','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Status','name'=>'status','type'=>'select','validation'=>'required|min:1|max:255','width'=>'col-sm-10','dataenum'=>'1|Offen;2|Behazlt'];
			$this->form[] = ['label'=>'Paymentmode','name'=>'paymentmode','type'=>'select','validation'=>'required','width'=>'col-sm-10','dataenum'=>'1|Test Bank;2|Bank'];
			$this->form[] = ['label'=>'Repeat Every','name'=>'repeat_every','type'=>'select','validation'=>'required','width'=>'col-sm-10','dataenum'=>'1|one weeks;2|Two Weeks;2|One Months;3|Two Months;4|Three Months'];
			$this->form[] = ['label'=>'Clientid','name'=>'clientid','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'File','name'=>'file','type'=>'upload','width'=>'col-sm-9'];
			$this->form[] = ['label'=>'Contactid','name'=>'contactid','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'tblprojects,id'];
			$this->form[] = ['label'=>'Project Id','name'=>'project_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Billable','name'=>'billable','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Invoiceid','name'=>'invoiceid','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Recurring Type','name'=>'recurring_type','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Recurring','name'=>'recurring','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Recurring Ends On','name'=>'recurring_ends_on','type'=>'date','validation'=>'required|date','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Custom Recurring','name'=>'custom_recurring','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Last Recurring Date','name'=>'last_recurring_date','type'=>'date','validation'=>'required|date','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Create Invoice Billable','name'=>'create_invoice_billable','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Send Invoice To Customer','name'=>'send_invoice_to_customer','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Recurring From','name'=>'recurring_from','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Dateadded','name'=>'dateadded','type'=>'datetime','validation'=>'required|date_format:Y-m-d H:i:s','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Addedfrom','name'=>'addedfrom','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Cr Date','name'=>'cr_date','type'=>'datetime','validation'=>'required|date_format:Y-m-d H:i:s','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Red Dot Display','name'=>'red_dot_display','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ['label'=>'Name','name'=>'expense_name','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//
			//$this->form[] = ['label'=>'Note','name'=>'note','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Expensescategories','name'=>'category','type'=>'select2','validation'=>'required','width'=>'col-sm-10','datatable'=>'tblexpensescategories,name'];
			//$this->form[] = ['label'=>'Expense Date','name'=>'date','type'=>'date','validation'=>'required|date','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Amount','name'=>'amount','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//// $this->form[] = ['label'=>'Category','name'=>'category','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Currency','name'=>'currency','type'=>'select2','validation'=>'required','width'=>'col-sm-10','datatable'=>'tblcurrencies,name'];
			//$this->form[] = ['label'=>'Tax','name'=>'tax','type'=>'select','validation'=>'required','dataenum'=>'1|4.00%;2|7.00%;3|10.00%;4|19.00%;5|21.00%','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Tax2','name'=>'tax2','type'=>'select','validation'=>'required','dataenum'=>'1|4.00%;2|7.00%;3|10.00%;4|19.00%;5|21.00%','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Reference No','name'=>'reference_no','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Status','name'=>'status','type'=>'select','validation'=>'required|min:1|max:255','dataenum'=>'1|Offen;2|Behazlt','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Paymentmode','name'=>'paymentmode','type'=>'select','validation'=>'required','dataenum'=>'1|Test Bank;2|Bank','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Repeat Every','name'=>'repeat_every','type'=>'select','validation'=>'required',"dataenum"=>"1|one weeks;2|Two Weeks;2|One Months;3|Two Months;4|Three Months",'width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Clientid','name'=>'clientid','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Contactid','name'=>'contactid','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Project Id','name'=>'project_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'tblprojects,id'];
			//$this->form[] = ['label'=>'Billable','name'=>'billable','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Invoiceid','name'=>'invoiceid','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Recurring Type','name'=>'recurring_type','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//
			//$this->form[] = ['label'=>'Recurring','name'=>'recurring','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Recurring Ends On','name'=>'recurring_ends_on','type'=>'date','validation'=>'required|date','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Custom Recurring','name'=>'custom_recurring','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Last Recurring Date','name'=>'last_recurring_date','type'=>'date','validation'=>'required|date','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Create Invoice Billable','name'=>'create_invoice_billable','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Send Invoice To Customer','name'=>'send_invoice_to_customer','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Recurring From','name'=>'recurring_from','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Dateadded','name'=>'dateadded','type'=>'datetime','validation'=>'required|date_format:Y-m-d H:i:s','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Addedfrom','name'=>'addedfrom','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Cr Date','name'=>'cr_date','type'=>'datetime','validation'=>'required|date_format:Y-m-d H:i:s','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Red Dot Display','name'=>'red_dot_display','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//// $this->form[] = ['label'=>'Status','name'=>'status','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
				$user_id = CRUDBooster::myId();
				if(!CRUDBooster::isSuperadmin()){
					$query->where('user_id',$user_id);
				}

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	//Your code here

				if($column_index==7) {
				   if($column_value==1) {
						 return $column_value="4.00%";
					 }
					 elseif($column_value==2) {
						return $column_value="7.00%";
					}
					 elseif($column_value==3) {
						return $column_value="10.00%";
					}
					 elseif($column_value==4) {
						return $column_value="19.00%";
					}
					 elseif($column_value==5) {
						return $column_value="21.00%";
					}


				}
			   if($column_index==8) {
					 if($column_value==1) {
						 return $column_value="4.00%";
					 }
					 elseif($column_value==2) {
						return $column_value="7.00%";
					}
					 elseif($column_value==3) {
						return $column_value="10.00%";
					}
					 elseif($column_value==4) {
						return $column_value="19.00%";
					}
					 elseif($column_value==5) {
						return $column_value="21.00%";
					}


				}


				if($column_index==10) {
					 if($column_value==1) {
						 return $column_value="Offen";
					 }
					 elseif($column_value==2) {
						return $column_value="Behazlt";

				}
			}
				if($column_index==11) {
					 if($column_value==1) {
						 return $column_value="Test Bank";
					 }
					 elseif($column_value==2) {
						return $column_value="Test";

				}
			}
			if($column_index==12) {
				 if($column_value==1) {
					 return $column_value="One weeks";
				 }
				 elseif($column_value==2) {
					return $column_value="Two weeks";
				}
				 elseif($column_value==3) {
					return $column_value="One Months";
				}
				 elseif($column_value==4) {
					return $column_value="Two Months";
				}
				 elseif($column_value==5) {
					return $column_value="Three Months";
				}


			}



	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here
					$postdata['user_id'] = CRUDBooster::myId();

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        $postdata['user_id'] = CRUDBooster::myId();

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }



	    //By the way, you can still create your own method in here... :)


	}
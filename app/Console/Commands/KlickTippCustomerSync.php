<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\KlickTippWebhookResponseProcessor;
use AppStore;
use App\KlickTippHistory;
use GuzzleHttp\Client;

class KlickTippCustomerSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:customer';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Customer Synchronization';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try{
            // collection of all request that should be sync
            $klickTippRequestConsole = \DB::table('klick_tipp_requests')->where('status', 'ok')->where('last_sync_date', '!=', null)->get();

            $client = new Client();

            $klickTippRequestConsole->each(function($request){
                if ($request->sync_ready_at != null){

                    // app active or not checker
                    $purchase_check  = AppStore::CheckAppPurchaseBooleanConsole('40', $request->user_id);
                    // if active continue
                    if($purchase_check){
                        $url = "http://*************/customers/".$request->user_id."/subscribers?offset=0&limit=1";
            
                        $response  = $client->get($url);
                        $result = json_decode($response->getBody()->getContents(), TRUE);
        
                        $totalSubscribers = $result['result']['total'];

                        $existingRequestHistory= KlickTippHistory::where('klickTippId', $request->id)->orderBy('totalSync', 'DESC')->first();
                        $total_sync_now = ($totalSubscribers - $existingRequestHistory->totalSync);
                        
                        if($total_sync_now>0){
                            KlickTippWebhookResponseProcessor::dispatch($request->user_id, $request->last_sync_date);
                        }
                    }
                }
            });

        }catch( \Exception $e){
            print "KlickTipp Subscriber Sync Failed\n";
        }finally {
            print "KlickTipp Subscriber Sync End\n";
        }
    }
}

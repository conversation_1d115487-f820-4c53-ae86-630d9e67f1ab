<?php

namespace App\Console\Commands;

use App\Http\Controllers\NewShopSyncController;
use App\Jobs\SuspendUser\DisconnectProducts;
use App\Jobs\SuspendUser\RemoveProducts;
use App\Shop;
use Illuminate\Console\Command;

class TestScript extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'script:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test script';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        app(NewShopSyncController::class)->syncMiraklConnectOrder(Shop::find(1465));
    }
}

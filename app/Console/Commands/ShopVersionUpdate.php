<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use DB;

class ShopVersionUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shop:version-update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Shop version update';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        \App\Shop::where('channel', 10)
        ->get()
        ->each(function($shop) {
            try {
                $url  = rtrim($shop->url, '/').'/api/v1/shop_version?userToken='.$shop->username.'&userPassToken='.$shop->password;
                $curl = curl_init($url);
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
                $data = curl_exec($curl);
                curl_close($curl);
                $version = @json_decode($data, true)['version'];

                if(!empty($version)) {
                    \App\Shop::where('id', '=', $shop->id)->update(['shop_version' => $version]);
                }
            }catch(\Exception $e) {}
        });
    }
}

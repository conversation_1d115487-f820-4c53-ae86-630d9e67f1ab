<?php

namespace App\Console\Commands;

use App\Jobs\ProcessAnalysisArchiveDeleteJob;
use Illuminate\Console\Command;

class ProcessAnalysisArchiveDelete extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keepa:delete_archived_analysis_products';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Old Archived Analysis Products';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ProcessAnalysisArchiveDeleteJob::dispatch();
    }
}

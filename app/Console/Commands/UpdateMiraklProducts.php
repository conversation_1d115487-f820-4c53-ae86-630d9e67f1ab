<?php

namespace App\Console\Commands;

use App\Enums\Channel;
use App\Enums\V2UserAccess;
use App\Services\Modules\Export\Mirakl\MiraklChannelManager;
use App\Shop;
use Illuminate\Console\Command;

class UpdateMiraklProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mirakl:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    public function handle()
    {
        $shops = Shop::whereIn(
            'channel', Channel::MIRAKL_CHANNELS
        )->get();

        foreach ($shops as $shop) {
            $ids = \DB::table('channel_product_update_histories')
                ->join('channel_products', 'channel_products.id', '=', 'channel_product_update_histories.channel_product_id')
                ->where(['shop_id' => $shop->id, 'synced' => false])
                ->whereNotIn('user_id',V2UserAccess::USERS)
                ->pluck('channel_product_id')->toArray();

            if(empty($ids)) continue;

            $service = new MiraklChannelManager();
            $channel = $service($shop->id);

            try {
                $channel->export($ids, "UPDATE");
                \DB::table('channel_product_update_histories')
                    ->whereIn('channel_product_id', $ids)
                    ->update(['synced' => true]);
            }
            catch (\Exception $e) {
                debug_log('Unable to update mirakl channel ' . $e->getMessage(),'MIRAKL_UPDATE_ERROR');
            }

        }
    }
}

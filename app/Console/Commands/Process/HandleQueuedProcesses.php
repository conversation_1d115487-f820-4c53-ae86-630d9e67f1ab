<?php

namespace App\Console\Commands\Process;

use App\Enums\Actions;
use App\Enums\Channel;
use App\Jobs\DestroyChannelProduct;
use App\Models\ChannelExportProcess;
use App\Services\ChannelProductService;
use App\Services\Modules\Export\Request\ExportRequestService;
use App\Models\Process;
use Illuminate\Console\Command;

class HandleQueuedProcesses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'process:handle';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handle queued processes';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        if(empty(\App\Enums\V2UserAccess::USERS)) return;

        try {
            $this->info('Fetching pending processes');

            Process::where('status', 0)
                ->whereNotIn('user_id', \App\Enums\V2UserAccess::USERS)
                ->orderBy('id', 'desc')
                ->chunk(100, function ($processes) {
                    foreach ($processes as $process) {
                        $exportProcesses = ChannelExportProcess::where('process_id', $process->id)
                            ->where('status', 0)
                            ->orderBy('id', 'desc')
                            ->get();

                        if ($exportProcesses->isNotEmpty()) {
                            $productIds = $exportProcesses->pluck('product_id')->toArray();

                            $firstRecord = $exportProcesses->first();
                            $shop = $this->getShop($firstRecord->shop_id);
                            $metadata = $firstRecord->metadata;

                            ChannelExportProcess::whereIn('product_id', $productIds)->update(['status' => 1]);

                            switch ($process->action_id) {
                                case Actions::EXPORT_PRODUCT:
                                    app(ChannelProductService::class)->exportToShop($productIds,$shop->id,$shop->user_id,"create",$metadata['additional'] ?? []);
                                    break;
                                case Actions::DISCONNECT_PRODUCT:
                                    app(ChannelProductService::class)->deleteFromShop($productIds,$shop->id,$shop->user_id,$metadata['incomplete']);
                                    break;

                                case Actions::DELETE_PRODUCT:
                                    DestroyChannelProduct::dispatch($productIds,$shop->id,$shop->user_id);
                                     break;
                                default:
                                    break;
                            }
                        }
                    }
                });
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    }

    private function getShop($shopId)
    {
        try {
            return \DB::table('shops')->where('id', $shopId)->first();
        } catch (\Exception $e) {
            $this->error('Error executing query: ' . $e->getMessage());
            throw $e;
        }
    }
}

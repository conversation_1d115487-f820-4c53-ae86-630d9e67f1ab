<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\OverdueInvoiceNotificationJob;
use App\Jobs\DropfunnelCsvEmailJob;

class SendOverdueNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'overdue:notify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Upcomming invoice overdue notification';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        OverdueInvoiceNotificationJob::dispatch();
        DropfunnelCsvEmailJob::dispatch()->onQueue('database-long-running');
    }
}

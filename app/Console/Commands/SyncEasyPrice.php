<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product\ProfitCalculation;
use App\Models\ChannelProduct;

class SyncEasyPrice extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'easy-price:fix';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync easy price';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $calculations = ProfitCalculation::where('default',1)->get();
        foreach ($calculations as $calculation) {
            $products = ChannelProduct::where('calculation_id',$calculation->id)->cursor();
            foreach ($products as $product) {
                $price = app('App\Services\ChannelProductService')->calculatePrice($product,$calculation);
                if($price!=$product->vk_price){
                    app('App\Services\ChannelProductService')->update($product->id,[
                        'vk_price' => $price,
                        'calculation_id' => $calculation->id
                    ]);
                }
            }
        }
    }
}

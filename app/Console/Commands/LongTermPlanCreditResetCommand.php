<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\LongTermPlanCreditResetJob;

class LongTermPlanCreditResetCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'credit:long-term-reset';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Long term credit reset';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        LongTermPlanCreditResetJob::dispatch();
    }
}

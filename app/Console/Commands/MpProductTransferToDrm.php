<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\TransferMarketplaceToDrm;
use Illuminate\Support\Facades\DB;
use App\Jobs\Marketplace\MarketplaceProductTransferToDrm;
use Illuminate\Support\Facades\Log;

class MpProductTransferToDrm extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:product-transfer';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace Product Transfer To DRM';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $all_user_ids = TransferMarketplaceToDrm::distinct('user_id')->pluck('user_id')->toArray();

        if(!empty($all_user_ids)){
            foreach($all_user_ids as $user_id){
                $user_plan = app('App\Services\UserService')->userPlanStatus($user_id);

                $transferableproducts = TransferMarketplaceToDrm::where('user_id', $user_id)->where('status', 0);

                if(!empty($user_plan)){
                    if($user_plan['plan'] == 'Trial'){
                        $transferableproducts->chunk(400, function($products) use($user_id){
                            $mp_product_ids = $products->pluck('mp_product_id')->toArray();

                            MarketplaceProductTransferToDrm::dispatch($mp_product_ids, $user_id, null, true);
                        });
                    }else{
                        $transferableproducts->chunk(400, function($products) use($user_id){
                            $mp_product_ids = $products->pluck('mp_product_id')->toArray();

                            MarketplaceProductTransferToDrm::dispatch($mp_product_ids, $user_id);
                        });
                    }
                }
            }
        }
    }
}

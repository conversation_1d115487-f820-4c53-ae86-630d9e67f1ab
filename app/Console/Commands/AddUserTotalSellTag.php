<?php

namespace App\Console\Commands;

use App\Models\UserDetails;
use App\DropfunnelCustomerTag;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class AddUserTotalSellTag extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sell_tag:Add';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add sell tag for cms users privilige id 3,4';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */


    public function handle()
    {
        Log::channel('command')->info('Start Sale tagg adding process');
        // Define the thresholds and corresponding tags
        $thresholds = [
            10000000 => 'award1M',
            5000000 => 'award5000k',
            500000 => 'award500k',
            250000 => 'award250k',
            100000 => 'award100k',
            10000  => 'award10k'
        ];// Define the thresholds and corresponding tags

        // Process UserDetails records in chunks
        foreach ($this->processUserDetails() as $userDetail) {
            foreach ($thresholds as $threshold => $tag) {
                // Check if the total sell amount meets the threshold
                if ($userDetail->total_amount >= $threshold) {
                    // Check if the user already has the tag
                    $existingTag = $userDetail->dropfunnelTags->firstWhere('tag', $tag);
                    Log::channel('command')->info('exist ' . ' Tag '.$tag);
                    // If the tag doesn't exist, create it
                    if (!$existingTag) {
                        DropfunnelCustomerTag::insertTag($tag, $userDetail->user_id, $userDetail->customer_id, 1);
                    }

                    // Break the loop after processing the user for the current threshold
                    break;
                }
            }
        }
    }
    // Define the generator function to process UserDetails records in chunks
    private function processUserDetails() {
        try {
            foreach (UserDetails::where('total_amount', '>=', 10000)->cursor() as $userDetail) {
                yield $userDetail;
            }
        } catch (\Exception $e) {
            Log::error('Error occurred while processing UserDetails records: ' . $e->getMessage());
        }
    }
}

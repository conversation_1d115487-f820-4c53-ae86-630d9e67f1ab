<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\FinanceAutoExport;

class HandlingTimeDecrement extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'handlingTime:decrement';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This is used for decrementing Handling time';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try{
            app('App\Http\Controllers\AdminDrmAllOrdersController')->decrementHandlingTime();
        }catch(\Exception $e){

        }

        try {
            FinanceAutoExport::dispatch();
        }catch (\Exception $e) {}
        
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class CountryJson extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:country-json';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate country list json to local storage';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $countryArr = DB::table('tax_rates')
            ->select('country', 'country_de', 'country_code')
            ->get()
            ->map(function ($item) {
                return [
                    $item->country => $item->country_code,
                    $item->country_de => $item->country_code,
                    $item->country_code => $item->country_code,
                ];
            })
            ->toArray();

        Storage::disk('local')->put('country/country.json', json_encode(array_merge(...$countryArr)));
        return 0;
    }
}

<?php

namespace App\Console\Commands;

use App\Jobs\ProcessMPRatingUpdateJob;
use Illuminate\Console\Command;
use App\Models\Product\AnalysisProduct;
use App\Jobs\ProcessGoogleMerchantUpdateJob;
use Illuminate\Support\Facades\DB;

class ProcessGoogleMerchantUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keepa:update_google_merchant_reports';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Google Merchant Reports';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::table('google_oauth')->get()->each(function ($item) {
            ProcessGoogleMerchantUpdateJob::dispatch($item->id, $item->user_id);
        });
    }
}

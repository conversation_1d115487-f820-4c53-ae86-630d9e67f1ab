<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\DRMManualSubscriptionJob;

class DRMManualSubscriptionCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'drm_manual:subscription';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'DRM Manual subscription Command';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DRMManualSubscriptionJob::dispatch();
    }
}

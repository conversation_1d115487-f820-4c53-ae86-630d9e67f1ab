<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class SyncUniversalFeeds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'universal-feeds:sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync universal feeds';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->syncChannelFeeds();
        $this->syncProductFeeds();
    }

    private function syncChannelFeeds()
    {
        $feeds = DB::table('channel_universal_feeds')->get();

        foreach ($feeds as $feed)
        {
            $url = rtrim(config('app.feed_base_url'), '/') . '/universal-feed-h2/' . $feed->slug;
            Storage::disk('spaces')->put($feed->feed_url, file_get_contents_utf8($url), 'public');
        }
    }

    private function syncProductFeeds()
    {
        $feeds = DB::table('product_universal_feeds')->get();

        foreach ($feeds as $feed)
        {
            $url = rtrim(config('app.feed_base_url'), '/') . '/universal-feed-h1/' . $feed->slug;
            Storage::disk('spaces')->put($feed->feed_url, file_get_contents_utf8($url), 'public');
        }
    }
}

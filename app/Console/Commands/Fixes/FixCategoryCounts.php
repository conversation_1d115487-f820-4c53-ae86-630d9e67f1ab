<?php

namespace App\Console\Commands\Fixes;

use Illuminate\Console\Command;
use App\Models\ChannelUserCategory;
use App\Jobs\ChannelManager\SyncCategoryProductCount;

class FixCategoryCounts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'channel-category:count-fix';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix channel category issues';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if(empty(\App\Enums\V2UserAccess::USERS)) return;

        $users = ChannelUserCategory::whereNotIn('user_id', \App\Enums\V2UserAccess::USERS)
        ->pluck('user_id')->unique()->toArray();
        
        foreach (array_filter($users) as $user) {
            $categories = ChannelUserCategory::where('user_id',$user)->pluck('id')->toArray();
            SyncCategoryProductCount::dispatch($categories,10,$user);
        }
    }
}

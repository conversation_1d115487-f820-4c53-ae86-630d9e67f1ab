<?php

namespace App\Console\Commands;

use App\Jobs\MpBestSellingProductsUpdateJob;
use Illuminate\Console\Command;

class MpBestSellingProductsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:best-sold-products';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Best Sold Porducts';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        MpBestSellingProductsUpdateJob::dispatch();
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\AppointmentRenewMonthly;

class AppointmentRenewal extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'appointment:renewal';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Appoinment renewal command';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        AppointmentRenewMonthly::dispatch();
    }
}

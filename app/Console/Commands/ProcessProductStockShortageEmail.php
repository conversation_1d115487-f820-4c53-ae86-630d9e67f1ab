<?php

namespace App\Console\Commands;

use App\Jobs\ProcessProductStockShortageEmailJob;
use Illuminate\Console\Command;

class ProcessProductStockShortageEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keepa:send_product_stock_shortage_email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send DRM Product Stock Shortage Email';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ProcessProductStockShortageEmailJob::dispatch();
    }
}

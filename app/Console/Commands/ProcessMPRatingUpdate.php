<?php

namespace App\Console\Commands;

use App\Jobs\ProcessMPRatingUpdateJob;
use Illuminate\Console\Command;
use App\Models\Product\AnalysisProduct;

class ProcessMPRatingUpdate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keepa:update_mp_avg_rating_column';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update avg_rating column in marketplace_products';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        AnalysisProduct::where('source', 2)->where('archived', '<>', 1)->chunk(1000, function($rows){
            $ids = $rows->pluck('id')->toArray();
            ProcessMPRatingUpdateJob::dispatch($ids);
        });
    }
}

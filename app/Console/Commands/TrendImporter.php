<?php

namespace App\Console\Commands;

use App\Jobs\TrendImport;
use App\Models\AppStore\PurchasedApp;
use App\Services\AppStoreService;
use App\TrendCategories;
use Illuminate\Console\Command;

class TrendImporter extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keepa:trendImport';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import trends to users account';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Trend Importer
     * Transfer Products from market place to users drm channel
     */


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
//        $plans = config('global.trend_importer_app_plans');
//
//        $app_ids = TrendCategories::select('drm_app_id')->pluck('drm_app_id');
//        $purchased_list = PurchasedApp::with('trend_categories:keepa_cat_id,drm_app_id')
//                            ->whereIn('app_id', $app_ids)
//                            ->get();
//
//        foreach ($purchased_list as $key => $purchased) {
//            $plan_id = app(AppStoreService::class)->getAppPlan($purchased->app_id, $purchased->cms_user_id);
//            $plan_id = ($plan_id == 'trial' ) ? key($plans) : $plan_id; #get first plan id if trial
//
//            $package_limit = $plans[$plan_id];
//            $keepa_cat_id = $purchased->trend_categories['keepa_cat_id'];
////             TrendImport::dispatch($purchased->cms_user_id, $keepa_cat_id, $package_limit)->onQueue('keepa_import');
//        }
    }
}

<?php

namespace App\Console\Commands\Marketplace;

use App\Models\Marketplace\Product;
use Carbon\Carbon;
use Illuminate\Console\Command;
use App\Jobs\Marketplace\BulkMarketplaceProductDelete;


class EladyStockOutProductDelete extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'elady:stockoutproductdelete';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'MP eLady supplier stock out product delete';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // app(\App\Services\Marketplace\BulkMarketplaceProductDelete::class)->eladyStockOutProductDelete();

        return true;
        
        ini_set('max_execution_time', '0');
        $products = Product::select('id','ean','image')
                    ->with('core_products:marketplace_product_id,id,user_id')
                    ->where('stock',0)
                    ->where('delivery_company_id',14379)
                    ->where('stock_updated_at','<=',Carbon::now()->subDays(7))
                    ->limit(1000)
                    ->get();

        if(!blank($products)){
            foreach ($products->chunk(50) as $product) {
                dispatch(new BulkMarketplaceProductDelete($product));
            }
        }else{
            info("MP eLady 0 stock, product not found");
        }
    }
}

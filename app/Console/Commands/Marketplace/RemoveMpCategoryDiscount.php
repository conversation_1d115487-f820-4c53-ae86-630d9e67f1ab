<?php

namespace App\Console\Commands\Marketplace;

use Illuminate\Console\Command;
use App\Jobs\Marketplace\CategoryDiscountRemoveSync;
use App\Models\Marketplace\Category;
use App\Models\Marketplace\MarketplaceParentCategory;
use Illuminate\Support\Facades\Log;

class RemoveMpCategoryDiscount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:remove-caregory-discount';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace category discount remove';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        MarketplaceParentCategory::where('is_offer_active', 1)->where('end_date', '<', now())->update([
            'is_offer_active' => 0,
        ]);
        $categories = Category::where('is_offer_active', 1)->where('end_date', '<', now())->get();

        foreach($categories as $category){
            CategoryDiscountRemoveSync::dispatch($category);
        }
    }
}

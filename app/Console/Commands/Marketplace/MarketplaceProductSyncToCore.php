<?php

namespace App\Console\Commands\Marketplace;

use Illuminate\Console\Command;
use App\Models\Marketplace\CoreSyncReport;
use App\Models\Marketplace\Product;
use App\DrmProduct;
use App\Models\Marketplace\ProductSyncHistory;

class MarketplaceProductSyncToCore extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mp-core:sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace product sync to core';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        return true;
        $syncHistories = ProductSyncHistory::where('tries','<',3)
                        ->where('status',0)->cursor();

        foreach($syncHistories as $syncHistory){
            try {
                switch ($syncHistory->event) {
                    case 'update':
                        $coreProducts = DrmProduct::where('marketplace_product_id',$syncHistory->marketplace_product_id)
                        ->pluck('id')->toArray();
                        
                        if(!empty($coreProducts)){
                            app('App\Services\DRMProductService')->update(
                                $coreProducts,
                                $syncHistory->metadata
                            );
                        }
                        break;
                        
                    case 'delete':
                        $coreProducts = DrmProduct::where('marketplace_product_id',$syncHistory->marketplace_product_id)
                                        ->select('id','user_id')->get();
                        
                        foreach ($coreProducts as $coreProduct) {
                            app('App\Services\DRMProductService')->destroy(
                                $coreProduct->id,
                                $coreProduct->user_id
                            );
                        }
                        break;
                }
                    
                
                $syncHistory->status = 1;
            } catch (\Throwable $th) {
                $syncHistory->log = $th->getMessage();
                $syncHistory->tries++;
            } finally {
                $syncHistory->save();
            }
        }
    }
}

<?php

namespace App\Console\Commands\Marketplace;

use Illuminate\Console\Command;
use App\Models\Marketplace\Product;
use App\Jobs\Marketplace\DuplicateEanProductActive;

class BestDuplicateEanProductActive extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:best-price-duplicate-ean-product-active';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace duplicate EAN best price products active';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try{
            info('Duplicate ean product active starting...');
            $country_ids = Product::distinct()->pluck('country_id');
            foreach($country_ids as $country_id){
                Product::where('country_id', $country_id)->select('ean')->groupBy('ean')->havingRaw('COUNT(*) > 1')
                    ->chunk(1500, function ($duplicate_eans) use ($country_id){
                        $duplicate_eans = $duplicate_eans->pluck('ean');
                        info('Duplicate ean product active starting for country_id: '. $country_id);
                        dispatch(new DuplicateEanProductActive($duplicate_eans, $country_id));
                    });
                }

                    // Product::whereIn('ean', $duplicate_eans)
                    //     ->whereNotIn('status', [2, 7, 5])
                    //     ->update(['status' => 0, 'is_dublicate' => 1, 'best_price' => 0]);

                    // foreach ($duplicate_eans as $duplicate_ean) {
                    //     $active_priority_product = Product::with('mainCategory:id,im_handel')
                    //         ->where('ean', $duplicate_ean)
                    //         ->whereNotIn('status', [2, 7, 5])
                    //         ->where('shipping_method', 2)
                    //         ->where('internel_stock', '>=', 1)
                    //         ->where('vk_price', '<>', 0)
                    //         ->select('id','ean','best_price','vk_price','ek_price','shipping_cost','shipping_method','stock','internel_stock','status', 'category_id')
                    //         ->orderByRaw('`ek_price` + `shipping_cost`')
                    //         ->first();

                    //     // If no such product, then find the best price product
                    //     if (!$active_priority_product) {
                    //         $active_priority_product = Product::with('mainCategory:id,im_handel')
                    //             ->where('ean', $duplicate_ean)
                    //             ->whereNotIn('status', [2, 7])
                    //             ->where('shipping_method', 1)
                    //             ->where('stock', '>=', 1)
                    //             ->where('vk_price', '<>', 0)
                    //             ->select('id','ean','best_price','vk_price','ek_price','shipping_cost','shipping_method','stock','internel_stock','status', 'category_id')
                    //             ->orderByRaw('`ek_price` + `shipping_cost`')
                    //             ->first();
                    //     }

                    // // Update the selected product
                    // if ($active_priority_product) {

                    //         $duplicate_ids = Product::where('id', '<>', $active_priority_product->id)
                    //             ->where('ean', $active_priority_product->ean)
                    //             ->tap(function ($query) use ($active_priority_product) {
                    //                 $query->update(['category_id' => $active_priority_product->category_id ?? 68]);
                    //             })
                    //             ->pluck('id')
                    //             ->push($active_priority_product->id)
                    //             ->toArray();
                
                    //         dispatch(new MarketplaceIMHandelSync($duplicate_ids, $active_priority_product->mainCategory->im_handel ?? 0.00));
                    //         $active_priority_product->update(['status' => 1, 'best_price' => 1]);
                    //     }
                    // }
        } catch (\Exception $e) {
            info($e);
        }
    }
}

<?php

namespace App\Console\Commands\Marketplace;


use DB;
use Illuminate\Console\Command;
use App\Models\Marketplace\Product;
use App\Jobs\Marketplace\FulfilmentProductAcceptedMail;


class FulfilmentProductAutoConfirmShipment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mp_fulfilment_auto:confirm_shipment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace fulfilment product auto confirm shipment';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $supplierIds = [];
        $products =  DB::connection('marketplace')->table('marketplace_products')
        ->where('marketplace_products.shipping_method',2)->where('marketplace_products.internel_sync_status',6)
        ->whereBetween('marketplace_products.internel_stock_received_at', [now()->subMinutes(1440), now()])
        ->select("marketplace_products.id","marketplace_products.supplier_id")
        ->leftJoin('fulfilment_stock_send_log', function($join) {
            $join->on('marketplace_products.id', '=', 'fulfilment_stock_send_log.Product_id');
        })->whereNull('fulfilment_stock_send_log.is_left')->get();

        if(!blank($products)){
            foreach($products as $product){
                try {
                  Product::where('id',$product->id)->update([
                    'status' => 1,
                    'internel_sync_status' => 0,
                    'internel_stock_received_at'=> null,
                  ]);
                  array_push($supplierIds,$product->supplier_id ?? null);
                } catch (\Throwable $th) {
                    $th->getMessage();
                }
            }
            foreach(array_unique(array_filter($supplierIds)) as $supplierId){
              FulfilmentProductAcceptedMail::dispatch($supplierId);
            }
        }
    }
}

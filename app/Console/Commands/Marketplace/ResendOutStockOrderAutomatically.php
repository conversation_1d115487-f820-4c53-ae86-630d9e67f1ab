<?php

namespace App\Console\Commands\Marketplace;

use Illuminate\Console\Command;
use App\NewOrder;
use App\Models\Marketplace\Product;


class ResendOutStockOrderAutomatically extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'resendOrder:resend-out-stock-order-automatically';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Resend order when out stock product stock avaailable';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public  $out_stock_orders;
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $out_stock_orders = NewOrder::where('cms_user_id', 2455)
            ->where('insert_type', 8)
            ->where('status', 'stock_unavailable')
            ->whereNull('credit_ref')
            ->whereNull('mp_api_id')
            ->whereDate('created_at', '>', '2024-03-01')
            ->select('id', 'cart', 'cms_client')
            ->get();

        info('Stock out product order resend start');
        foreach($out_stock_orders as $out_stock_order){
            $order_cart = reset(json_decode($out_stock_order->cart));
            $mp_product = Product::where('id', $order_cart->marketplace_product_id)
                ->select('id', 'ean', 'stock', 'shipping_method', 'internel_stock')
                ->first();

            $shipping_method = $mp_product->shipping_method;
            $quantity = $order_cart->qty;
            if (!blank($mp_product) && (
                    ($shipping_method == 2 && $mp_product->internel_stock >= $quantity) ||
                    ($shipping_method == 1 && $mp_product->stock >= $quantity)
                )) {
                info('Order resend id: ' . $out_stock_order->id);
                send_order_drm_to_internel($out_stock_order->id);
            }
        }
        info('Stock out product order resend end');

    }
}

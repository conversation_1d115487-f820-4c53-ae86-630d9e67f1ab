<?php

namespace App\Console\Commands\Marketplace;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Jobs\Marketplace\DeleteSixtyDaysConsistentlyMarketplaceSalesInfoDelete;

class DeleteSixtyDaysConsistentlyMarketplaceSalesInfo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:remove-sixty-days-marketplace-sales-info-delete';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace sixty days consistently marketplace sales info delete';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $sixty_days_ago = now()->subDays(65)->toDateString();
        $sales_information = DB::connection('marketplace')
        ->table('marketplace_product_sales_information')
        ->where('created_at', '<', $sixty_days_ago)
        ->get();

        foreach ($sales_information->chunk(1000) as $sales_info) {
            $ids = $sales_info->pluck('id')->toArray();
            dispatch(new DeleteSixtyDaysConsistentlyMarketplaceSalesInfoDelete($ids));
        }
    }
}

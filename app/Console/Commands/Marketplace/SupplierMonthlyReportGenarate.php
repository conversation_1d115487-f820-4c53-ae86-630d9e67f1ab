<?php

namespace App\Console\Commands\Marketplace;

use Illuminate\Console\Command;

class SupplierMonthlyReportGenarate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:supplier-monthly-report-genarate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace supplier monthly report genarate end of the month';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try{
           app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->createSupplierMonthlyReport();
        }catch(\Exception $e){}
    }
}

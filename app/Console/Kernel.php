<?php

namespace App\Console;

use App\Console\Commands\FetchDecathlonOffers;
use App\Console\Commands\Fixes\FixApiStocks;
use App\Console\Commands\Marketplace\EladyStockOutProductDelete;
use App\Console\Commands\SendColizeyFeed;
use App\Console\Commands\SendStepMails;
use App\Console\Commands\ShopValidation;
// use App\Console\Commands\EbayRefreshUserToken;

use App\Console\Commands\ShopOrderSync;
use App\Console\Commands\ShopOrderSyncBronze;
use App\Console\Commands\ShopOrderSyncSilver;
use App\Console\Commands\ShopOrderSyncGold;
use App\Console\Commands\ShopOrderSyncPlatinum;


// use App\Console\Commands\ImportSync\ImportSyncBronze;
// use App\Console\Commands\ImportSync\ImportSyncSilver;
// use App\Console\Commands\ImportSync\ImportSyncGold;
// use App\Console\Commands\ImportSync\ImportSyncPlatinum;

use App\Console\Commands\KlickTippCustomerSync;
use App\Console\Commands\UpdateMiraklProducts;
use App\Jobs\ChannelManager\ProcessMiraklExportReport;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Log;

use App\Console\Commands\SendOverdueNotification; //Overdue notification
use App\Console\Commands\ProformaInvoiceRestore; //Proforma restore

use App\Console\Commands\MonthlyPaywallCommand; //Monthly Paywall

use App\Console\Commands\DRMManualSubscriptionCommand; //DRM Manual subscription
use App\Console\Commands\HandlingTimeDecrement; // Handling Time Decrement
use App\Console\Commands\AppStoreTagInsertToCustomer; // App store Tag insert to customer

use App\Console\Commands\InactiveUserActionCommand; // Inactive user command

use App\Traits\CronApiReport;

use App\Console\Commands\Marketplace\MarkrtplaceTwelveHourCommand; // Marketplace 12 hours
use App\Console\Commands\Marketplace\MarkrtplaceThreeDaysCommand; // Marketplace 3 days
use App\Console\Commands\Marketplace\SupplierMonthlyReportGenarate; // Marketplace every month
use App\Console\Commands\Marketplace\MpOrdersTurnoverCredit; // Marketplace monthly order turnover credit
use App\Console\Commands\Marketplace\RemoveMpCategoryDiscount;
use App\Console\Commands\Marketplace\RemoveMarketplaceSponsorsCampaign;
use App\Console\Commands\Marketplace\RemoveMarketplaceProductDiscount;
use App\Console\Commands\Marketplace\RemoveMarketplaceZeroStockProducts; // Marketplace zero stock product delete
use App\Console\Commands\Marketplace\DeleteSixtyDaysConsistentlyZeroStockProducts; // Marketplace sixty day zero stock product delete
use App\Console\Commands\Marketplace\BestDuplicateEanProductActive;
use App\Console\Commands\Marketplace\ResendOutStockOrderAutomatically;

use App\Console\Commands\AppointmentRenewal; // Appoinment renewal every month

// use App\Console\Commands\ShopVersionUpdate; // Shop version update
use App\Console\Commands\NotificationClear;
use App\Console\Commands\StoreDRMStockHistory;
use App\Console\Commands\WohlaufShippmentCommand; // Shippment
use App\Console\Commands\Marketplace\MarketplaceProductSyncToCore;
use App\Console\Commands\Marketplace\FixCoreChannel;
use App\Console\Commands\Marketplace\FulfilmentProductAutoConfirmShipment;
use App\Jobs\ProcessSalesEstimationCall;

use App\Console\Commands\Fixes\FixCategoryFullPaths;
use App\Console\Commands\Fixes\FixCategoryCounts;
use App\Console\Commands\Marketplace\MarketplacePreOrderSync;
use App\Console\Commands\SyncEasyPrice;

use App\Console\Commands\Marketplace\MarketplaceStoreProductCount;
use App\Console\Commands\RetryProformaMissedMail;

class Kernel extends ConsoleKernel
{
    use CronApiReport;

    protected $commands = [
        ShopOrderSync::class,
        ShopOrderSyncBronze::class,
        ShopOrderSyncSilver::class,
        ShopOrderSyncGold::class,
        ShopOrderSyncPlatinum::class,

        // ImportSyncBronze::class,
        // ImportSyncSilver::class,
        // ImportSyncGold::class,
        // ImportSyncPlatinum::class,

        ShopValidation::class,
        // EbayRefreshUserToken::class,
        KlickTippCustomerSync::class,

        SendOverdueNotification::class, // Overdue notification
        ProformaInvoiceRestore::class,  //Proforma restore
        RetryProformaMissedMail::class, //Retry mail

        MonthlyPaywallCommand::class, //Monthly Paywall

        DRMManualSubscriptionCommand::class, //DRM Manual subscription

        HandlingTimeDecrement::class, //DRM Handling time decrement
        AppStoreTagInsertToCustomer::class, // App store Tag insert to customer
        SendStepMails::class, // Sends Step Mails

        InactiveUserActionCommand::class, //Inactive user command

        MarkrtplaceTwelveHourCommand::class, // Marketplace 12 hours
        MarkrtplaceThreeDaysCommand::class, // Marketplace 3 days
        SupplierMonthlyReportGenarate::class, // Marketplace every month
        MarketplacePreOrderSync::class, // Marketplace pre order sync
        EladyStockOutProductDelete::class, // MP eLady supplier stock out product delete
        MpOrdersTurnoverCredit::class, // Marketplace monthly order turnover credit
        RemoveMpCategoryDiscount::class,
        RemoveMarketplaceProductDiscount::class,
        RemoveMarketplaceSponsorsCampaign::class,
        RemoveMarketplaceZeroStockProducts::class, // Marketplace zero stock product delete
        DeleteSixtyDaysConsistentlyZeroStockProducts::class, // Marketplace sixty day zero stock product delete
        BestDuplicateEanProductActive::class, // Marketplace duplicate EAN products active
        ResendOutStockOrderAutomatically::class, // Stock out Order resend

        AppointmentRenewal::class, // Appoinment renewal every month
        NotificationClear::class, // Clear notification weekly
        StoreDRMStockHistory::class, // Store stock history daily

        //ShopVersionUpdate::class, // Shop version update

        WohlaufShippmentCommand::class, //Wohlauf shipment labels
        MarketplaceProductSyncToCore::class,
        FulfilmentProductAutoConfirmShipment::class,
        FixCategoryFullPaths::class,
        FixCategoryCounts::class,
        SyncEasyPrice::class,
        FetchDecathlonOffers::class,
        FixCoreChannel::class,
        FixApiStocks::class,
        MarketplaceStoreProductCount::class,
        SendColizeyFeed::class,
        UpdateMiraklProducts::class
    ];

    protected function schedule(Schedule $schedule)
    {
        //New Shop Order Sync Job
        if (!app()->environment('development')) {
            // //Normal 24 hour sync
            $schedule->command('shop:order')
            ->cron('0 */24 * * *')
            ->runInBackground()
            // ->onSuccess(function () {
            //     Log::channel('command')->info('Success');
            //  })
            // ->before(function () {
            //     Log::channel('command')->info('Start');
            //  })
            // ->after(function () {
            //     Log::channel('command')->info('finished');
            // })
            // ->onFailure(function () {
            //     Log::channel('command')->info('failed');
            // })
            ->withoutOverlapping();

            // //Bronze 12 hour
            $schedule->command('shop:order-bronze')
            ->cron('0 */12 * * *')
            ->runInBackground()
            // ->onSuccess(function () {
            //     Log::channel('command')->info('Success');
            //  })
            // ->before(function () {
            //     Log::channel('command')->info('Start');
            //  })
            // ->after(function () {
            //     Log::channel('command')->info('finished');
            // })
            // ->onFailure(function () {
            //     Log::channel('command')->info('failed');
            // })
            ->withoutOverlapping();

            // //Silver 6 hour
            $schedule->command('shop:order-silver')
            ->cron('0 */6 * * *')
            ->runInBackground()
            ->onSuccess(function () {
                $this->sendReportTo('Order API', 'order_api_6', 'success');
             })
            ->before(function () {
                $this->sendReportTo('Order API', 'order_api_6', 'start');
             })
            ->after(function () {
                $this->sendReportTo('Order API', 'order_api_6', 'finish');
            })
            ->onFailure(function () {
                $this->sendReportTo('Order API', 'order_api_6', 'failed');
            })->withoutOverlapping();

            // //Gold 3 hour
            $schedule->command('shop:order-gold')
            ->cron('0 */3 * * *')
            ->runInBackground()
            ->onSuccess(function () {
                $this->sendReportTo('Order API', 'order_api_3', 'success');
             })
            ->before(function () {
                $this->sendReportTo('Order API', 'order_api_3', 'start');
             })
            ->after(function () {
                $this->sendReportTo('Order API', 'order_api_3', 'finish');
            })
            ->onFailure(function () {
                $this->sendReportTo('Order API', 'order_api_3', 'failed');
            })->withoutOverlapping();

            //Platinum 30 min
            $schedule->command('shop:order-platinum')
            ->everyThirtyMinutes()
            ->runInBackground()
            ->onSuccess(function () {
                $this->sendReportTo('Order API', 'order_api_30', 'success');
             })
            ->before(function () {
                $this->sendReportTo('Order API', 'order_api_30', 'start');
             })
            ->after(function () {
                $this->sendReportTo('Order API', 'order_api_30', 'finish');
            })
            ->onFailure(function () {
                $this->sendReportTo('Order API', 'order_api_30', 'failed');
            })
            ->withoutOverlapping();




            // $schedule->command('import:sync')
            // ->cron('0 */24 * * *')
            // ->runInBackground()
            // ->onSuccess(function () {
            //     Log::channel('command')->info('Success');
            //  })
            // ->before(function () {
            //     Log::channel('command')->info('Start');
            //  })
            // ->after(function () {
            //     Log::channel('command')->info('finished');
            // })
            // ->onFailure(function () {
            //     Log::channel('command')->info('failed');
            // })->withoutOverlapping();

            // // //Bronze 12 hour
            // $schedule->command('import:sync-bronze')
            // ->cron('0 */12 * * *')
            // ->runInBackground()
            // ->onSuccess(function () {
            //     Log::channel('command')->info('Success');
            //  })
            // ->before(function () {
            //     Log::channel('command')->info('Start');
            //  })
            // ->after(function () {
            //     Log::channel('command')->info('finished');
            // })
            // ->onFailure(function () {
            //     Log::channel('command')->info('failed');
            // })->withoutOverlapping();

            // // //Silver 6 hour
            // $schedule->command('import:sync-silver')
            // ->cron('0 */6 * * *')
            // ->runInBackground()
            // ->onSuccess(function () {
            //     Log::channel('command')->info('Success');
            //  })
            // ->before(function () {
            //     Log::channel('command')->info('Start');
            //  })
            // ->after(function () {
            //     Log::channel('command')->info('finished');
            // })
            // ->onFailure(function () {
            //     Log::channel('command')->info('failed');
            // })->withoutOverlapping();

            // // //Gold 3 hour
            // $schedule->command('import:sync-gold')
            // ->cron('0 */3 * * *')
            // ->runInBackground()
            // ->onSuccess(function () {
            //     Log::channel('command')->info('Success');
            //  })
            // ->before(function () {
            //     Log::channel('command')->info('Start');
            //  })
            // ->after(function () {
            //     Log::channel('command')->info('finished');
            // })
            // ->onFailure(function () {
            //     Log::channel('command')->info('failed');
            // })->withoutOverlapping();

            // //Platinum 30 min
            // $schedule->command('import:sync-platinum')
            // ->everyThirtyMinutes()
            // ->runInBackground()
            // ->onSuccess(function () {
            //     Log::channel('command')->info('Success');
            //     $this->sendReportTo('CSV Sync', 'csv_sync', 'success');
            //  })
            // ->before(function () {
            //     Log::channel('command')->info('Start');
            //     $this->sendReportTo('CSV Sync', 'csv_sync', 'start');
            //  })
            // ->after(function () {
            //     Log::channel('command')->info('finished');
            //     $this->sendReportTo('CSV Sync', 'csv_sync', 'finish');
            // })
            // ->onFailure(function () {
            //     Log::channel('command')->info('failed');
            //     $this->sendReportTo('CSV Sync', 'csv_sync', 'failed');
            // })->withoutOverlapping();


        }

        //KlickTipp Subsrciber Sync Scheduler
        $schedule->command('sync:customer')->daily()->runInBackground()
        // ->onSuccess(function () {
        //     Log::channel('command')->info('Success');
        // })
        // ->before(function () {
        //     Log::channel('command')->info('Start');
        // })
        // ->after(function () {
        //     Log::channel('command')->info('finished');
        // })
        // ->onFailure(function () {
        //     Log::channel('command')->info('failed');
        // })
        ->withoutOverlapping();

        $schedule->command('ebay:refreshAccessToken')->hourly();

        //Overdue invoice
        $schedule->command('overdue:notify')->daily()->runInBackground()
        // ->onSuccess(function () {
        //     Log::channel('command')->info('Success');
        // })
        // ->before(function () {
        //     Log::channel('command')->info('Start');
        // })
        // ->after(function () {
        //     Log::channel('command')->info('finished');
        // })
        // ->onFailure(function () {
        //     Log::channel('command')->info('failed');
        // })
        ->withoutOverlapping();


        //Proforma restore
        $schedule->command('proforma:restore')->daily()->runInBackground()
        // ->onSuccess(function () {
        //     Log::channel('command')->info('Success');
        // })
        // ->before(function () {
        //     Log::channel('command')->info('Start');
        // })
        // ->after(function () {
        //     Log::channel('command')->info('finished');
        // })
        // ->onFailure(function () {
        //     Log::channel('command')->info('failed');
        // })
        ->withoutOverlapping();

        //Retry failed mail
        $schedule->command('proforma:retry-mail')->daily()->runInBackground()->withoutOverlapping();



        //Monthly Paywall
        $schedule->command('paywall:notify')->daily()->runInBackground()
        // ->onSuccess(function () {
        //     Log::channel('command')->info('Monthly Paywall - Success');
        // })
        // ->before(function () {
        //     Log::channel('command')->info('Monthly Paywall - Start');
        // })
        // ->after(function () {
        //     Log::channel('command')->info('Monthly Paywall - finished');
        // })
        // ->onFailure(function () {
        //     Log::channel('command')->info('Monthly Paywall - failed');
        // })
        ->withoutOverlapping();

        //DRM Manual Subscription
        $schedule->command('drm_manual:subscription')->daily()->runInBackground()
        // ->onSuccess(function () {
        //     Log::channel('command')->info('DRM Manual subscription - Success');
        // })
        // ->before(function () {
        //     Log::channel('command')->info('DRM Manual subscription - Start');
        // })
        // ->after(function () {
        //     Log::channel('command')->info('DRM Manual subscription - finished');
        // })
        // ->onFailure(function () {
        //     Log::channel('command')->info('DRM Manual subscription - failed');
        // })
        ->withoutOverlapping();

        //DRM Order Handling time decrement
        $schedule->command('handlingTime:decrement')->daily()->runInBackground()
        // ->onSuccess(function () {
        //     Log::channel('command')->info('DRM Order Handling Time Decrement - Success');
        // })
        // ->before(function () {
        //     Log::channel('command')->info('DRM Order Handling Time Decrement - Start');
        // })
        // ->after(function () {
        //     Log::channel('command')->info('DRM Order Handling Time Decrement - finished');
        // })
        // ->onFailure(function () {
        //     Log::channel('command')->info('DRM Order Handling Time Decrement - failed');
        // })
        ->withoutOverlapping();

        // App store Tag insert to customer
        $schedule->command('appStoreTag:InsertToCustomer')->daily()->runInBackground()
        // ->onSuccess(function () {
        //     Log::channel('command')->info('DRM Order App Store Tag Insert - Success');
        // })
        // ->before(function () {
        //     Log::channel('command')->info('DRM Order App Store Tag Insert - Start');
        // })
        // ->after(function () {
        //     Log::channel('command')->info('DRM Order App Store Tag Insert - finished');
        // })
        // ->onFailure(function () {
        //     Log::channel('command')->info('DRM Order App Store Tag Insert - failed');
        // })
        ->withoutOverlapping();

        // send step mail for email campaign
        $schedule->command('send:stepMail')
            ->everyThreeMinutes()
            ->runInBackground()
            // ->onSuccess(function () {
            //     Log::channel('command')->info('DRM Dropfunnel step mail send - Success');
            // })
            // ->before(function () {
            //     Log::channel('command')->info('DRM Dropfunnel step mail send - Start');
            // })
            // ->after(function () {
            //     Log::channel('command')->info('DRM Dropfunnel step mail send - finished');
            // })
            // ->onFailure(function () {
            //     Log::channel('command')->info('DRM Dropfunnel step mail send - failed');
            // })
            ->withoutOverlapping();

        //inactive user action
        $schedule->command('inactive-user:action')->daily()->runInBackground()
        ->onSuccess(function () {
            Log::channel('command')->info('Inactive user action Success');
        })
        ->before(function () {
            Log::channel('command')->info('Inactive user action Start');
        })
        ->after(function () {
            Log::channel('command')->info('Inactive user action finished');
        })
        ->onFailure(function () {
            Log::channel('command')->info('Inactive user action failed');
        })
        ->withoutOverlapping();

        // $schedule->call('App\Http\Controllers\AdminCommonController@getDrmUpdateAll')->everyMinute();

        /*Internel Api not exists*/
        //marketplace 12 hour - hourly
        // $schedule->command('marketplace:twelvehour')
        // ->hourly()
        // ->runInBackground()
        // ->onSuccess(function () {
        //     Log::channel('command')->info('Marketplace 12 hours Success');
        //  })
        // ->before(function () {
        //     Log::channel('command')->info('Marketplace 12 hours Start');
        //  })
        // ->after(function () {
        //     Log::channel('command')->info('Marketplace 12 hours finished');
        // })
        // ->onFailure(function () {
        //     Log::channel('command')->info('Marketplace 12 hours failed');
        // })->withoutOverlapping();

        //marketplace 3 days
        // $schedule->command('marketplace:threedays')
        // ->cron('0 0 */3 * *')
        // ->runInBackground()
        // ->onSuccess(function () {
        //     Log::channel('command')->info('Marketplace 3 days Success');
        //  })
        // ->before(function () {
        //     Log::channel('command')->info('Marketplace 3 days Start');
        //  })
        // ->after(function () {
        //     Log::channel('command')->info('Marketplace 3 days finished');
        // })
        // ->onFailure(function () {
        //     Log::channel('command')->info('Marketplace 3 days failed');
        // })->withoutOverlapping();

        //Appointment renewal
        $schedule->command('appointment:renewal')
        ->monthly()
        ->runInBackground()
        ->onFailure(function () {
            Log::channel('command')->info('Appointment renewal failed');
        })->withoutOverlapping();


        //Appointment renewal: 1 minute
        $schedule->command('appointment:remainder')
        ->everyFiveMinutes()
        ->runInBackground()
        ->onFailure(function () {
            Log::channel('command')->info('Appointment remainder failed');
        })->withoutOverlapping();

        // Channel cheapest price calculate: 1 minute
        $schedule->command('channel:calculate-cheapest-price')
            ->everyThirtyMinutes()
            ->runInBackground()
            ->onFailure(function () {
                // Log::channel('command')->info('Cheapest price calculation scheduler failed.');
            })
            ->withoutOverlapping();

        // mp top sell products turnover amount to show in user's special widget
        $schedule->command('widget:mp-top-sell-products-turnover')
            ->daily()
            ->runInBackground()
            ->onFailure(function () {
                // Log::channel('command')->info('Mp top sell products turnover calculation failed.');
            })
            ->withoutOverlapping();

        // For Main Campaign Start by Waiting Time: 5 minutes
        $schedule->command('start:campaign')
        ->everyThirtyMinutes()
        ->onFailure(function () {
            Log::channel('command')->info('Main Campaign Start failed.');
        })
        ->withoutOverlapping()
        ->runInBackground();

        $schedule->command('user:trial_end_check')
        ->daily()
        ->runInBackground()
        ->onFailure(function () {
            Log::channel('command')->info('Users DT Trial Plan Checking failed');
        })->withoutOverlapping();


        $schedule->command('notification:clear')->weekly();
        $schedule->command('store:stock-history')->daily();
        // Marketplace Pre Order sync
        $schedule->command('marketplace:pre-order-sync')->daily();



        //Shop version update
        // $schedule->command('shop:version-update')->weekly()
        // ->runInBackground()
        // ->onSuccess(function () {
        //     Log::channel('command')->info('Shop version update - Success');
        // })
        // ->before(function () {
        //     Log::channel('command')->info('Shop version update - Start');
        // })
        // ->after(function () {
        //     Log::channel('command')->info('Shop version update - finished');
        // })
        // ->onFailure(function () {
        //     Log::channel('command')->info('Shop version update - failed');
        // })->withoutOverlapping();

        $schedule->command('wohlauf:shippments')
        ->cron('0 */3 * * *')
        ->runInBackground()
        ->withoutOverlapping();

        $schedule->command('keepa:fetchAvgKeepaCategories', [0])->monthlyOn(14)->runInBackground()->withoutOverlapping();        #for regular/avg 0
        $schedule->command('keepa:fetchAvgKeepaCategories', [30])->monthlyOn(26)->runInBackground()->withoutOverlapping();       #for regular/avg 30
        $schedule->command('keepa:fetchKeepaProducts')->twiceMonthly(15, 27)->runInBackground()->withoutOverlapping();
        // $schedule->command('keepa:generateTrendReports')->monthlyOn(28)->runInBackground()->withoutOverlapping();                #report depends on 30 days avg
        $schedule->command('keepa:trendImport')->monthlyOn(28)->runInBackground()->withoutOverlapping();



        $schedule->command('keepa:import_feed_automagic')->daily()->runInBackground()->withoutOverlapping();



        // $schedule->call(new ProcessSalesEstimationCall)->weeklyOn(6, '8:00');
        // $schedule->command('keepa:make_sales_estimation_calls')->weeklyOn(6, '8:00');
        $schedule->command('customer:auto_delete')->daily()->runInBackground()->withoutOverlapping();
        // $schedule->command('mp_fulfilment_auto:confirm_shipment')->daily();

        $schedule->command('core-channel:fix')->hourly()->runInBackground()->withoutOverlapping();

        $schedule->command('channel-category:fix')->everyThreeHours()->runInBackground()->withoutOverlapping();
        // $schedule->command('channel-category:count-fix')->everySixHours()->runInBackground()->withoutOverlapping();
        // $schedule->command('keepa:send_product_stock_shortage_email')->hourly()->runInBackground()->withoutOverlapping(); REMOVED BY SAJIB FOR NOT NEED CONFIRM BY RAKIB REASON EMAIL

        $schedule->command('easy-price:fix')->hourly()->runInBackground()->withoutOverlapping();
        $schedule->command('keepa:delete_archived_analysis_products')->daily()->runInBackground()->withoutOverlapping();
        $schedule->command('keepa:update_keepa_csv_products_table_mp_available_column')->dailyAt('13:00')->runInBackground()->withoutOverlapping();
        $schedule->command('keepa:update_sales_estimate_columns')->dailyAt('14:00')->runInBackground()->withoutOverlapping();

        $schedule->command('marketplace:supplier-monthly-report-genarate')->monthlyOn(1, '05:00')->runInBackground()->withoutOverlapping();
        $schedule->command('marketplace:remove-caregory-discount')->daily()->runInBackground()->withoutOverlapping();
        $schedule->command('marketplace:remove-marketplace-product-discount')->daily()->runInBackground()->withoutOverlapping();
        $schedule->command('marketplace:remove-marketplace-sponsors-campaign')->daily()->runInBackground()->withoutOverlapping();
        $schedule->command('marketplace:monthly-orders-turnover')->monthly()->runInBackground()->withoutOverlapping();
        $schedule->command('marketplace:remove-marketplace-zero-stock-product')->cron('0 0 */10 * *')->runInBackground()->withoutOverlapping();
        $schedule->command('marketplace:remove-sixty-days-zero-stock-product')->daily()->runInBackground()->withoutOverlapping();
        $schedule->command('resendOrder:resend-out-stock-order-automatically')->everySixHours()->runInBackground()->withoutOverlapping();
        // $schedule->command('marketplace:remove-sixty-days-marketplace-sales-info-delete')->daily()->runInBackground()->withoutOverlapping();
        // $schedule->command('elady:stockoutproductdelete')->daily()->runInBackground()->withoutOverlapping();
        $schedule->command('marketplace:best-price-duplicate-ean-product-active')->everySixHours()->runInBackground()->withoutOverlapping();

        $schedule->command('keepa:update_mp_avg_rating_column')->hourly()->runInBackground()->withoutOverlapping();

        $schedule->command('api-stock:fix')->everyThreeHours()->runInBackground()->withoutOverlapping();

        $schedule->command('keepa:refresh_comparison_analysis_products')->dailyAt('11:30')->runInBackground()->withoutOverlapping();

        $schedule->command('keepa:update_google_merchant_reports')->dailyAt('13:30')->runInBackground()->withoutOverlapping();

        // $schedule->command('marketplace:zero-shipping-product-update')->everyThirtyMinutes()->runInBackground()->withoutOverlapping();


        // $schedule->command('sell_tag:Add')
        // ->timezone('Asia/Dhaka') // Adjusted for a +6 timezone
        // ->dailyAt('13:20')
        // ->runInBackground()
        // ->withoutOverlapping();

        // For Tariff purchase, inactivated channel will delete after 24 hours
        $schedule->command('inactive-channel:delete')
        ->daily()
        // ->everyFiveMinutes()
        // ->onSuccess(function () {
        //     Log::channel('command')->info('Inactive channel deletion Success');
        //  })
        // ->before(function () {
        //     Log::channel('command')->info('Inactive channel deletion Start');
        //  })
        // ->after(function () {
        //     Log::channel('command')->info('Inactive channel deletion finished');
        // })
        ->onFailure(function () {
            Log::channel('command')->info('Inactive channel deletion failed');
        })
        ->withoutOverlapping()
        ->runInBackground();

        // $schedule->command('marketplace:storeproductscount')->everyThreeMinutes()->runInBackground()->withoutOverlapping();

        // Hourly
        // $schedule->command('marketplace:storeproductscount')->everyThirtyMinutes()->runInBackground()->withoutOverlapping();

        //User Account Lock
        $schedule->command('user:account-lock')
        ->dailyAt('04:00')
        ->runInBackground()
        // ->onSuccess(function () {
        //     Log::channel('command')->info('User Account Lock - Success');
        // })
        // ->before(function () {
        //     Log::channel('command')->info('User Account Lock - Start');
        // })
        // ->after(function () {
        //     Log::channel('command')->info('User Account Lock - finished');
        // })
        ->onFailure(function () {
            Log::channel('command')->info('User Account Lock - failed');
        })
        ->withoutOverlapping();

        // after 14 days trial end, actions for after trial users
        $schedule->command('users:trial-end-actions')
            ->dailyAt('05:00')
            ->runInBackground()
            ->onFailure(function () {
                Log::channel('command')->info('Trial end actions for users failed.');
            })
            ->withoutOverlapping();

        $schedule->command('process:handle')
            ->everyFiveMinutes()
            ->runInBackground()
            ->withoutOverlapping();

        // Long term credit reset every month
        $schedule->command('credit:long-term-reset')
        ->daily()
        ->withoutOverlapping()
        ->runInBackground()
        ->onFailure(function () {
            Log::channel('command')->info('Long term credit reset failed.');
        });

        $schedule->command('colizey:update')
            ->everyThreeHours()
            ->withoutOverlapping()
            ->runInBackground();

        $schedule->command('galaxus:update')
            ->everyThreeHours()
            ->withoutOverlapping()
            ->runInBackground();


        $schedule->command('mirakl:update')
            ->everyThreeHours()
            ->withoutOverlapping()
            ->runInBackground();


        // For mp transferred products total calculation to drm
        // $schedule->command('marketplace:drm-transferred-calculation')
        // ->daily()
        // ->withoutOverlapping()
        // ->runInBackground()
        // ->onFailure(function () {
        //     Log::channel('command')->info('Mp transferred product calculation failed.');
        // });
    }

    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}

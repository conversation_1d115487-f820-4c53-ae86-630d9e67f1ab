<?php

namespace App\Models\Marketplace;

use Illuminate\Database\Eloquent\Model;
use App\Models\Product;

class ProductSyncedHistory extends Model
{
    protected $fillable = ['user_id', 'product_ids', 'download_count', 'synced_time'];

    protected $casts = [
        'product_ids' => 'array',
    ];

    public function products ()
    {
        return \App\Models\Marketplace\Product::find($this->product_ids);
    }


}

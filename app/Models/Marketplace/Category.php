<?php

namespace App\Models\Marketplace;

use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    protected $connection = 'marketplace';
    protected $table = 'marketplace_categories';

    protected $fillable = [
        'name',
        'parent_id',
        'slug',
        'price',
        'is_active',
        'is_top_category',
        'photo',
    ];

    protected $appends = [
//        'productCount'
    ];

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id');
    }

    public function parent()
    {
        return $this->belongsTo(MarketplaceParentCategory::class, 'parent_id', 'id');
    }

    public function childrenProducts()
    {
        return $this->hasManyThrough(Product::class, Category::class, 'parent_id');
    }

    public function getProductCountAttribute()
    {
        return \App\Models\Marketplace\Product::where('status', \App\Enums\VisibilityStatus::ACTIVE)->count();

        return $this->products()->where('status', \App\Enums\VisibilityStatus::ACTIVE)->count() + $this->childrenProducts()->where('status', \App\Enums\VisibilityStatus::ACTIVE)->count();
    }

    public function getImageAttribute()
    {
        if($this->photo) {
            return $this->photo;
        }

        return 'Marketing_assets/img/category-default-thumbnail.jpg';
    }
}

<?php

namespace App\Models\Marketplace;

use Illuminate\Database\Eloquent\Model;

class UserAccess extends Model
{
    protected $guarded = [];

    protected $table = 'marketplace_user_accesses';
    protected $connection   = 'marketplace';

    protected $casts = [
        'accessable_parent_categories' => 'array',
        'accessable_categories' => 'array',
        'check_accessable_categories' => 'array',
    ];
}

<?php

namespace App\Models\Marketplace;

use App\DrmProduct;
use App\Enums\ProfitType;
use App\User;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

use App\Models\Marketplace\MpSendStockComment;
class Product extends Model
{
    protected $connection = 'marketplace';
    
    protected $table = 'marketplace_products';

    protected $guarded = [];
    // protected $fillable = [
    //     'name',
    //     'collection_id',
    //     'parent_id',
    //     'ean',
    //     'ek_price',
    //     'description',
    //     'category_id',
    //     'image',
    //     'item_color',
    //     'brand',
    //     'status',
    //     'profit_type',
    //     'profit_price',
    //     'misc',
    //     'delivery_company_id',
    //     'country_id',
    //     'language_id',
    //     'item_number',
    //     'purchase_price',
    //     'vat',
    //     'shipping_cost',
    //     'shipping_method',
    //     'stock',
    //     'item_weight',
    //     'item_size',
    //     'note',
    //     'production_year',
    //     'materials',
    //     'tags',
    //     'update_enabled',
    //     'gender',
    //     'deleted_at',
    //     'is_top_product',
    //     'update_status',
    //     'marketplace_product_id',
    //     'marketplace_api_response',
    //     'internel_delivery_id',
    //     'internel_stock',
    //     'internel_order_id',
    //     'supplier_id',
    //     'vk_price',
    //     'uvp',
    //     'accept_status',
    //     'category',
    //     'calculation_id',
    //     'delivery_days',
    // ];

    protected $casts = [
        'misc' => 'array',
        'image' => 'array',
        'category' => 'array',
    ];

    public function core_products()
    {
        return $this->setConnection('mysql')->hasMany(DrmProduct::class, 'marketplace_product_id','id');
    }
    
    public function collection()
    {
        return $this->belongsTo(Collection::class);
    }

    public function mainCategory()
    {
        return $this->belongsTo(Category::class,'category_id');
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }   

    public function supplier()
    {
        return $this->belongsTo(User::class, 'supplier_id');
    }

    public function parent()
    {
        return $this->belongsTo(Product::class, 'parent_id');
    }

    public function getProfitAmountAttribute()
    {
        if(is_null($this->collection->product_profit_type) || is_null($this->collection->product_profit_price) || empty($this->parent)) {
            return 0;
        }

        return $this->collection->product_profit_type == ProfitType::FIXED ? $this->collection->product_profit_price : ($this->parent->price * $this->collection->product_profit_price) / 100;
    }

    public function listing_prices()
    {
        return $this->morphToMany(ListingPrice::class, 'listable', 'marketplace_listables');
    }

    // public function getFirstImageAttribute()
    // {
    //     return $this->image;
    // }

    // public function setImagesAttribute($value)
    // {
    //     return json_encode($value, true) ?? [];
    // }
    public function getImageAttribute($value)
    {
        return json_decode($value, true) ?? [];
    }

    public function setImageAttribute($value)
    {
        $this->attributes['image'] = json_encode($value);
    }

    public function getImagesAttribute()
    {
        return $this->image;
        return Arr::get($this->image, '0');
    }

    public static function isInDrmProductList($product, $user_id)
    {
        return DrmProduct::where('marketplace_product_id', $product->id)->where('user_id', $user_id)->exists();
    }


    
    public static function boot()
    {
        parent::boot();

        static::saving (function ($model) {
            self::savingOrUpdating($model);
        });

        static::updating (function ($model) {
           self::savingOrUpdating($model);
        });

    }

    public static function savingOrUpdating ($model) {
        if ( $model->isDirty('stock') ) {
             $model->old_stock        = $model->getOriginal('stock');
             $model->stock_updated_at = \Carbon\Carbon::now();
        }

        if ( $model->isDirty('internel_stock') ) {
             $model->old_internel_stock          = $model->getOriginal('internel_stock');
             $model->internel_stock_updated_at   = \Carbon\Carbon::now();
        }
    }

    public static function isInternel($id)
    {
        return Product::find($id)->marketplace_product_id;
    }

    public function scopeSoldOut()
    {
        return $this->where('stock', 0);
    }

    public function scopeOnlyAvailable()
    {
        return $this->where('stock', '>', 0);
    }

    public function MpCoreDrmTransferProduct()
    {
        return $this->hasMany(MpCoreDrmTransferProduct::class,'marketplace_product_id','id');
    }

    public function productBrand()
    {
        return $this->hasOne(ProductBrand::class,'id','brand');
    }

    public function additionalInfo(){
        return $this->hasOne(AdditionalInfo::class,'product_id','id');
    }

    public function stockSendComment()
    {
        return $this->hasOne(MpSendStockComment::class, 'mp_product_id', 'id');
    }

    public function stockSendLog(){
        return $this->hasOne(FulfilmentStockSendLog::class, 'product_id');
    }
    
}

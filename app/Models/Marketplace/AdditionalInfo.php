<?php

namespace App\Models\Marketplace;

use Illuminate\Database\Eloquent\Model;

class AdditionalInfo extends Model
{
    protected $connection = 'marketplace';
    
    protected $table = 'mp_product_additional_info';

    protected $fillable = [
        'product_id',
        'manufacturer',
        'manufacturer_link',
        'manufacturer_id',
        'custom_tariff_number',
        'shipping_company_id',
        'region',
        'country_of_origin',
        'min_stock',
        'min_order',
        'gross_weight',
        'net_weight',
        'product_length',
        'product_width',
        'product_height',
        'volume',
        'packaging_length',
        'packaging_width',
        'packaging_height',
        'item_unit',
        'packing_unit',
        'volume_gross'
    ];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }
}
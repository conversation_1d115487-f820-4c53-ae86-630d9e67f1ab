<?php

namespace App\Models\Marketplace;

use App\User;
use Illuminate\Database\Eloquent\Model;

class MarketplaceSyncedOrder extends Model
{
    protected $table = 'marketplace_synced_orders';

    protected $fillable = [
        'supplier_id',
        'order_date',
        'order_id',
        'product_ids',
        'status_history',
        'status',
        'order_date',
        'order_infos',
        'history',
        'shipment_provider',
        'customer_info',
    ];

    protected $casts = [
        'status_history' => 'array',
        'product_ids'    => 'array',
        'order_infos'    => 'array',
        'history'        => 'array',
        'customer_info'  => 'array',
    ];

    public function supplier ()
    {
        return $this->belongsTo(User::class);
    }

    public static function products ($orderId)
    {
        return Product::whereIn('id', MarketplaceSyncedOrder::where('id', $orderId)->first()->product_ids)
               ->get();
    }
}

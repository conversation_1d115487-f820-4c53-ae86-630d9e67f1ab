<?php

namespace App\Models\Marketplace;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Validator;

class MarketplaceParentCategory extends Model
{
    protected $connection = 'marketplace';
    protected $fillable = ['id','name','is_active','discount_percentage','start_date','end_date','is_offer_active'];

    public function validator($request)
    {
        return Validator::make($request->all(),[
                'name'       => 'required|string|min:3|max:100|unique:marketplace.marketplace_parent_categories,name',
                'is_active'  => 'required',
        ]);
    }

    public function category(){
        return $this->hasMany(Category::class,'parent_id')->where('is_active',1)->orderBy('name');
    }
}

<?php

namespace App\Models\Tariff;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class TariffCredit extends Model
{
    const TYPE_TARIFF_CREDIT = 1;

    const TYPE_BONUS_CREDIT = 2;

    const TYPE_MARKETPLACE_BONUS_CREDIT = 3;

    const PRECISION_ADJUST_BY = 100;

    protected $table = 'tariff_credits';

    protected $fillable = ['user_id', 'type', 'total_credit', 'used_credit', 'renew_at'];

    public function creditLog(): HasOne
    {
        return $this->hasOne(TariffCreditLog::class, 'tariff_credit_id');
    }
    
    public function createLog(int $message_id, string $message = '', int $value): void
    {
        $this->creditLog()->create([
            'message_id' => $message_id,
            'message' => $message,
            'credit_value' => $value,
        ]);
    }
}

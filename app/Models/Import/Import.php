<?php

namespace App\Models\Import;

use App\DrmProduct;
use App\TmpDrmProduct;
use Illuminate\Database\Eloquent\Model;

class Import extends Model
{
    protected $table = 'drm_imports';
    const FOREIGN_KEY = 'drm_import_id';

    public function sync_report(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(SyncReport::class,self::FOREIGN_KEY);
    }
    public function filter(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(Filter::class,self::FOREIGN_KEY);
    }
    public function temp_products(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(TmpDrmProduct::class,self::FOREIGN_KEY);
    }
    public function products(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(DrmProduct::class,self::FOREIGN_KEY);
    }
}

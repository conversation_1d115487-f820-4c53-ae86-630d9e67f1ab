<?php

namespace App\Models\Import;

use Illuminate\Database\Eloquent\Model;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use App\Models\Marketplace\ApiCategory;

class ApiCategoryImport implements ToModel, WithHeadingRow
{
    protected $connection = 'marketplace';
    protected $table = 'api_category_mapping';
    
    public $mpApiId;
    public function __construct($api_id)
    {
        $this->mpApiId = $api_id;
    }

    public function model(array $row)
    {
        return new ApiCategory([
            'api_category_id'    => $row['api_category_id'],
            'api_category_name'  => $row['api_category_name'],
            'api_id'             => $this->mpApiId,
            'mp_category_id'     => $row['mp_category_id'], 
            'is_complete'        => $row['is_complete'] ? $row['is_complete'] : 0
        ]);
    }
}

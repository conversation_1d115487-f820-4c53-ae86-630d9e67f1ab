<?php

namespace App\Models\Product;

use Illuminate\Database\Eloquent\Model;
use App\Models\ChannelProduct;
use App\Models\Product\AnalysisProduct;

class GoogleMerchantReport extends Model
{

    protected $table = 'google_merchant_reports';

    protected $fillable = [
        'id',
        'user_id',
        'offer_id',
        'channel_product_id',
        'analysis_product_id',
        'ean',
        'title',
        'brand',
        'clicks',
        'impressions',
        'ctr',
        'created_at',
        'updated_at'
    ];

    public function channelProducts()
    {
        return $this->hasOne(ChannelProduct::class, 'id', 'channel_product_id');
    }

    public function analysisProducts()
    {
        return $this->hasOne(AnalysisProduct::class, 'id', 'analysis_product_id');
    }

    public function getTitleCalculationAttribute()
    {

        $title_original = $this->title;

        $title_len = strlen($title_original);
        $data = [];

        // title score
        $title_progress = $title_len * 100 / 80;
        $data['title_progress'] = $title_progress;

        if( $title_progress >= 70  && $title_progress <= 100){
            $data['color'] = '#16a085';
            $data['status'] = __('Perfect');
            $data['class'] = 'badge-success';
        }
        if( $title_progress < 70 ){
            $data['color'] = '#f1c40f';
            $data['status'] = __('Too Short');
            $data['class'] = 'badge-warning';
        }
        if( $title_progress > 100 ){
            $data['color'] = '#c0392b';
            $data['status'] = __('Too Long');
            $data['class'] = 'badge-danger';
        }

        if($title_progress > 100){
            $data['title_progress'] = 100;
        }

        return $data;

    }

}

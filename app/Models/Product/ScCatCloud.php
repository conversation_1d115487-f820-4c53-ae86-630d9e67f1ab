<?php

namespace App\Models\Product;
use Illuminate\Database\Eloquent\Model;

class ScCatCloud extends Model
{
	protected $connection = 'drm_scrap_cloud';
    protected $table = 'categories';
    protected $parentColumn = 'parent';

    public function bap()
    {
        return $this->belongsTo(ScCatCloud::class,$this->parentColumn);
    }

    public function baps()
    {
        return $this->bap()->with('baps');
    }

    public function children()
    {
        return $this->hasMany(ScCatCloud::class, $this->parentColumn);
    }

    public function allChildren()
    {
        return $this->children()->with('allChildren');
    }
}
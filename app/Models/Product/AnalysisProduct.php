<?php

namespace App\Models\Product;

use App\AnalysisCategory;
use Illuminate\Database\Eloquent\Model;
use App\DrmProduct;
use App\Models\ChannelProduct;
use App\ProductPriceApi;
use Illuminate\Database\Eloquent\SoftDeletes;

class AnalysisProduct extends Model
{
    use SoftDeletes;

    protected $table = 'analysis_products';

    protected $fillable = [
        'id',
        'title',
        'ean',
        'image',
        'product_id',

        'ebay_id',
        'amazon_id',
        'google_id',

        'ebay_product_number',
        'amazon_product_number',
        'google_product_number',

        'ebay_price',
        'amazon_price',
        'google_price',
        'ebay_rating',
        'amazon_rating',
        'google_rating',
        'ebay_rating_count',
        'amazon_rating_count',
        'google_rating_count',
        'ebay_weekly_sales_estimate',
        'amazon_weekly_sales_estimate',
        'google_weekly_sales_estimate',
        'ebay_monthly_sales_estimate',
        'amazon_monthly_sales_estimate',
        'google_monthly_sales_estimate',
        'ebay_product_sold',
        'ebay_last_sync',
        'amazon_last_sync',
        'amazon_sales_estimation_last_update',
        'google_last_sync',
        'price',
        'purchase_price',
        'uvp',
        'brand',
        'supplier',
        'item_number',
        'availability',
        'category',
        'category_id',
        'source',
        'archived',
        'archived_at',
        'ebay_request_id',
        'amazon_request_id',
        'google_request_id',
        'user_id',
        'synced_product',
        'check24_price',
        'kaufland_price',
        'is_prime',
        'amazon_seller_name',
        'amazon_seller_link',
        'amazon_other_sellers',
        'amazon_also_bought'
    ];

    protected $casts = [
        'image' => 'array'
    ];

    public function getImageAttribute($value)
    {
        return json_decode($value, true) ?? [];
    }

    public function product()
    {
        return $this->belongsTo(DrmProduct::class, 'product_id', 'id');
    }

    public function analysisCategory(){
        return $this->belongsTo(AnalysisCategory::class, 'category_id', 'id');
    }

    public function getBrandsAttribute()
    {
        return $this->pluck('brand')->toArray();
    }

    public function productPriceApi()
    {
        return $this->hasMany(ProductPriceApi::class, 'ean', 'ean');
    }

}

<?php

namespace App\Models\Export;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class UniversalExport extends Model
{
    protected $fillable = [
        'product_ids',
        'feed_name',
        'user_id',
        'feed_url',
        'fields',
        'lang',
        'dt_feed',
        'extension',
        'delimiter',
        'desc_html',
        'in_stock',
        'out_of_stock',
        'shop_url',
        'default',
        'image_limit',
        'max_handling_time',
        'handling_custom_label',
        'description_limit',
        'is_automatic_feed',
        'synced_at',
        'google_template',
        'shop_id'
    ];

    protected $casts = [
        'product_ids' => 'array',
        'fields'      => 'array'
    ];

    public function nextUpdate($interval = 1440)
    {
        $lastUpdate = $this->synced_at;
        return $this->getNextUpdate($lastUpdate,$interval);
    }

    private function getNextUpdate($lastUpdate,$interval)
    {
        $now = Carbon::now()->toDateTimeString();
        $timestamp = Carbon::createFromTimestamp(strtotime($lastUpdate))->addMinutes($interval);
        $nextUpdate = $timestamp->diffForHumans();
        $timeString = $timestamp->toDateTimeString();

        if (strtotime($now) > strtotime($timeString)) {
            $nextUpdate = $this->getNextUpdate($now,$lastUpdate);
        }

        return $nextUpdate;
    }
}

<?php

namespace App\Models\Export;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;

class RevokedProductExport implements FromCollection, WithHeadings
{

    public function collection(){
        ## 3. Conditional export and customize result
        $transferedProductStatusRevokedProducts = DB::connection('marketplace')->table('mp_core_drm_transfer_products')
            ->join('marketplace_products','mp_core_drm_transfer_products.marketplace_product_id','marketplace_products.id')
            ->join('delivery_companies','marketplace_products.delivery_company_id','delivery_companies.id')
            ->select('mp_core_drm_transfer_products.*','delivery_companies.name as delivery_companies_name','marketplace_products.status as marketplace_product_status','marketplace_products.ean','marketplace_products.name')
            ->where('marketplace_products.status','!=',1)->get();
        $result = array();
        foreach($transferedProductStatusRevokedProducts as $revokedProduct){
           $drmProduct = \App\DrmProduct::select('id','user_id')->with('user:id,name')->where('id',$revokedProduct->drm_product_id)->first();
           $result[] = array(
              'product_id'=>$revokedProduct->marketplace_product_id,
              'ean'=> $revokedProduct->ean,
              'title'=> $revokedProduct->name,
              'supplier_name'=> $revokedProduct->delivery_companies_name,
              'customer_name'=> $drmProduct->user ? $drmProduct->user->name : '',
           );
        }
        return collect($result);
    }
    
    public function headings(): array
    {
        return ["product_id","ean","title","supplier_name","customer_name"];
    }

}
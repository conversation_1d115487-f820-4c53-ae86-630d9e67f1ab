<?php

namespace App\Models\Export;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;

class MarketplaceProductsExport implements FromCollection, WithHeadings
{
    public $products;
    public $heder;
    public $is_drm;

    public function __construct($products, $heder, $is_drm)
    {
        $this->products = $products;
        $this->heder = $heder;
        $this->is_drm = $is_drm;
    }
    
    public function collection(){
        $result = array();
        $product_item = [];
        foreach($this->products as $product){
            $result[] = $product_item;
            $product = $this->is_drm ? (array)$product : $product->toArray();
            foreach($product as $key => $value){
                $product_item[$key] = $value;
            }
        }

        return collect($result);
    }
    
    public function headings(): array
    {
        return $this->heder;
    }

}
<?php

namespace App\Models\Export;

use Illuminate\Database\Eloquent\Model;
use App\Models\Marketplace\ApiCategory;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ApiCategoryExport implements FromCollection, WithHeadings
{
    public $mpApiId;
    public function __construct($api_id)
    {
        $this->mpApiId = $api_id;
    }

    public function collection()
    {
        return ApiCategory::where('api_id',$this->mpApiId)->get();
    }

    public function headings(): array
    {
        return ["id", "api_category_id", "api_category_name", "api_id", "mp_category_id", "is_complete"];
    }

}
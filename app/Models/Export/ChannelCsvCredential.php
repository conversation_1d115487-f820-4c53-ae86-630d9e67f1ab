<?php

namespace App\Models\Export;

use App\Shop;
use Illuminate\Database\Eloquent\Model;

class ChannelCsvCredential extends Model
{
    protected $table = 'channel_csv_credentials';
    protected $fillable = [
        'channel',
        'user_id',
        'token',
        'shop_id'
    ];

    public function shop(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Shop::class);
    }
}
